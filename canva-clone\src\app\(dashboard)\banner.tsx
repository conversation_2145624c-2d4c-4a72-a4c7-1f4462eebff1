"use client";

import { useState } from "react";
import { Sparkles, Plus } from "lucide-react";

import { Button } from "@/components/ui/button";
import { TemplateCategories } from "./template-categories";

export const Banner = () => {
  const [showTemplates, setShowTemplates] = useState(false);

  if (showTemplates) {
    return (
      <div className="space-y-6">
        <div className="text-white aspect-[5/1] min-h-[248px] flex gap-x-6 p-6 items-center rounded-xl bg-gradient-to-r from-[#2e62cb] via-[#0073ff] to-[#3faff5]">
          <div className="rounded-full size-28 items-center justify-center bg-white/50 hidden md:flex">
            <div className="rounded-full size-20 flex items-center justify-center bg-white">
              <Sparkles className="h-20 text-[#0073ff] fill-[#0073ff]" />
            </div>
          </div>
          <div className="flex flex-col gap-y-2">
            <h1 className="text-xl md:text-3xl font-semibold">Visualize your ideas with The Canvas</h1>
            <p className="text-xs md:text-sm mb-2">
              Choose the perfect template size for your design project.
            </p>
            <Button
              onClick={() => setShowTemplates(false)}
              variant="secondary"
              className="w-[160px]"
            >
              Back to templates
            </Button>
          </div>
        </div>
        <TemplateCategories />
      </div>
    );
  }

  return (
    <div className="text-white aspect-[5/1] min-h-[248px] flex gap-x-6 p-6 items-center rounded-xl bg-gradient-to-r from-[#2e62cb] via-[#0073ff] to-[#3faff5]">
      <div className="rounded-full size-28 items-center justify-center bg-white/50 hidden md:flex">
        <div className="rounded-full size-20 flex items-center justify-center bg-white">
          <Sparkles className="h-20 text-[#0073ff] fill-[#0073ff]" />
        </div>
      </div>
      <div className="flex flex-col gap-y-2">
        <h1 className="text-xl md:text-3xl font-semibold">Visualize your ideas with The Canvas</h1>
        <p className="text-xs md:text-sm mb-2">
          Turn inspiration into design in no time. Choose from professional templates or start from scratch.
        </p>
        <Button
          onClick={() => setShowTemplates(true)}
          variant="secondary"
          className="w-[160px]"
        >
          <Plus className="size-4 mr-2" />
          Start creating
        </Button>
      </div>
    </div>
  );
};
