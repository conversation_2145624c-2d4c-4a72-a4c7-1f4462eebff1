"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@auth";
exports.ids = ["vendor-chunks/@auth"];
exports.modules = {

/***/ "(rsc)/./node_modules/@auth/drizzle-adapter/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/@auth/drizzle-adapter/index.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DrizzleAdapter: () => (/* binding */ DrizzleAdapter)\n/* harmony export */ });\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/entity.js\");\n/* harmony import */ var drizzle_orm_mysql_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! drizzle-orm/mysql-core */ \"(rsc)/./node_modules/drizzle-orm/mysql-core/db.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/db.js\");\n/* harmony import */ var drizzle_orm_sqlite_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! drizzle-orm/sqlite-core */ \"(rsc)/./node_modules/drizzle-orm/sqlite-core/db.js\");\n/* harmony import */ var _lib_mysql_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/mysql.js */ \"(rsc)/./node_modules/@auth/drizzle-adapter/lib/mysql.js\");\n/* harmony import */ var _lib_pg_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lib/pg.js */ \"(rsc)/./node_modules/@auth/drizzle-adapter/lib/pg.js\");\n/* harmony import */ var _lib_sqlite_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/sqlite.js */ \"(rsc)/./node_modules/@auth/drizzle-adapter/lib/sqlite.js\");\n/**\n * <div style={{display: \"flex\", justifyContent: \"space-between\", alignItems: \"center\", padding: 16}}>\n *  <p>Official <a href=\"https://orm.drizzle.team\">Drizzle ORM</a> adapter for Auth.js / NextAuth.js.</p>\n *  <a href=\"https://orm.drizzle.team\">\n *   <img style={{display: \"block\"}} src=\"/img/adapters/drizzle.svg\" width=\"38\" />\n *  </a>\n * </div>\n *\n * ## Installation\n *\n * ```bash npm2yarn\n * npm install drizzle-orm @auth/drizzle-adapter\n * npm install drizzle-kit --save-dev\n * ```\n *\n * @module @auth/drizzle-adapter\n */\n\n\n\n\n\n\n\nfunction DrizzleAdapter(db, schema) {\n    if ((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_3__.is)(db, drizzle_orm_mysql_core__WEBPACK_IMPORTED_MODULE_4__.MySqlDatabase)) {\n        return (0,_lib_mysql_js__WEBPACK_IMPORTED_MODULE_0__.MySqlDrizzleAdapter)(db, schema);\n    }\n    else if ((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_3__.is)(db, drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.PgDatabase)) {\n        return (0,_lib_pg_js__WEBPACK_IMPORTED_MODULE_1__.PostgresDrizzleAdapter)(db, schema);\n    }\n    else if ((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_3__.is)(db, drizzle_orm_sqlite_core__WEBPACK_IMPORTED_MODULE_6__.BaseSQLiteDatabase)) {\n        return (0,_lib_sqlite_js__WEBPACK_IMPORTED_MODULE_2__.SQLiteDrizzleAdapter)(db, schema);\n    }\n    throw new Error(`Unsupported database type (${typeof db}) in Auth.js Drizzle adapter.`);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@auth/drizzle-adapter/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@auth/drizzle-adapter/lib/mysql.js":
/*!*********************************************************!*\
  !*** ./node_modules/@auth/drizzle-adapter/lib/mysql.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MySqlDrizzleAdapter: () => (/* binding */ MySqlDrizzleAdapter),\n/* harmony export */   defineTables: () => (/* binding */ defineTables)\n/* harmony export */ });\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/utils.js\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/conditions.js\");\n/* harmony import */ var drizzle_orm_mysql_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! drizzle-orm/mysql-core */ \"(rsc)/./node_modules/drizzle-orm/mysql-core/table.js\");\n/* harmony import */ var drizzle_orm_mysql_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! drizzle-orm/mysql-core */ \"(rsc)/./node_modules/drizzle-orm/mysql-core/columns/varchar.js\");\n/* harmony import */ var drizzle_orm_mysql_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! drizzle-orm/mysql-core */ \"(rsc)/./node_modules/drizzle-orm/mysql-core/columns/timestamp.js\");\n/* harmony import */ var drizzle_orm_mysql_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! drizzle-orm/mysql-core */ \"(rsc)/./node_modules/drizzle-orm/mysql-core/columns/int.js\");\n/* harmony import */ var drizzle_orm_mysql_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! drizzle-orm/mysql-core */ \"(rsc)/./node_modules/drizzle-orm/mysql-core/primary-keys.js\");\n/* harmony import */ var drizzle_orm_mysql_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! drizzle-orm/mysql-core */ \"(rsc)/./node_modules/drizzle-orm/mysql-core/columns/boolean.js\");\n\n\nfunction defineTables(schema = {}) {\n    const usersTable = schema.usersTable ??\n        ((0,drizzle_orm_mysql_core__WEBPACK_IMPORTED_MODULE_0__.mysqlTable)(\"user\", {\n            id: (0,drizzle_orm_mysql_core__WEBPACK_IMPORTED_MODULE_1__.varchar)(\"id\", { length: 255 })\n                .primaryKey()\n                .$defaultFn(() => crypto.randomUUID()),\n            name: (0,drizzle_orm_mysql_core__WEBPACK_IMPORTED_MODULE_1__.varchar)(\"name\", { length: 255 }),\n            email: (0,drizzle_orm_mysql_core__WEBPACK_IMPORTED_MODULE_1__.varchar)(\"email\", { length: 255 }).notNull(),\n            emailVerified: (0,drizzle_orm_mysql_core__WEBPACK_IMPORTED_MODULE_2__.timestamp)(\"emailVerified\", { mode: \"date\", fsp: 3 }),\n            image: (0,drizzle_orm_mysql_core__WEBPACK_IMPORTED_MODULE_1__.varchar)(\"image\", { length: 255 }),\n        }));\n    const accountsTable = schema.accountsTable ??\n        ((0,drizzle_orm_mysql_core__WEBPACK_IMPORTED_MODULE_0__.mysqlTable)(\"account\", {\n            userId: (0,drizzle_orm_mysql_core__WEBPACK_IMPORTED_MODULE_1__.varchar)(\"userId\", { length: 255 })\n                .notNull()\n                .references(() => usersTable.id, { onDelete: \"cascade\" }),\n            type: (0,drizzle_orm_mysql_core__WEBPACK_IMPORTED_MODULE_1__.varchar)(\"type\", { length: 255 })\n                .$type()\n                .notNull(),\n            provider: (0,drizzle_orm_mysql_core__WEBPACK_IMPORTED_MODULE_1__.varchar)(\"provider\", { length: 255 }).notNull(),\n            providerAccountId: (0,drizzle_orm_mysql_core__WEBPACK_IMPORTED_MODULE_1__.varchar)(\"providerAccountId\", {\n                length: 255,\n            }).notNull(),\n            refresh_token: (0,drizzle_orm_mysql_core__WEBPACK_IMPORTED_MODULE_1__.varchar)(\"refresh_token\", { length: 255 }),\n            access_token: (0,drizzle_orm_mysql_core__WEBPACK_IMPORTED_MODULE_1__.varchar)(\"access_token\", { length: 255 }),\n            expires_at: (0,drizzle_orm_mysql_core__WEBPACK_IMPORTED_MODULE_3__.int)(\"expires_at\"),\n            token_type: (0,drizzle_orm_mysql_core__WEBPACK_IMPORTED_MODULE_1__.varchar)(\"token_type\", { length: 255 }),\n            scope: (0,drizzle_orm_mysql_core__WEBPACK_IMPORTED_MODULE_1__.varchar)(\"scope\", { length: 255 }),\n            id_token: (0,drizzle_orm_mysql_core__WEBPACK_IMPORTED_MODULE_1__.varchar)(\"id_token\", { length: 2048 }),\n            session_state: (0,drizzle_orm_mysql_core__WEBPACK_IMPORTED_MODULE_1__.varchar)(\"session_state\", { length: 255 }),\n        }, (account) => ({\n            compositePk: (0,drizzle_orm_mysql_core__WEBPACK_IMPORTED_MODULE_4__.primaryKey)({\n                columns: [account.provider, account.providerAccountId],\n            }),\n        })));\n    const sessionsTable = schema.sessionsTable ??\n        ((0,drizzle_orm_mysql_core__WEBPACK_IMPORTED_MODULE_0__.mysqlTable)(\"session\", {\n            sessionToken: (0,drizzle_orm_mysql_core__WEBPACK_IMPORTED_MODULE_1__.varchar)(\"sessionToken\", { length: 255 }).primaryKey(),\n            userId: (0,drizzle_orm_mysql_core__WEBPACK_IMPORTED_MODULE_1__.varchar)(\"userId\", { length: 255 })\n                .notNull()\n                .references(() => usersTable.id, { onDelete: \"cascade\" }),\n            expires: (0,drizzle_orm_mysql_core__WEBPACK_IMPORTED_MODULE_2__.timestamp)(\"expires\", { mode: \"date\" }).notNull(),\n        }));\n    const verificationTokensTable = schema.verificationTokensTable ??\n        ((0,drizzle_orm_mysql_core__WEBPACK_IMPORTED_MODULE_0__.mysqlTable)(\"verificationToken\", {\n            identifier: (0,drizzle_orm_mysql_core__WEBPACK_IMPORTED_MODULE_1__.varchar)(\"identifier\", { length: 255 }).notNull(),\n            token: (0,drizzle_orm_mysql_core__WEBPACK_IMPORTED_MODULE_1__.varchar)(\"token\", { length: 255 }).notNull(),\n            expires: (0,drizzle_orm_mysql_core__WEBPACK_IMPORTED_MODULE_2__.timestamp)(\"expires\", { mode: \"date\" }).notNull(),\n        }, (verficationToken) => ({\n            compositePk: (0,drizzle_orm_mysql_core__WEBPACK_IMPORTED_MODULE_4__.primaryKey)({\n                columns: [verficationToken.identifier, verficationToken.token],\n            }),\n        })));\n    const authenticatorsTable = schema.authenticatorsTable ??\n        ((0,drizzle_orm_mysql_core__WEBPACK_IMPORTED_MODULE_0__.mysqlTable)(\"authenticator\", {\n            credentialID: (0,drizzle_orm_mysql_core__WEBPACK_IMPORTED_MODULE_1__.varchar)(\"credentialID\", { length: 255 })\n                .notNull()\n                .unique(),\n            userId: (0,drizzle_orm_mysql_core__WEBPACK_IMPORTED_MODULE_1__.varchar)(\"userId\", { length: 255 })\n                .notNull()\n                .references(() => usersTable.id, { onDelete: \"cascade\" }),\n            providerAccountId: (0,drizzle_orm_mysql_core__WEBPACK_IMPORTED_MODULE_1__.varchar)(\"providerAccountId\", {\n                length: 255,\n            }).notNull(),\n            credentialPublicKey: (0,drizzle_orm_mysql_core__WEBPACK_IMPORTED_MODULE_1__.varchar)(\"credentialPublicKey\", {\n                length: 255,\n            }).notNull(),\n            counter: (0,drizzle_orm_mysql_core__WEBPACK_IMPORTED_MODULE_3__.int)(\"counter\").notNull(),\n            credentialDeviceType: (0,drizzle_orm_mysql_core__WEBPACK_IMPORTED_MODULE_1__.varchar)(\"credentialDeviceType\", {\n                length: 255,\n            }).notNull(),\n            credentialBackedUp: (0,drizzle_orm_mysql_core__WEBPACK_IMPORTED_MODULE_5__.boolean)(\"credentialBackedUp\").notNull(),\n            transports: (0,drizzle_orm_mysql_core__WEBPACK_IMPORTED_MODULE_1__.varchar)(\"transports\", { length: 255 }),\n        }, (authenticator) => ({\n            compositePk: (0,drizzle_orm_mysql_core__WEBPACK_IMPORTED_MODULE_4__.primaryKey)({\n                columns: [authenticator.userId, authenticator.credentialID],\n            }),\n        })));\n    return {\n        usersTable,\n        accountsTable,\n        sessionsTable,\n        verificationTokensTable,\n        authenticatorsTable,\n    };\n}\nfunction MySqlDrizzleAdapter(client, schema) {\n    const { usersTable, accountsTable, sessionsTable, verificationTokensTable, authenticatorsTable, } = defineTables(schema);\n    return {\n        async createUser(data) {\n            const { id, ...insertData } = data;\n            const hasDefaultId = (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_6__.getTableColumns)(usersTable)[\"id\"][\"hasDefault\"];\n            await client\n                .insert(usersTable)\n                .values(hasDefaultId ? insertData : { ...insertData, id });\n            return client\n                .select()\n                .from(usersTable)\n                .where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(usersTable.email, data.email))\n                .then((res) => res[0]);\n        },\n        async getUser(userId) {\n            return client\n                .select()\n                .from(usersTable)\n                .where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(usersTable.id, userId))\n                .then((res) => (res.length > 0 ? res[0] : null));\n        },\n        async getUserByEmail(email) {\n            return client\n                .select()\n                .from(usersTable)\n                .where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(usersTable.email, email))\n                .then((res) => (res.length > 0 ? res[0] : null));\n        },\n        async createSession(data) {\n            await client.insert(sessionsTable).values(data);\n            return client\n                .select()\n                .from(sessionsTable)\n                .where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(sessionsTable.sessionToken, data.sessionToken))\n                .then((res) => res[0]);\n        },\n        async getSessionAndUser(sessionToken) {\n            return client\n                .select({\n                session: sessionsTable,\n                user: usersTable,\n            })\n                .from(sessionsTable)\n                .where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(sessionsTable.sessionToken, sessionToken))\n                .innerJoin(usersTable, (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(usersTable.id, sessionsTable.userId))\n                .then((res) => (res.length > 0 ? res[0] : null));\n        },\n        async updateUser(data) {\n            if (!data.id) {\n                throw new Error(\"No user id.\");\n            }\n            await client\n                .update(usersTable)\n                .set(data)\n                .where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(usersTable.id, data.id));\n            const [result] = await client\n                .select()\n                .from(usersTable)\n                .where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(usersTable.id, data.id));\n            if (!result) {\n                throw new Error(\"No user found.\");\n            }\n            return result;\n        },\n        async updateSession(data) {\n            await client\n                .update(sessionsTable)\n                .set(data)\n                .where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(sessionsTable.sessionToken, data.sessionToken));\n            return client\n                .select()\n                .from(sessionsTable)\n                .where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(sessionsTable.sessionToken, data.sessionToken))\n                .then((res) => res[0]);\n        },\n        async linkAccount(data) {\n            await client.insert(accountsTable).values(data);\n        },\n        async getUserByAccount(account) {\n            const result = await client\n                .select({\n                account: accountsTable,\n                user: usersTable,\n            })\n                .from(accountsTable)\n                .innerJoin(usersTable, (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(accountsTable.userId, usersTable.id))\n                .where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(accountsTable.provider, account.provider), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(accountsTable.providerAccountId, account.providerAccountId)))\n                .then((res) => res[0]);\n            return result?.user ?? null;\n        },\n        async deleteSession(sessionToken) {\n            await client\n                .delete(sessionsTable)\n                .where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(sessionsTable.sessionToken, sessionToken));\n        },\n        async createVerificationToken(data) {\n            await client.insert(verificationTokensTable).values(data);\n            return client\n                .select()\n                .from(verificationTokensTable)\n                .where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(verificationTokensTable.identifier, data.identifier))\n                .then((res) => res[0]);\n        },\n        async useVerificationToken(params) {\n            const deletedToken = await client\n                .select()\n                .from(verificationTokensTable)\n                .where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(verificationTokensTable.identifier, params.identifier), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(verificationTokensTable.token, params.token)))\n                .then((res) => (res.length > 0 ? res[0] : null));\n            if (deletedToken) {\n                await client\n                    .delete(verificationTokensTable)\n                    .where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(verificationTokensTable.identifier, params.identifier), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(verificationTokensTable.token, params.token)));\n            }\n            return deletedToken;\n        },\n        async deleteUser(id) {\n            await client.delete(usersTable).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(usersTable.id, id));\n        },\n        async unlinkAccount(params) {\n            await client\n                .delete(accountsTable)\n                .where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(accountsTable.provider, params.provider), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(accountsTable.providerAccountId, params.providerAccountId)));\n        },\n        async getAccount(providerAccountId, provider) {\n            return client\n                .select()\n                .from(accountsTable)\n                .where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(accountsTable.provider, provider), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(accountsTable.providerAccountId, providerAccountId)))\n                .then((res) => res[0] ?? null);\n        },\n        async createAuthenticator(data) {\n            await client.insert(authenticatorsTable).values(data);\n            return await client\n                .select()\n                .from(authenticatorsTable)\n                .where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(authenticatorsTable.credentialID, data.credentialID))\n                .then((res) => res[0] ?? null);\n        },\n        async getAuthenticator(credentialID) {\n            return await client\n                .select()\n                .from(authenticatorsTable)\n                .where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(authenticatorsTable.credentialID, credentialID))\n                .then((res) => res[0] ?? null);\n        },\n        async listAuthenticatorsByUserId(userId) {\n            return await client\n                .select()\n                .from(authenticatorsTable)\n                .where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(authenticatorsTable.userId, userId))\n                .then((res) => res);\n        },\n        async updateAuthenticatorCounter(credentialID, newCounter) {\n            await client\n                .update(authenticatorsTable)\n                .set({ counter: newCounter })\n                .where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(authenticatorsTable.credentialID, credentialID));\n            const authenticator = await client\n                .select()\n                .from(authenticatorsTable)\n                .where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(authenticatorsTable.credentialID, credentialID))\n                .then((res) => res[0]);\n            if (!authenticator)\n                throw new Error(\"Authenticator not found.\");\n            return authenticator;\n        },\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@auth/drizzle-adapter/lib/mysql.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@auth/drizzle-adapter/lib/pg.js":
/*!******************************************************!*\
  !*** ./node_modules/@auth/drizzle-adapter/lib/pg.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PostgresDrizzleAdapter: () => (/* binding */ PostgresDrizzleAdapter),\n/* harmony export */   defineTables: () => (/* binding */ defineTables)\n/* harmony export */ });\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/utils.js\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/conditions.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/table.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/text.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/timestamp.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/integer.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/primary-keys.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/boolean.js\");\n\n\nfunction defineTables(schema = {}) {\n    const usersTable = schema.usersTable ??\n        ((0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)(\"user\", {\n            id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)(\"id\")\n                .primaryKey()\n                .$defaultFn(() => crypto.randomUUID()),\n            name: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)(\"name\"),\n            email: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)(\"email\").notNull(),\n            emailVerified: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.timestamp)(\"emailVerified\", { mode: \"date\" }),\n            image: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)(\"image\"),\n        }));\n    const accountsTable = schema.accountsTable ??\n        ((0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)(\"account\", {\n            userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)(\"userId\")\n                .notNull()\n                .references(() => usersTable.id, { onDelete: \"cascade\" }),\n            type: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)(\"type\").$type().notNull(),\n            provider: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)(\"provider\").notNull(),\n            providerAccountId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)(\"providerAccountId\").notNull(),\n            refresh_token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)(\"refresh_token\"),\n            access_token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)(\"access_token\"),\n            expires_at: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)(\"expires_at\"),\n            token_type: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)(\"token_type\"),\n            scope: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)(\"scope\"),\n            id_token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)(\"id_token\"),\n            session_state: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)(\"session_state\"),\n        }, (account) => ({\n            compositePk: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.primaryKey)({\n                columns: [account.provider, account.providerAccountId],\n            }),\n        })));\n    const sessionsTable = schema.sessionsTable ??\n        ((0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)(\"session\", {\n            sessionToken: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)(\"sessionToken\").primaryKey(),\n            userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)(\"userId\")\n                .notNull()\n                .references(() => usersTable.id, { onDelete: \"cascade\" }),\n            expires: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.timestamp)(\"expires\", { mode: \"date\" }).notNull(),\n        }));\n    const verificationTokensTable = schema.verificationTokensTable ??\n        ((0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)(\"verificationToken\", {\n            identifier: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)(\"identifier\").notNull(),\n            token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)(\"token\").notNull(),\n            expires: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.timestamp)(\"expires\", { mode: \"date\" }).notNull(),\n        }, (verficationToken) => ({\n            compositePk: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.primaryKey)({\n                columns: [verficationToken.identifier, verficationToken.token],\n            }),\n        })));\n    const authenticatorsTable = schema.authenticatorsTable ??\n        ((0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)(\"authenticator\", {\n            credentialID: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)(\"credentialID\").notNull().unique(),\n            userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)(\"userId\")\n                .notNull()\n                .references(() => usersTable.id, { onDelete: \"cascade\" }),\n            providerAccountId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)(\"providerAccountId\").notNull(),\n            credentialPublicKey: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)(\"credentialPublicKey\").notNull(),\n            counter: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)(\"counter\").notNull(),\n            credentialDeviceType: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)(\"credentialDeviceType\").notNull(),\n            credentialBackedUp: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.boolean)(\"credentialBackedUp\").notNull(),\n            transports: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)(\"transports\"),\n        }, (authenticator) => ({\n            compositePK: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.primaryKey)({\n                columns: [authenticator.userId, authenticator.credentialID],\n            }),\n        })));\n    return {\n        usersTable,\n        accountsTable,\n        sessionsTable,\n        verificationTokensTable,\n        authenticatorsTable,\n    };\n}\nfunction PostgresDrizzleAdapter(client, schema) {\n    const { usersTable, accountsTable, sessionsTable, verificationTokensTable, authenticatorsTable, } = defineTables(schema);\n    return {\n        async createUser(data) {\n            const { id, ...insertData } = data;\n            const hasDefaultId = (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_6__.getTableColumns)(usersTable)[\"id\"][\"hasDefault\"];\n            return client\n                .insert(usersTable)\n                .values(hasDefaultId ? insertData : { ...insertData, id })\n                .returning()\n                .then((res) => res[0]);\n        },\n        async getUser(userId) {\n            return client\n                .select()\n                .from(usersTable)\n                .where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(usersTable.id, userId))\n                .then((res) => (res.length > 0 ? res[0] : null));\n        },\n        async getUserByEmail(email) {\n            return client\n                .select()\n                .from(usersTable)\n                .where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(usersTable.email, email))\n                .then((res) => (res.length > 0 ? res[0] : null));\n        },\n        async createSession(data) {\n            return client\n                .insert(sessionsTable)\n                .values(data)\n                .returning()\n                .then((res) => res[0]);\n        },\n        async getSessionAndUser(sessionToken) {\n            return client\n                .select({\n                session: sessionsTable,\n                user: usersTable,\n            })\n                .from(sessionsTable)\n                .where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(sessionsTable.sessionToken, sessionToken))\n                .innerJoin(usersTable, (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(usersTable.id, sessionsTable.userId))\n                .then((res) => (res.length > 0 ? res[0] : null));\n        },\n        async updateUser(data) {\n            if (!data.id) {\n                throw new Error(\"No user id.\");\n            }\n            const [result] = await client\n                .update(usersTable)\n                .set(data)\n                .where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(usersTable.id, data.id))\n                .returning();\n            if (!result) {\n                throw new Error(\"No user found.\");\n            }\n            return result;\n        },\n        async updateSession(data) {\n            return client\n                .update(sessionsTable)\n                .set(data)\n                .where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(sessionsTable.sessionToken, data.sessionToken))\n                .returning()\n                .then((res) => res[0]);\n        },\n        async linkAccount(data) {\n            await client.insert(accountsTable).values(data);\n        },\n        async getUserByAccount(account) {\n            const result = await client\n                .select({\n                account: accountsTable,\n                user: usersTable,\n            })\n                .from(accountsTable)\n                .innerJoin(usersTable, (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(accountsTable.userId, usersTable.id))\n                .where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(accountsTable.provider, account.provider), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(accountsTable.providerAccountId, account.providerAccountId)))\n                .then((res) => res[0]);\n            return result?.user ?? null;\n        },\n        async deleteSession(sessionToken) {\n            await client\n                .delete(sessionsTable)\n                .where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(sessionsTable.sessionToken, sessionToken));\n        },\n        async createVerificationToken(data) {\n            return client\n                .insert(verificationTokensTable)\n                .values(data)\n                .returning()\n                .then((res) => res[0]);\n        },\n        async useVerificationToken(params) {\n            return client\n                .delete(verificationTokensTable)\n                .where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(verificationTokensTable.identifier, params.identifier), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(verificationTokensTable.token, params.token)))\n                .returning()\n                .then((res) => (res.length > 0 ? res[0] : null));\n        },\n        async deleteUser(id) {\n            await client.delete(usersTable).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(usersTable.id, id));\n        },\n        async unlinkAccount(params) {\n            await client\n                .delete(accountsTable)\n                .where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(accountsTable.provider, params.provider), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(accountsTable.providerAccountId, params.providerAccountId)));\n        },\n        async getAccount(providerAccountId, provider) {\n            return client\n                .select()\n                .from(accountsTable)\n                .where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(accountsTable.provider, provider), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(accountsTable.providerAccountId, providerAccountId)))\n                .then((res) => res[0] ?? null);\n        },\n        async createAuthenticator(data) {\n            return client\n                .insert(authenticatorsTable)\n                .values(data)\n                .returning()\n                .then((res) => res[0] ?? null);\n        },\n        async getAuthenticator(credentialID) {\n            return client\n                .select()\n                .from(authenticatorsTable)\n                .where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(authenticatorsTable.credentialID, credentialID))\n                .then((res) => res[0] ?? null);\n        },\n        async listAuthenticatorsByUserId(userId) {\n            return client\n                .select()\n                .from(authenticatorsTable)\n                .where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(authenticatorsTable.userId, userId))\n                .then((res) => res);\n        },\n        async updateAuthenticatorCounter(credentialID, newCounter) {\n            const authenticator = await client\n                .update(authenticatorsTable)\n                .set({ counter: newCounter })\n                .where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(authenticatorsTable.credentialID, credentialID))\n                .returning()\n                .then((res) => res[0]);\n            if (!authenticator)\n                throw new Error(\"Authenticator not found.\");\n            return authenticator;\n        },\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@auth/drizzle-adapter/lib/pg.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@auth/drizzle-adapter/lib/sqlite.js":
/*!**********************************************************!*\
  !*** ./node_modules/@auth/drizzle-adapter/lib/sqlite.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SQLiteDrizzleAdapter: () => (/* binding */ SQLiteDrizzleAdapter),\n/* harmony export */   defineTables: () => (/* binding */ defineTables)\n/* harmony export */ });\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/utils.js\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/conditions.js\");\n/* harmony import */ var drizzle_orm_sqlite_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! drizzle-orm/sqlite-core */ \"(rsc)/./node_modules/drizzle-orm/sqlite-core/table.js\");\n/* harmony import */ var drizzle_orm_sqlite_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! drizzle-orm/sqlite-core */ \"(rsc)/./node_modules/drizzle-orm/sqlite-core/columns/text.js\");\n/* harmony import */ var drizzle_orm_sqlite_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! drizzle-orm/sqlite-core */ \"(rsc)/./node_modules/drizzle-orm/sqlite-core/columns/integer.js\");\n/* harmony import */ var drizzle_orm_sqlite_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! drizzle-orm/sqlite-core */ \"(rsc)/./node_modules/drizzle-orm/sqlite-core/primary-keys.js\");\n\n\nfunction defineTables(schema = {}) {\n    const usersTable = schema.usersTable ??\n        ((0,drizzle_orm_sqlite_core__WEBPACK_IMPORTED_MODULE_0__.sqliteTable)(\"user\", {\n            id: (0,drizzle_orm_sqlite_core__WEBPACK_IMPORTED_MODULE_1__.text)(\"id\")\n                .primaryKey()\n                .$defaultFn(() => crypto.randomUUID()),\n            name: (0,drizzle_orm_sqlite_core__WEBPACK_IMPORTED_MODULE_1__.text)(\"name\"),\n            email: (0,drizzle_orm_sqlite_core__WEBPACK_IMPORTED_MODULE_1__.text)(\"email\").notNull(),\n            emailVerified: (0,drizzle_orm_sqlite_core__WEBPACK_IMPORTED_MODULE_2__.integer)(\"emailVerified\", { mode: \"timestamp_ms\" }),\n            image: (0,drizzle_orm_sqlite_core__WEBPACK_IMPORTED_MODULE_1__.text)(\"image\"),\n        }));\n    const accountsTable = schema.accountsTable ??\n        ((0,drizzle_orm_sqlite_core__WEBPACK_IMPORTED_MODULE_0__.sqliteTable)(\"account\", {\n            userId: (0,drizzle_orm_sqlite_core__WEBPACK_IMPORTED_MODULE_1__.text)(\"userId\")\n                .notNull()\n                .references(() => usersTable.id, { onDelete: \"cascade\" }),\n            type: (0,drizzle_orm_sqlite_core__WEBPACK_IMPORTED_MODULE_1__.text)(\"type\").$type().notNull(),\n            provider: (0,drizzle_orm_sqlite_core__WEBPACK_IMPORTED_MODULE_1__.text)(\"provider\").notNull(),\n            providerAccountId: (0,drizzle_orm_sqlite_core__WEBPACK_IMPORTED_MODULE_1__.text)(\"providerAccountId\").notNull(),\n            refresh_token: (0,drizzle_orm_sqlite_core__WEBPACK_IMPORTED_MODULE_1__.text)(\"refresh_token\"),\n            access_token: (0,drizzle_orm_sqlite_core__WEBPACK_IMPORTED_MODULE_1__.text)(\"access_token\"),\n            expires_at: (0,drizzle_orm_sqlite_core__WEBPACK_IMPORTED_MODULE_2__.integer)(\"expires_at\"),\n            token_type: (0,drizzle_orm_sqlite_core__WEBPACK_IMPORTED_MODULE_1__.text)(\"token_type\"),\n            scope: (0,drizzle_orm_sqlite_core__WEBPACK_IMPORTED_MODULE_1__.text)(\"scope\"),\n            id_token: (0,drizzle_orm_sqlite_core__WEBPACK_IMPORTED_MODULE_1__.text)(\"id_token\"),\n            session_state: (0,drizzle_orm_sqlite_core__WEBPACK_IMPORTED_MODULE_1__.text)(\"session_state\"),\n        }, (account) => ({\n            compositePk: (0,drizzle_orm_sqlite_core__WEBPACK_IMPORTED_MODULE_3__.primaryKey)({\n                columns: [account.provider, account.providerAccountId],\n            }),\n        })));\n    const sessionsTable = schema.sessionsTable ??\n        ((0,drizzle_orm_sqlite_core__WEBPACK_IMPORTED_MODULE_0__.sqliteTable)(\"session\", {\n            sessionToken: (0,drizzle_orm_sqlite_core__WEBPACK_IMPORTED_MODULE_1__.text)(\"sessionToken\").primaryKey(),\n            userId: (0,drizzle_orm_sqlite_core__WEBPACK_IMPORTED_MODULE_1__.text)(\"userId\")\n                .notNull()\n                .references(() => usersTable.id, { onDelete: \"cascade\" }),\n            expires: (0,drizzle_orm_sqlite_core__WEBPACK_IMPORTED_MODULE_2__.integer)(\"expires\", { mode: \"timestamp_ms\" }).notNull(),\n        }));\n    const verificationTokensTable = schema.verificationTokensTable ??\n        ((0,drizzle_orm_sqlite_core__WEBPACK_IMPORTED_MODULE_0__.sqliteTable)(\"verificationToken\", {\n            identifier: (0,drizzle_orm_sqlite_core__WEBPACK_IMPORTED_MODULE_1__.text)(\"identifier\").notNull(),\n            token: (0,drizzle_orm_sqlite_core__WEBPACK_IMPORTED_MODULE_1__.text)(\"token\").notNull(),\n            expires: (0,drizzle_orm_sqlite_core__WEBPACK_IMPORTED_MODULE_2__.integer)(\"expires\", { mode: \"timestamp_ms\" }).notNull(),\n        }, (verficationToken) => ({\n            compositePk: (0,drizzle_orm_sqlite_core__WEBPACK_IMPORTED_MODULE_3__.primaryKey)({\n                columns: [verficationToken.identifier, verficationToken.token],\n            }),\n        })));\n    const authenticatorsTable = schema.authenticatorsTable ??\n        ((0,drizzle_orm_sqlite_core__WEBPACK_IMPORTED_MODULE_0__.sqliteTable)(\"authenticator\", {\n            credentialID: (0,drizzle_orm_sqlite_core__WEBPACK_IMPORTED_MODULE_1__.text)(\"credentialID\").notNull().unique(),\n            userId: (0,drizzle_orm_sqlite_core__WEBPACK_IMPORTED_MODULE_1__.text)(\"userId\")\n                .notNull()\n                .references(() => usersTable.id, { onDelete: \"cascade\" }),\n            providerAccountId: (0,drizzle_orm_sqlite_core__WEBPACK_IMPORTED_MODULE_1__.text)(\"providerAccountId\").notNull(),\n            credentialPublicKey: (0,drizzle_orm_sqlite_core__WEBPACK_IMPORTED_MODULE_1__.text)(\"credentialPublicKey\").notNull(),\n            counter: (0,drizzle_orm_sqlite_core__WEBPACK_IMPORTED_MODULE_2__.integer)(\"counter\").notNull(),\n            credentialDeviceType: (0,drizzle_orm_sqlite_core__WEBPACK_IMPORTED_MODULE_1__.text)(\"credentialDeviceType\").notNull(),\n            credentialBackedUp: (0,drizzle_orm_sqlite_core__WEBPACK_IMPORTED_MODULE_2__.integer)(\"credentialBackedUp\", {\n                mode: \"boolean\",\n            }).notNull(),\n            transports: (0,drizzle_orm_sqlite_core__WEBPACK_IMPORTED_MODULE_1__.text)(\"transports\"),\n        }, (authenticator) => ({\n            compositePK: (0,drizzle_orm_sqlite_core__WEBPACK_IMPORTED_MODULE_3__.primaryKey)({\n                columns: [authenticator.userId, authenticator.credentialID],\n            }),\n        })));\n    return {\n        usersTable,\n        accountsTable,\n        sessionsTable,\n        verificationTokensTable,\n        authenticatorsTable,\n    };\n}\nfunction SQLiteDrizzleAdapter(client, schema) {\n    const { usersTable, accountsTable, sessionsTable, verificationTokensTable, authenticatorsTable, } = defineTables(schema);\n    return {\n        async createUser(data) {\n            const { id, ...insertData } = data;\n            const hasDefaultId = (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.getTableColumns)(usersTable)[\"id\"][\"hasDefault\"];\n            return client\n                .insert(usersTable)\n                .values(hasDefaultId ? insertData : { ...insertData, id })\n                .returning()\n                .get();\n        },\n        async getUser(userId) {\n            const result = await client\n                .select()\n                .from(usersTable)\n                .where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(usersTable.id, userId))\n                .get();\n            return result ?? null;\n        },\n        async getUserByEmail(email) {\n            const result = await client\n                .select()\n                .from(usersTable)\n                .where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(usersTable.email, email))\n                .get();\n            return result ?? null;\n        },\n        async createSession(data) {\n            return client.insert(sessionsTable).values(data).returning().get();\n        },\n        async getSessionAndUser(sessionToken) {\n            const result = await client\n                .select({\n                session: sessionsTable,\n                user: usersTable,\n            })\n                .from(sessionsTable)\n                .where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(sessionsTable.sessionToken, sessionToken))\n                .innerJoin(usersTable, (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(usersTable.id, sessionsTable.userId))\n                .get();\n            return result ?? null;\n        },\n        async updateUser(data) {\n            if (!data.id) {\n                throw new Error(\"No user id.\");\n            }\n            const result = await client\n                .update(usersTable)\n                .set(data)\n                .where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(usersTable.id, data.id))\n                .returning()\n                .get();\n            if (!result) {\n                throw new Error(\"User not found.\");\n            }\n            return result;\n        },\n        async updateSession(data) {\n            const result = await client\n                .update(sessionsTable)\n                .set(data)\n                .where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(sessionsTable.sessionToken, data.sessionToken))\n                .returning()\n                .get();\n            return result ?? null;\n        },\n        async linkAccount(data) {\n            await client.insert(accountsTable).values(data).run();\n        },\n        async getUserByAccount(account) {\n            const result = await client\n                .select({\n                account: accountsTable,\n                user: usersTable,\n            })\n                .from(accountsTable)\n                .innerJoin(usersTable, (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(accountsTable.userId, usersTable.id))\n                .where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(accountsTable.provider, account.provider), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(accountsTable.providerAccountId, account.providerAccountId)))\n                .get();\n            return result?.user ?? null;\n        },\n        async deleteSession(sessionToken) {\n            await client\n                .delete(sessionsTable)\n                .where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(sessionsTable.sessionToken, sessionToken))\n                .run();\n        },\n        async createVerificationToken(data) {\n            return client\n                .insert(verificationTokensTable)\n                .values(data)\n                .returning()\n                .get();\n        },\n        async useVerificationToken(params) {\n            const result = await client\n                .delete(verificationTokensTable)\n                .where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(verificationTokensTable.identifier, params.identifier), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(verificationTokensTable.token, params.token)))\n                .returning()\n                .get();\n            return result ?? null;\n        },\n        async deleteUser(id) {\n            await client.delete(usersTable).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(usersTable.id, id)).run();\n        },\n        async unlinkAccount(params) {\n            await client\n                .delete(accountsTable)\n                .where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(accountsTable.provider, params.provider), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(accountsTable.providerAccountId, params.providerAccountId)))\n                .run();\n        },\n        async getAccount(providerAccountId, provider) {\n            return client\n                .select()\n                .from(accountsTable)\n                .where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(accountsTable.provider, provider), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(accountsTable.providerAccountId, providerAccountId)))\n                .then((res) => res[0] ?? null);\n        },\n        async createAuthenticator(data) {\n            return client\n                .insert(authenticatorsTable)\n                .values(data)\n                .returning()\n                .then((res) => res[0] ?? null);\n        },\n        async getAuthenticator(credentialID) {\n            return client\n                .select()\n                .from(authenticatorsTable)\n                .where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(authenticatorsTable.credentialID, credentialID))\n                .then((res) => res[0] ?? null);\n        },\n        async listAuthenticatorsByUserId(userId) {\n            return client\n                .select()\n                .from(authenticatorsTable)\n                .where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(authenticatorsTable.userId, userId))\n                .then((res) => res);\n        },\n        async updateAuthenticatorCounter(credentialID, newCounter) {\n            const authenticator = await client\n                .update(authenticatorsTable)\n                .set({ counter: newCounter })\n                .where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(authenticatorsTable.credentialID, credentialID))\n                .returning()\n                .then((res) => res[0]);\n            if (!authenticator)\n                throw new Error(\"Authenticator not found.\");\n            return authenticator;\n        },\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@auth/drizzle-adapter/lib/sqlite.js\n");

/***/ })

};
;