"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[projectId]/page",{

/***/ "(app-pages-browser)/./src/features/editor/hooks/use-zoom-events.ts":
/*!******************************************************!*\
  !*** ./src/features/editor/hooks/use-zoom-events.ts ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useZoomEvents: function() { return /* binding */ useZoomEvents; }\n/* harmony export */ });\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fabric */ \"(app-pages-browser)/./node_modules/fabric/dist/fabric.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fabric__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst useZoomEvents = (param)=>{\n    let { canvas } = param;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!canvas) return;\n        let isZooming = false;\n        let lastDistance = 0;\n        // Handle trackpad pinch-to-zoom (touch events)\n        const handleTouchStart = (e)=>{\n            if (e.touches.length === 2) {\n                isZooming = true;\n                const touch1 = e.touches[0];\n                const touch2 = e.touches[1];\n                lastDistance = Math.sqrt(Math.pow(touch2.clientX - touch1.clientX, 2) + Math.pow(touch2.clientY - touch1.clientY, 2));\n                e.preventDefault();\n            }\n        };\n        const handleTouchMove = (e)=>{\n            if (e.touches.length === 2 && isZooming) {\n                const touch1 = e.touches[0];\n                const touch2 = e.touches[1];\n                const distance = Math.sqrt(Math.pow(touch2.clientX - touch1.clientX, 2) + Math.pow(touch2.clientY - touch1.clientY, 2));\n                if (lastDistance > 0) {\n                    const scale = distance / lastDistance;\n                    // Make pinch zoom much more gentle and controlled\n                    const scaleFactor = 1 + (scale - 1) * 0.1; // Reduce sensitivity by 90%\n                    let zoom = canvas.getZoom() * scaleFactor;\n                    // Limit zoom levels\n                    if (zoom > 1) zoom = 1;\n                    if (zoom < 0.2) zoom = 0.2;\n                    // Get center point between fingers\n                    const centerX = (touch1.clientX + touch2.clientX) / 2;\n                    const centerY = (touch1.clientY + touch2.clientY) / 2;\n                    // Get canvas bounds\n                    const canvasElement = canvas.getElement();\n                    const rect = canvasElement.getBoundingClientRect();\n                    // Convert to canvas coordinates\n                    const point = {\n                        x: centerX - rect.left,\n                        y: centerY - rect.top\n                    };\n                    canvas.zoomToPoint(point, zoom);\n                }\n                lastDistance = distance;\n                e.preventDefault();\n            }\n        };\n        const handleTouchEnd = (e)=>{\n            if (e.touches.length < 2) {\n                isZooming = false;\n                lastDistance = 0;\n            }\n        };\n        // Handle keyboard zoom shortcuts\n        const handleKeyDown = (e)=>{\n            const isCtrlKey = e.ctrlKey || e.metaKey;\n            // Prevent default browser zoom\n            if (isCtrlKey && (e.key === \"+\" || e.key === \"-\" || e.key === \"0\")) {\n                e.preventDefault();\n            }\n            // Ctrl/Cmd + Plus: Zoom in\n            if (isCtrlKey && (e.key === \"+\" || e.key === \"=\")) {\n                let zoom = canvas.getZoom();\n                zoom += 0.1;\n                if (zoom > 1) zoom = 1;\n                const center = canvas.getCenter();\n                canvas.zoomToPoint(new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Point(center.left, center.top), zoom);\n                e.preventDefault();\n            }\n            // Ctrl/Cmd + Minus: Zoom out\n            if (isCtrlKey && e.key === \"-\") {\n                let zoom = canvas.getZoom();\n                zoom -= 0.1;\n                if (zoom < 0.2) zoom = 0.2;\n                const center = canvas.getCenter();\n                canvas.zoomToPoint(new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Point(center.left, center.top), zoom);\n                e.preventDefault();\n            }\n            // Ctrl/Cmd + 0: Reset zoom\n            if (isCtrlKey && e.key === \"0\") {\n                const center = canvas.getCenter();\n                canvas.zoomToPoint(new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Point(center.left, center.top), 1);\n                e.preventDefault();\n            }\n        };\n        // Add event listeners to canvas element\n        const canvasElement = canvas.getElement();\n        if (canvasElement) {\n            canvasElement.addEventListener(\"touchstart\", handleTouchStart, {\n                passive: false\n            });\n            canvasElement.addEventListener(\"touchmove\", handleTouchMove, {\n                passive: false\n            });\n            canvasElement.addEventListener(\"touchend\", handleTouchEnd, {\n                passive: false\n            });\n        }\n        // Add keyboard events to document\n        document.addEventListener(\"keydown\", handleKeyDown);\n        return ()=>{\n            if (canvasElement) {\n                canvasElement.removeEventListener(\"touchstart\", handleTouchStart);\n                canvasElement.removeEventListener(\"touchmove\", handleTouchMove);\n                canvasElement.removeEventListener(\"touchend\", handleTouchEnd);\n            }\n            document.removeEventListener(\"keydown\", handleKeyDown);\n        };\n    }, [\n        canvas\n    ]);\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/hooks/use-zoom-events.ts\n"));

/***/ })

});