"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[projectId]/page",{

/***/ "(app-pages-browser)/./src/features/editor/components/editor.tsx":
/*!***************************************************!*\
  !*** ./src/features/editor/components/editor.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Editor: function() { return /* binding */ Editor; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fabric */ \"(app-pages-browser)/./node_modules/fabric/dist/fabric.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fabric__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash.debounce */ \"(app-pages-browser)/./node_modules/lodash.debounce/index.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _features_projects_api_use_update_project__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/features/projects/api/use-update-project */ \"(app-pages-browser)/./src/features/projects/api/use-update-project.ts\");\n/* harmony import */ var _features_editor_types__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/features/editor/types */ \"(app-pages-browser)/./src/features/editor/types.ts\");\n/* harmony import */ var _features_editor_components_navbar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/features/editor/components/navbar */ \"(app-pages-browser)/./src/features/editor/components/navbar.tsx\");\n/* harmony import */ var _features_editor_components_footer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/features/editor/components/footer */ \"(app-pages-browser)/./src/features/editor/components/footer.tsx\");\n/* harmony import */ var _features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/features/editor/hooks/use-editor */ \"(app-pages-browser)/./src/features/editor/hooks/use-editor.ts\");\n/* harmony import */ var _features_editor_components_sidebar__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/features/editor/components/sidebar */ \"(app-pages-browser)/./src/features/editor/components/sidebar.tsx\");\n/* harmony import */ var _features_editor_components_toolbar__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/features/editor/components/toolbar */ \"(app-pages-browser)/./src/features/editor/components/toolbar.tsx\");\n/* harmony import */ var _features_editor_components_shape_sidebar__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/features/editor/components/shape-sidebar */ \"(app-pages-browser)/./src/features/editor/components/shape-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_fill_color_sidebar__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/features/editor/components/fill-color-sidebar */ \"(app-pages-browser)/./src/features/editor/components/fill-color-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_stroke_color_sidebar__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/features/editor/components/stroke-color-sidebar */ \"(app-pages-browser)/./src/features/editor/components/stroke-color-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_stroke_width_sidebar__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/features/editor/components/stroke-width-sidebar */ \"(app-pages-browser)/./src/features/editor/components/stroke-width-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_opacity_sidebar__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/features/editor/components/opacity-sidebar */ \"(app-pages-browser)/./src/features/editor/components/opacity-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_text_sidebar__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/features/editor/components/text-sidebar */ \"(app-pages-browser)/./src/features/editor/components/text-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_font_sidebar__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/features/editor/components/font-sidebar */ \"(app-pages-browser)/./src/features/editor/components/font-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_image_sidebar__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/features/editor/components/image-sidebar */ \"(app-pages-browser)/./src/features/editor/components/image-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_filter_sidebar__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/features/editor/components/filter-sidebar */ \"(app-pages-browser)/./src/features/editor/components/filter-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_draw_sidebar__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/features/editor/components/draw-sidebar */ \"(app-pages-browser)/./src/features/editor/components/draw-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_ai_sidebar__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/features/editor/components/ai-sidebar */ \"(app-pages-browser)/./src/features/editor/components/ai-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_template_sidebar__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/features/editor/components/template-sidebar */ \"(app-pages-browser)/./src/features/editor/components/template-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_remove_bg_sidebar__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/features/editor/components/remove-bg-sidebar */ \"(app-pages-browser)/./src/features/editor/components/remove-bg-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_settings_sidebar__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/features/editor/components/settings-sidebar */ \"(app-pages-browser)/./src/features/editor/components/settings-sidebar.tsx\");\n/* __next_internal_client_entry_do_not_use__ Editor auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst Editor = (param)=>{\n    let { initialData } = param;\n    _s();\n    const { mutate } = (0,_features_projects_api_use_update_project__WEBPACK_IMPORTED_MODULE_4__.useUpdateProject)(initialData.id);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    const debouncedSave = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(lodash_debounce__WEBPACK_IMPORTED_MODULE_2___default()((values)=>{\n        mutate(values);\n    }, 500), [\n        mutate\n    ]);\n    const [activeTool, setActiveTool] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"select\");\n    const [canvasIsSelected, setCanvasIsSelected] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const onClearSelection = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(()=>{\n        if (_features_editor_types__WEBPACK_IMPORTED_MODULE_5__.selectionDependentTools.includes(activeTool)) {\n            setActiveTool(\"select\");\n        }\n    }, [\n        activeTool\n    ]);\n    const { init, editor } = (0,_features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_8__.useEditor)({\n        defaultState: initialData.json,\n        defaultWidth: initialData.width,\n        defaultHeight: initialData.height,\n        clearSelectionCallback: onClearSelection,\n        saveCallback: debouncedSave,\n        setCanvasIsSelected\n    });\n    const onChangeActiveTool = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)((tool)=>{\n        if (tool === \"draw\") {\n            editor === null || editor === void 0 ? void 0 : editor.enableDrawingMode();\n        }\n        if (activeTool === \"draw\") {\n            editor === null || editor === void 0 ? void 0 : editor.disableDrawingMode();\n        }\n        if (tool === activeTool) {\n            return setActiveTool(\"select\");\n        }\n        setActiveTool(tool);\n    }, [\n        activeTool,\n        editor\n    ]);\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        const canvas = new fabric__WEBPACK_IMPORTED_MODULE_1__.fabric.Canvas(canvasRef.current, {\n            controlsAboveOverlay: true,\n            preserveObjectStacking: true\n        });\n        init({\n            initialCanvas: canvas,\n            initialContainer: containerRef.current\n        });\n        return ()=>{\n            canvas.dispose();\n        };\n    }, [\n        init\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_navbar__WEBPACK_IMPORTED_MODULE_6__.Navbar, {\n                id: initialData.id,\n                editor: editor,\n                activeTool: activeTool,\n                onChangeActiveTool: onChangeActiveTool,\n                canvasIsSelected: canvasIsSelected\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute h-[calc(100%-68px)] w-full top-[68px] flex\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_sidebar__WEBPACK_IMPORTED_MODULE_9__.Sidebar, {\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_shape_sidebar__WEBPACK_IMPORTED_MODULE_11__.ShapeSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_fill_color_sidebar__WEBPACK_IMPORTED_MODULE_12__.FillColorSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_stroke_color_sidebar__WEBPACK_IMPORTED_MODULE_13__.StrokeColorSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_stroke_width_sidebar__WEBPACK_IMPORTED_MODULE_14__.StrokeWidthSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_opacity_sidebar__WEBPACK_IMPORTED_MODULE_15__.OpacitySidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_text_sidebar__WEBPACK_IMPORTED_MODULE_16__.TextSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_font_sidebar__WEBPACK_IMPORTED_MODULE_17__.FontSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_image_sidebar__WEBPACK_IMPORTED_MODULE_18__.ImageSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_template_sidebar__WEBPACK_IMPORTED_MODULE_22__.TemplateSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_filter_sidebar__WEBPACK_IMPORTED_MODULE_19__.FilterSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_ai_sidebar__WEBPACK_IMPORTED_MODULE_21__.AiSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_remove_bg_sidebar__WEBPACK_IMPORTED_MODULE_23__.RemoveBgSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_draw_sidebar__WEBPACK_IMPORTED_MODULE_20__.DrawSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_settings_sidebar__WEBPACK_IMPORTED_MODULE_24__.SettingsSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"bg-muted flex-1 overflow-auto relative flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_toolbar__WEBPACK_IMPORTED_MODULE_10__.Toolbar, {\n                                editor: editor,\n                                activeTool: activeTool,\n                                onChangeActiveTool: onChangeActiveTool\n                            }, JSON.stringify(editor === null || editor === void 0 ? void 0 : editor.canvas.getActiveObject()), false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 h-[calc(100%-124px)] bg-muted\",\n                                ref: containerRef,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                                    ref: canvasRef\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_footer__WEBPACK_IMPORTED_MODULE_7__.Footer, {\n                                editor: editor\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n        lineNumber: 109,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Editor, \"n/S7kUBrgsra2E9lpJNgEzHIIBc=\", false, function() {\n    return [\n        _features_projects_api_use_update_project__WEBPACK_IMPORTED_MODULE_4__.useUpdateProject,\n        _features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_8__.useEditor\n    ];\n});\n_c = Editor;\nvar _c;\n$RefreshReg$(_c, \"Editor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/components/editor.tsx\n"));

/***/ })

});