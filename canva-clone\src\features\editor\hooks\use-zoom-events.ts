import { fabric } from "fabric";
import { useEffect } from "react";

interface UseZoomEventsProps {
  canvas: fabric.Canvas | null;
}

export const useZoomEvents = ({ canvas }: UseZoomEventsProps) => {
  useEffect(() => {
    if (!canvas) return;

    let isZooming = false;
    let lastDistance = 0;

    // Handle trackpad pinch-to-zoom (touch events)
    const handleTouchStart = (e: TouchEvent) => {
      if (e.touches.length === 2) {
        isZooming = true;
        const touch1 = e.touches[0];
        const touch2 = e.touches[1];
        lastDistance = Math.sqrt(
          Math.pow(touch2.clientX - touch1.clientX, 2) +
          Math.pow(touch2.clientY - touch1.clientY, 2)
        );
        e.preventDefault();
      }
    };

    const handleTouchMove = (e: TouchEvent) => {
      if (e.touches.length === 2 && isZooming) {
        const touch1 = e.touches[0];
        const touch2 = e.touches[1];
        const distance = Math.sqrt(
          Math.pow(touch2.clientX - touch1.clientX, 2) +
          Math.pow(touch2.clientY - touch1.clientY, 2)
        );

        if (lastDistance > 0) {
          const scale = distance / lastDistance;
          let zoom = canvas.getZoom() * scale;

          // Limit zoom levels
          if (zoom > 1) zoom = 1;
          if (zoom < 0.2) zoom = 0.2;

          // Get center point between fingers
          const centerX = (touch1.clientX + touch2.clientX) / 2;
          const centerY = (touch1.clientY + touch2.clientY) / 2;

          // Get canvas bounds
          const canvasElement = canvas.getElement();
          const rect = canvasElement.getBoundingClientRect();
          
          // Convert to canvas coordinates
          const point = {
            x: centerX - rect.left,
            y: centerY - rect.top
          };

          canvas.zoomToPoint(point, zoom);
        }

        lastDistance = distance;
        e.preventDefault();
      }
    };

    const handleTouchEnd = (e: TouchEvent) => {
      if (e.touches.length < 2) {
        isZooming = false;
        lastDistance = 0;
      }
    };

    // Handle keyboard zoom shortcuts
    const handleKeyDown = (e: KeyboardEvent) => {
      const isCtrlKey = e.ctrlKey || e.metaKey;
      
      // Prevent default browser zoom
      if (isCtrlKey && (e.key === '+' || e.key === '-' || e.key === '0')) {
        e.preventDefault();
      }

      // Ctrl/Cmd + Plus: Zoom in
      if (isCtrlKey && (e.key === '+' || e.key === '=')) {
        let zoom = canvas.getZoom();
        zoom += 0.1;
        if (zoom > 1) zoom = 1;
        
        const center = canvas.getCenter();
        canvas.zoomToPoint(
          new fabric.Point(center.left, center.top),
          zoom
        );
        e.preventDefault();
      }

      // Ctrl/Cmd + Minus: Zoom out
      if (isCtrlKey && e.key === '-') {
        let zoom = canvas.getZoom();
        zoom -= 0.1;
        if (zoom < 0.2) zoom = 0.2;
        
        const center = canvas.getCenter();
        canvas.zoomToPoint(
          new fabric.Point(center.left, center.top),
          zoom
        );
        e.preventDefault();
      }

      // Ctrl/Cmd + 0: Reset zoom
      if (isCtrlKey && e.key === '0') {
        const center = canvas.getCenter();
        canvas.zoomToPoint(
          new fabric.Point(center.left, center.top),
          1
        );
        e.preventDefault();
      }
    };

    // Add event listeners to canvas element
    const canvasElement = canvas.getElement();
    
    if (canvasElement) {
      canvasElement.addEventListener('touchstart', handleTouchStart, { passive: false });
      canvasElement.addEventListener('touchmove', handleTouchMove, { passive: false });
      canvasElement.addEventListener('touchend', handleTouchEnd, { passive: false });
    }

    // Add keyboard events to document
    document.addEventListener('keydown', handleKeyDown);

    return () => {
      if (canvasElement) {
        canvasElement.removeEventListener('touchstart', handleTouchStart);
        canvasElement.removeEventListener('touchmove', handleTouchMove);
        canvasElement.removeEventListener('touchend', handleTouchEnd);
      }
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [canvas]);
};
