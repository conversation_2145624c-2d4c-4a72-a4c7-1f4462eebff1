/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/[[...route]]/route";
exports.ids = ["app/api/[[...route]]/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/replicate/lib sync recursive":
/*!******************************************!*\
  !*** ./node_modules/replicate/lib/ sync ***!
  \******************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/replicate/lib sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs/promises":
/*!******************************!*\
  !*** external "fs/promises" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("fs/promises");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:buffer");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2F%5B%5B...route%5D%5D%2Froute&page=%2Fapi%2F%5B%5B...route%5D%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2F%5B%5B...route%5D%5D%2Froute.ts&appDir=C%3A%5CUsers%5Canwar%5CDocuments%5CSKETTCHA%5Ccanva-clone%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Canwar%5CDocuments%5CSKETTCHA%5Ccanva-clone&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2F%5B%5B...route%5D%5D%2Froute&page=%2Fapi%2F%5B%5B...route%5D%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2F%5B%5B...route%5D%5D%2Froute.ts&appDir=C%3A%5CUsers%5Canwar%5CDocuments%5CSKETTCHA%5Ccanva-clone%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Canwar%5CDocuments%5CSKETTCHA%5Ccanva-clone&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_anwar_Documents_SKETTCHA_canva_clone_src_app_api_route_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/[[...route]]/route.ts */ \"(rsc)/./src/app/api/[[...route]]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/[[...route]]/route\",\n        pathname: \"/api/[[...route]]\",\n        filename: \"route\",\n        bundlePath: \"app/api/[[...route]]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\app\\\\api\\\\[[...route]]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_anwar_Documents_SKETTCHA_canva_clone_src_app_api_route_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/[[...route]]/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2F%5B%5B...route%5D%5D%2Froute&page=%2Fapi%2F%5B%5B...route%5D%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2F%5B%5B...route%5D%5D%2Froute.ts&appDir=C%3A%5CUsers%5Canwar%5CDocuments%5CSKETTCHA%5Ccanva-clone%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Canwar%5CDocuments%5CSKETTCHA%5Ccanva-clone&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/[[...route]]/ai.ts":
/*!****************************************!*\
  !*** ./src/app/api/[[...route]]/ai.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/lib/index.mjs\");\n/* harmony import */ var hono__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hono */ \"(rsc)/./node_modules/hono/dist/index.js\");\n/* harmony import */ var _hono_auth_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @hono/auth-js */ \"(rsc)/./node_modules/@hono/auth-js/dist/index.mjs\");\n/* harmony import */ var _hono_zod_validator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hono/zod-validator */ \"(rsc)/./node_modules/@hono/zod-validator/dist/esm/index.js\");\n/* harmony import */ var _lib_replicate__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/replicate */ \"(rsc)/./src/lib/replicate.ts\");\n\n\n\n\n\nconst app = new hono__WEBPACK_IMPORTED_MODULE_0__.Hono().post(\"/remove-bg\", (0,_hono_auth_js__WEBPACK_IMPORTED_MODULE_1__.verifyAuth)(), (0,_hono_zod_validator__WEBPACK_IMPORTED_MODULE_2__.zValidator)(\"json\", zod__WEBPACK_IMPORTED_MODULE_4__.z.object({\n    image: zod__WEBPACK_IMPORTED_MODULE_4__.z.string()\n})), async (c)=>{\n    const { image } = c.req.valid(\"json\");\n    const input = {\n        image: image\n    };\n    const output = await _lib_replicate__WEBPACK_IMPORTED_MODULE_3__.replicate.run(\"cjwbw/rembg:fb8af171cfa1616ddcf1242c093f9c46bcada5ad4cf6f2fbe8b81b330ec5c003\", {\n        input\n    });\n    const res = output;\n    return c.json({\n        data: res\n    });\n}).post(\"/generate-image\", (0,_hono_auth_js__WEBPACK_IMPORTED_MODULE_1__.verifyAuth)(), (0,_hono_zod_validator__WEBPACK_IMPORTED_MODULE_2__.zValidator)(\"json\", zod__WEBPACK_IMPORTED_MODULE_4__.z.object({\n    prompt: zod__WEBPACK_IMPORTED_MODULE_4__.z.string()\n})), async (c)=>{\n    const { prompt } = c.req.valid(\"json\");\n    const input = {\n        cfg: 3.5,\n        steps: 28,\n        prompt: prompt,\n        aspect_ratio: \"3:2\",\n        output_format: \"webp\",\n        output_quality: 90,\n        negative_prompt: \"\",\n        prompt_strength: 0.85\n    };\n    const output = await _lib_replicate__WEBPACK_IMPORTED_MODULE_3__.replicate.run(\"stability-ai/stable-diffusion-3\", {\n        input\n    });\n    const res = output;\n    return c.json({\n        data: res[0]\n    });\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (app);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/[[...route]]/ai.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/api/[[...route]]/images.ts":
/*!********************************************!*\
  !*** ./src/app/api/[[...route]]/images.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var hono__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hono */ \"(rsc)/./node_modules/hono/dist/index.js\");\n/* harmony import */ var _hono_auth_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @hono/auth-js */ \"(rsc)/./node_modules/@hono/auth-js/dist/index.mjs\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/conditions.js\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/select.js\");\n/* harmony import */ var _lib_unsplash__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/unsplash */ \"(rsc)/./src/lib/unsplash.ts\");\n/* harmony import */ var _db_drizzle__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/db/drizzle */ \"(rsc)/./src/db/drizzle.ts\");\n/* harmony import */ var _db_schema__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/db/schema */ \"(rsc)/./src/db/schema.ts\");\n\n\n\n\n\n\nconst DEFAULT_COUNT = 50;\nconst DEFAULT_COLLECTION_IDS = [\n    \"317099\"\n];\nconst app = new hono__WEBPACK_IMPORTED_MODULE_0__.Hono().get(\"/\", (0,_hono_auth_js__WEBPACK_IMPORTED_MODULE_1__.verifyAuth)(), async (c)=>{\n    const auth = c.get(\"authUser\");\n    if (!auth.token?.id) {\n        return c.json({\n            error: \"Unauthorized\"\n        }, 401);\n    }\n    try {\n        // Get user's uploaded images\n        const userImages = await _db_drizzle__WEBPACK_IMPORTED_MODULE_3__.db.select().from(_db_schema__WEBPACK_IMPORTED_MODULE_4__.uploadedImages).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(_db_schema__WEBPACK_IMPORTED_MODULE_4__.uploadedImages.userId, auth.token.id)).orderBy((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_6__.desc)(_db_schema__WEBPACK_IMPORTED_MODULE_4__.uploadedImages.createdAt)).limit(20);\n        // Transform uploaded images to match expected format\n        const transformedUserImages = userImages.map((img)=>({\n                id: img.id,\n                urls: {\n                    regular: img.url,\n                    small: img.url,\n                    thumb: img.url\n                },\n                alt_description: img.originalName,\n                links: {\n                    html: \"#\"\n                },\n                user: {\n                    name: \"Your Upload\"\n                },\n                isUserUpload: true\n            }));\n        // Get Unsplash images (only if API key is configured)\n        let unsplashImages = [];\n        if (process.env.UNSPLASH_ACCESS_KEY) {\n            try {\n                const images = await _lib_unsplash__WEBPACK_IMPORTED_MODULE_2__.unsplash.photos.getRandom({\n                    collectionIds: DEFAULT_COLLECTION_IDS,\n                    count: DEFAULT_COUNT\n                });\n                if (!images.errors) {\n                    let response = images.response;\n                    if (!Array.isArray(response)) {\n                        response = [\n                            response\n                        ];\n                    }\n                    unsplashImages = response.map((img)=>({\n                            ...img,\n                            isUserUpload: false\n                        }));\n                }\n            } catch (error) {\n                console.error(\"Unsplash API error:\", error);\n            // Continue without Unsplash images\n            }\n        }\n        // Combine user uploads (first) with Unsplash images\n        const allImages = [\n            ...transformedUserImages,\n            ...unsplashImages\n        ];\n        return c.json({\n            data: allImages\n        });\n    } catch (error) {\n        console.error(\"Error fetching images:\", error);\n        return c.json({\n            error: \"Something went wrong\"\n        }, 400);\n    }\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (app);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/[[...route]]/images.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/api/[[...route]]/projects.ts":
/*!**********************************************!*\
  !*** ./src/app/api/[[...route]]/projects.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/lib/index.mjs\");\n/* harmony import */ var hono__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hono */ \"(rsc)/./node_modules/hono/dist/index.js\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/conditions.js\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/select.js\");\n/* harmony import */ var _hono_auth_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @hono/auth-js */ \"(rsc)/./node_modules/@hono/auth-js/dist/index.mjs\");\n/* harmony import */ var _hono_zod_validator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hono/zod-validator */ \"(rsc)/./node_modules/@hono/zod-validator/dist/esm/index.js\");\n/* harmony import */ var _db_drizzle__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/db/drizzle */ \"(rsc)/./src/db/drizzle.ts\");\n/* harmony import */ var _db_schema__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/db/schema */ \"(rsc)/./src/db/schema.ts\");\n\n\n\n\n\n\n\nconst app = new hono__WEBPACK_IMPORTED_MODULE_0__.Hono().get(\"/templates\", (0,_hono_auth_js__WEBPACK_IMPORTED_MODULE_1__.verifyAuth)(), (0,_hono_zod_validator__WEBPACK_IMPORTED_MODULE_2__.zValidator)(\"query\", zod__WEBPACK_IMPORTED_MODULE_5__.z.object({\n    page: zod__WEBPACK_IMPORTED_MODULE_5__.z.coerce.number(),\n    limit: zod__WEBPACK_IMPORTED_MODULE_5__.z.coerce.number()\n})), async (c)=>{\n    const { page, limit } = c.req.valid(\"query\");\n    const data = await _db_drizzle__WEBPACK_IMPORTED_MODULE_3__.db.select().from(_db_schema__WEBPACK_IMPORTED_MODULE_4__.projects).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_6__.eq)(_db_schema__WEBPACK_IMPORTED_MODULE_4__.projects.isTemplate, true)).limit(limit).offset((page - 1) * limit).orderBy((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.asc)(_db_schema__WEBPACK_IMPORTED_MODULE_4__.projects.isPro), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.desc)(_db_schema__WEBPACK_IMPORTED_MODULE_4__.projects.updatedAt));\n    return c.json({\n        data\n    });\n}).delete(\"/:id\", (0,_hono_auth_js__WEBPACK_IMPORTED_MODULE_1__.verifyAuth)(), (0,_hono_zod_validator__WEBPACK_IMPORTED_MODULE_2__.zValidator)(\"param\", zod__WEBPACK_IMPORTED_MODULE_5__.z.object({\n    id: zod__WEBPACK_IMPORTED_MODULE_5__.z.string()\n})), async (c)=>{\n    const auth = c.get(\"authUser\");\n    const { id } = c.req.valid(\"param\");\n    if (!auth.token?.id) {\n        return c.json({\n            error: \"Unauthorized\"\n        }, 401);\n    }\n    const data = await _db_drizzle__WEBPACK_IMPORTED_MODULE_3__.db.delete(_db_schema__WEBPACK_IMPORTED_MODULE_4__.projects).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_6__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_6__.eq)(_db_schema__WEBPACK_IMPORTED_MODULE_4__.projects.id, id), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_6__.eq)(_db_schema__WEBPACK_IMPORTED_MODULE_4__.projects.userId, auth.token.id))).returning();\n    if (data.length === 0) {\n        return c.json({\n            error: \"Not found\"\n        }, 404);\n    }\n    return c.json({\n        data: {\n            id\n        }\n    });\n}).post(\"/:id/duplicate\", (0,_hono_auth_js__WEBPACK_IMPORTED_MODULE_1__.verifyAuth)(), (0,_hono_zod_validator__WEBPACK_IMPORTED_MODULE_2__.zValidator)(\"param\", zod__WEBPACK_IMPORTED_MODULE_5__.z.object({\n    id: zod__WEBPACK_IMPORTED_MODULE_5__.z.string()\n})), async (c)=>{\n    const auth = c.get(\"authUser\");\n    const { id } = c.req.valid(\"param\");\n    if (!auth.token?.id) {\n        return c.json({\n            error: \"Unauthorized\"\n        }, 401);\n    }\n    const data = await _db_drizzle__WEBPACK_IMPORTED_MODULE_3__.db.select().from(_db_schema__WEBPACK_IMPORTED_MODULE_4__.projects).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_6__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_6__.eq)(_db_schema__WEBPACK_IMPORTED_MODULE_4__.projects.id, id), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_6__.eq)(_db_schema__WEBPACK_IMPORTED_MODULE_4__.projects.userId, auth.token.id)));\n    if (data.length === 0) {\n        return c.json({\n            error: \" Not found\"\n        }, 404);\n    }\n    const project = data[0];\n    const duplicateData = await _db_drizzle__WEBPACK_IMPORTED_MODULE_3__.db.insert(_db_schema__WEBPACK_IMPORTED_MODULE_4__.projects).values({\n        name: `Copy of ${project.name}`,\n        json: project.json,\n        width: project.width,\n        height: project.height,\n        userId: auth.token.id,\n        createdAt: new Date(),\n        updatedAt: new Date()\n    }).returning();\n    return c.json({\n        data: duplicateData[0]\n    });\n}).get(\"/\", (0,_hono_auth_js__WEBPACK_IMPORTED_MODULE_1__.verifyAuth)(), (0,_hono_zod_validator__WEBPACK_IMPORTED_MODULE_2__.zValidator)(\"query\", zod__WEBPACK_IMPORTED_MODULE_5__.z.object({\n    page: zod__WEBPACK_IMPORTED_MODULE_5__.z.coerce.number(),\n    limit: zod__WEBPACK_IMPORTED_MODULE_5__.z.coerce.number()\n})), async (c)=>{\n    const auth = c.get(\"authUser\");\n    const { page, limit } = c.req.valid(\"query\");\n    if (!auth.token?.id) {\n        return c.json({\n            error: \"Unauthorized\"\n        }, 401);\n    }\n    const data = await _db_drizzle__WEBPACK_IMPORTED_MODULE_3__.db.select().from(_db_schema__WEBPACK_IMPORTED_MODULE_4__.projects).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_6__.eq)(_db_schema__WEBPACK_IMPORTED_MODULE_4__.projects.userId, auth.token.id)).limit(limit).offset((page - 1) * limit).orderBy((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.desc)(_db_schema__WEBPACK_IMPORTED_MODULE_4__.projects.updatedAt));\n    return c.json({\n        data,\n        nextPage: data.length === limit ? page + 1 : null\n    });\n}).patch(\"/:id\", (0,_hono_auth_js__WEBPACK_IMPORTED_MODULE_1__.verifyAuth)(), (0,_hono_zod_validator__WEBPACK_IMPORTED_MODULE_2__.zValidator)(\"param\", zod__WEBPACK_IMPORTED_MODULE_5__.z.object({\n    id: zod__WEBPACK_IMPORTED_MODULE_5__.z.string()\n})), (0,_hono_zod_validator__WEBPACK_IMPORTED_MODULE_2__.zValidator)(\"json\", _db_schema__WEBPACK_IMPORTED_MODULE_4__.projectsInsertSchema.omit({\n    id: true,\n    userId: true,\n    createdAt: true,\n    updatedAt: true\n}).partial()), async (c)=>{\n    const auth = c.get(\"authUser\");\n    const { id } = c.req.valid(\"param\");\n    const values = c.req.valid(\"json\");\n    if (!auth.token?.id) {\n        return c.json({\n            error: \"Unauthorized\"\n        }, 401);\n    }\n    const data = await _db_drizzle__WEBPACK_IMPORTED_MODULE_3__.db.update(_db_schema__WEBPACK_IMPORTED_MODULE_4__.projects).set({\n        ...values,\n        updatedAt: new Date()\n    }).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_6__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_6__.eq)(_db_schema__WEBPACK_IMPORTED_MODULE_4__.projects.id, id), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_6__.eq)(_db_schema__WEBPACK_IMPORTED_MODULE_4__.projects.userId, auth.token.id))).returning();\n    if (data.length === 0) {\n        return c.json({\n            error: \"Unauthorized\"\n        }, 401);\n    }\n    return c.json({\n        data: data[0]\n    });\n}).get(\"/:id\", (0,_hono_auth_js__WEBPACK_IMPORTED_MODULE_1__.verifyAuth)(), (0,_hono_zod_validator__WEBPACK_IMPORTED_MODULE_2__.zValidator)(\"param\", zod__WEBPACK_IMPORTED_MODULE_5__.z.object({\n    id: zod__WEBPACK_IMPORTED_MODULE_5__.z.string()\n})), async (c)=>{\n    const auth = c.get(\"authUser\");\n    const { id } = c.req.valid(\"param\");\n    if (!auth.token?.id) {\n        return c.json({\n            error: \"Unauthorized\"\n        }, 401);\n    }\n    const data = await _db_drizzle__WEBPACK_IMPORTED_MODULE_3__.db.select().from(_db_schema__WEBPACK_IMPORTED_MODULE_4__.projects).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_6__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_6__.eq)(_db_schema__WEBPACK_IMPORTED_MODULE_4__.projects.id, id), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_6__.eq)(_db_schema__WEBPACK_IMPORTED_MODULE_4__.projects.userId, auth.token.id)));\n    if (data.length === 0) {\n        return c.json({\n            error: \"Not found\"\n        }, 404);\n    }\n    return c.json({\n        data: data[0]\n    });\n}).post(\"/\", (0,_hono_auth_js__WEBPACK_IMPORTED_MODULE_1__.verifyAuth)(), (0,_hono_zod_validator__WEBPACK_IMPORTED_MODULE_2__.zValidator)(\"json\", _db_schema__WEBPACK_IMPORTED_MODULE_4__.projectsInsertSchema.pick({\n    name: true,\n    json: true,\n    width: true,\n    height: true\n})), async (c)=>{\n    const auth = c.get(\"authUser\");\n    const { name, json, height, width } = c.req.valid(\"json\");\n    if (!auth.token?.id) {\n        return c.json({\n            error: \"Unauthorized\"\n        }, 401);\n    }\n    const data = await _db_drizzle__WEBPACK_IMPORTED_MODULE_3__.db.insert(_db_schema__WEBPACK_IMPORTED_MODULE_4__.projects).values({\n        name,\n        json,\n        width,\n        height,\n        userId: auth.token.id,\n        createdAt: new Date(),\n        updatedAt: new Date()\n    }).returning();\n    if (!data[0]) {\n        return c.json({\n            error: \"Something went wrong\"\n        }, 400);\n    }\n    return c.json({\n        data: data[0]\n    });\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (app);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9bWy4uLnJvdXRlXV0vcHJvamVjdHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQXdCO0FBQ0k7QUFDcUI7QUFDTjtBQUNNO0FBRWY7QUFDMkI7QUFFN0QsTUFBTVcsTUFBTSxJQUFJVixzQ0FBSUEsR0FDakJXLEdBQUcsQ0FDRixjQUNBTix5REFBVUEsSUFDVkMsK0RBQVVBLENBQ1IsU0FDQVAsa0NBQUNBLENBQUNhLE1BQU0sQ0FBQztJQUNQQyxNQUFNZCxrQ0FBQ0EsQ0FBQ2UsTUFBTSxDQUFDQyxNQUFNO0lBQ3JCQyxPQUFPakIsa0NBQUNBLENBQUNlLE1BQU0sQ0FBQ0MsTUFBTTtBQUN4QixLQUVGLE9BQU9FO0lBQ0wsTUFBTSxFQUFFSixJQUFJLEVBQUVHLEtBQUssRUFBRSxHQUFHQyxFQUFFQyxHQUFHLENBQUNDLEtBQUssQ0FBQztJQUVwQyxNQUFNQyxPQUFPLE1BQU1iLDJDQUFFQSxDQUNsQmMsTUFBTSxHQUNOQyxJQUFJLENBQUNkLGdEQUFRQSxFQUNiZSxLQUFLLENBQUN0QiwrQ0FBRUEsQ0FBQ08sZ0RBQVFBLENBQUNnQixVQUFVLEVBQUUsT0FDOUJSLEtBQUssQ0FBQ0EsT0FDTlMsTUFBTSxDQUFDLENBQUNaLE9BQU0sS0FBS0csT0FDbkJVLE9BQU8sQ0FDTnRCLGdEQUFHQSxDQUFDSSxnREFBUUEsQ0FBQ21CLEtBQUssR0FDbEJ4QixpREFBSUEsQ0FBQ0ssZ0RBQVFBLENBQUNvQixTQUFTO0lBRzNCLE9BQU9YLEVBQUVZLElBQUksQ0FBQztRQUFFVDtJQUFLO0FBQ3ZCLEdBRURVLE1BQU0sQ0FDTCxRQUNBekIseURBQVVBLElBQ1ZDLCtEQUFVQSxDQUFDLFNBQVNQLGtDQUFDQSxDQUFDYSxNQUFNLENBQUM7SUFBRW1CLElBQUloQyxrQ0FBQ0EsQ0FBQ2lDLE1BQU07QUFBRyxLQUM5QyxPQUFPZjtJQUNMLE1BQU1nQixPQUFPaEIsRUFBRU4sR0FBRyxDQUFDO0lBQ25CLE1BQU0sRUFBRW9CLEVBQUUsRUFBRSxHQUFHZCxFQUFFQyxHQUFHLENBQUNDLEtBQUssQ0FBQztJQUUzQixJQUFJLENBQUNjLEtBQUtDLEtBQUssRUFBRUgsSUFBSTtRQUNuQixPQUFPZCxFQUFFWSxJQUFJLENBQUM7WUFBRU0sT0FBTztRQUFlLEdBQUc7SUFDM0M7SUFFQSxNQUFNZixPQUFPLE1BQU1iLDJDQUFFQSxDQUNsQnVCLE1BQU0sQ0FBQ3RCLGdEQUFRQSxFQUNmZSxLQUFLLENBQ0pyQixnREFBR0EsQ0FDREQsK0NBQUVBLENBQUNPLGdEQUFRQSxDQUFDdUIsRUFBRSxFQUFFQSxLQUNoQjlCLCtDQUFFQSxDQUFDTyxnREFBUUEsQ0FBQzRCLE1BQU0sRUFBRUgsS0FBS0MsS0FBSyxDQUFDSCxFQUFFLElBR3BDTSxTQUFTO0lBRVosSUFBSWpCLEtBQUtrQixNQUFNLEtBQUssR0FBRztRQUNyQixPQUFPckIsRUFBRVksSUFBSSxDQUFDO1lBQUVNLE9BQU87UUFBWSxHQUFHO0lBQ3hDO0lBRUEsT0FBT2xCLEVBQUVZLElBQUksQ0FBQztRQUFFVCxNQUFNO1lBQUVXO1FBQUc7SUFBRTtBQUMvQixHQUVEUSxJQUFJLENBQ0gsa0JBQ0FsQyx5REFBVUEsSUFDVkMsK0RBQVVBLENBQUMsU0FBU1Asa0NBQUNBLENBQUNhLE1BQU0sQ0FBQztJQUFFbUIsSUFBSWhDLGtDQUFDQSxDQUFDaUMsTUFBTTtBQUFHLEtBQzlDLE9BQU9mO0lBQ0wsTUFBTWdCLE9BQU9oQixFQUFFTixHQUFHLENBQUM7SUFDbkIsTUFBTSxFQUFFb0IsRUFBRSxFQUFFLEdBQUdkLEVBQUVDLEdBQUcsQ0FBQ0MsS0FBSyxDQUFDO0lBRTNCLElBQUksQ0FBQ2MsS0FBS0MsS0FBSyxFQUFFSCxJQUFJO1FBQ25CLE9BQU9kLEVBQUVZLElBQUksQ0FBQztZQUFFTSxPQUFPO1FBQWUsR0FBRztJQUMzQztJQUVBLE1BQU1mLE9BQU8sTUFBTWIsMkNBQUVBLENBQ2xCYyxNQUFNLEdBQ05DLElBQUksQ0FBQ2QsZ0RBQVFBLEVBQ2JlLEtBQUssQ0FDSnJCLGdEQUFHQSxDQUNERCwrQ0FBRUEsQ0FBQ08sZ0RBQVFBLENBQUN1QixFQUFFLEVBQUVBLEtBQ2hCOUIsK0NBQUVBLENBQUNPLGdEQUFRQSxDQUFDNEIsTUFBTSxFQUFFSCxLQUFLQyxLQUFLLENBQUNILEVBQUU7SUFJdkMsSUFBSVgsS0FBS2tCLE1BQU0sS0FBSyxHQUFHO1FBQ3JCLE9BQU9yQixFQUFFWSxJQUFJLENBQUM7WUFBRU0sT0FBTTtRQUFhLEdBQUc7SUFDeEM7SUFFQSxNQUFNSyxVQUFVcEIsSUFBSSxDQUFDLEVBQUU7SUFFdkIsTUFBTXFCLGdCQUFnQixNQUFNbEMsMkNBQUVBLENBQzNCbUMsTUFBTSxDQUFDbEMsZ0RBQVFBLEVBQ2ZtQyxNQUFNLENBQUM7UUFDTkMsTUFBTSxDQUFDLFFBQVEsRUFBRUosUUFBUUksSUFBSSxDQUFDLENBQUM7UUFDL0JmLE1BQU1XLFFBQVFYLElBQUk7UUFDbEJnQixPQUFPTCxRQUFRSyxLQUFLO1FBQ3BCQyxRQUFRTixRQUFRTSxNQUFNO1FBQ3RCVixRQUFRSCxLQUFLQyxLQUFLLENBQUNILEVBQUU7UUFDckJnQixXQUFXLElBQUlDO1FBQ2ZwQixXQUFXLElBQUlvQjtJQUNqQixHQUNDWCxTQUFTO0lBRVosT0FBT3BCLEVBQUVZLElBQUksQ0FBQztRQUFFVCxNQUFNcUIsYUFBYSxDQUFDLEVBQUU7SUFBQztBQUN6QyxHQUVEOUIsR0FBRyxDQUNGLEtBQ0FOLHlEQUFVQSxJQUNWQywrREFBVUEsQ0FDUixTQUNBUCxrQ0FBQ0EsQ0FBQ2EsTUFBTSxDQUFDO0lBQ1BDLE1BQU1kLGtDQUFDQSxDQUFDZSxNQUFNLENBQUNDLE1BQU07SUFDckJDLE9BQU9qQixrQ0FBQ0EsQ0FBQ2UsTUFBTSxDQUFDQyxNQUFNO0FBQ3hCLEtBRUYsT0FBT0U7SUFDTCxNQUFNZ0IsT0FBT2hCLEVBQUVOLEdBQUcsQ0FBQztJQUNuQixNQUFNLEVBQUVFLElBQUksRUFBRUcsS0FBSyxFQUFFLEdBQUdDLEVBQUVDLEdBQUcsQ0FBQ0MsS0FBSyxDQUFDO0lBRXBDLElBQUksQ0FBQ2MsS0FBS0MsS0FBSyxFQUFFSCxJQUFJO1FBQ25CLE9BQU9kLEVBQUVZLElBQUksQ0FBQztZQUFFTSxPQUFPO1FBQWUsR0FBRztJQUMzQztJQUVBLE1BQU1mLE9BQU8sTUFBTWIsMkNBQUVBLENBQ2xCYyxNQUFNLEdBQ05DLElBQUksQ0FBQ2QsZ0RBQVFBLEVBQ2JlLEtBQUssQ0FBQ3RCLCtDQUFFQSxDQUFDTyxnREFBUUEsQ0FBQzRCLE1BQU0sRUFBRUgsS0FBS0MsS0FBSyxDQUFDSCxFQUFFLEdBQ3ZDZixLQUFLLENBQUNBLE9BQ05TLE1BQU0sQ0FBQyxDQUFDWixPQUFPLEtBQUtHLE9BQ3BCVSxPQUFPLENBQUN2QixpREFBSUEsQ0FBQ0ssZ0RBQVFBLENBQUNvQixTQUFTO0lBRWxDLE9BQU9YLEVBQUVZLElBQUksQ0FBQztRQUNaVDtRQUNBNkIsVUFBVTdCLEtBQUtrQixNQUFNLEtBQUt0QixRQUFRSCxPQUFPLElBQUk7SUFDL0M7QUFDRixHQUVEcUMsS0FBSyxDQUNKLFFBQ0E3Qyx5REFBVUEsSUFDVkMsK0RBQVVBLENBQ1IsU0FDQVAsa0NBQUNBLENBQUNhLE1BQU0sQ0FBQztJQUFFbUIsSUFBSWhDLGtDQUFDQSxDQUFDaUMsTUFBTTtBQUFHLEtBRTVCMUIsK0RBQVVBLENBQ1IsUUFDQUcsNERBQW9CQSxDQUNqQjBDLElBQUksQ0FBQztJQUNKcEIsSUFBSTtJQUNKSyxRQUFRO0lBQ1JXLFdBQVc7SUFDWG5CLFdBQVc7QUFDYixHQUNDd0IsT0FBTyxLQUVaLE9BQU9uQztJQUNMLE1BQU1nQixPQUFPaEIsRUFBRU4sR0FBRyxDQUFDO0lBQ25CLE1BQU0sRUFBRW9CLEVBQUUsRUFBRSxHQUFHZCxFQUFFQyxHQUFHLENBQUNDLEtBQUssQ0FBQztJQUMzQixNQUFNd0IsU0FBUzFCLEVBQUVDLEdBQUcsQ0FBQ0MsS0FBSyxDQUFDO0lBRTNCLElBQUksQ0FBQ2MsS0FBS0MsS0FBSyxFQUFFSCxJQUFJO1FBQ25CLE9BQU9kLEVBQUVZLElBQUksQ0FBQztZQUFFTSxPQUFPO1FBQWUsR0FBRztJQUMzQztJQUVBLE1BQU1mLE9BQU8sTUFBTWIsMkNBQUVBLENBQ2xCOEMsTUFBTSxDQUFDN0MsZ0RBQVFBLEVBQ2Y4QyxHQUFHLENBQUM7UUFDSCxHQUFHWCxNQUFNO1FBQ1RmLFdBQVcsSUFBSW9CO0lBQ2pCLEdBQ0N6QixLQUFLLENBQ0pyQixnREFBR0EsQ0FDREQsK0NBQUVBLENBQUNPLGdEQUFRQSxDQUFDdUIsRUFBRSxFQUFFQSxLQUNoQjlCLCtDQUFFQSxDQUFDTyxnREFBUUEsQ0FBQzRCLE1BQU0sRUFBRUgsS0FBS0MsS0FBSyxDQUFDSCxFQUFFLElBR3BDTSxTQUFTO0lBRVosSUFBSWpCLEtBQUtrQixNQUFNLEtBQUssR0FBRztRQUNyQixPQUFPckIsRUFBRVksSUFBSSxDQUFDO1lBQUVNLE9BQU87UUFBZSxHQUFHO0lBQzNDO0lBRUEsT0FBT2xCLEVBQUVZLElBQUksQ0FBQztRQUFFVCxNQUFNQSxJQUFJLENBQUMsRUFBRTtJQUFDO0FBQ2hDLEdBRURULEdBQUcsQ0FDRixRQUNBTix5REFBVUEsSUFDVkMsK0RBQVVBLENBQUMsU0FBU1Asa0NBQUNBLENBQUNhLE1BQU0sQ0FBQztJQUFFbUIsSUFBSWhDLGtDQUFDQSxDQUFDaUMsTUFBTTtBQUFHLEtBQzlDLE9BQU9mO0lBQ0wsTUFBTWdCLE9BQU9oQixFQUFFTixHQUFHLENBQUM7SUFDbkIsTUFBTSxFQUFFb0IsRUFBRSxFQUFFLEdBQUdkLEVBQUVDLEdBQUcsQ0FBQ0MsS0FBSyxDQUFDO0lBRTNCLElBQUksQ0FBQ2MsS0FBS0MsS0FBSyxFQUFFSCxJQUFJO1FBQ25CLE9BQU9kLEVBQUVZLElBQUksQ0FBQztZQUFFTSxPQUFPO1FBQWUsR0FBRztJQUMzQztJQUVBLE1BQU1mLE9BQU8sTUFBTWIsMkNBQUVBLENBQ2xCYyxNQUFNLEdBQ05DLElBQUksQ0FBQ2QsZ0RBQVFBLEVBQ2JlLEtBQUssQ0FDSnJCLGdEQUFHQSxDQUNERCwrQ0FBRUEsQ0FBQ08sZ0RBQVFBLENBQUN1QixFQUFFLEVBQUVBLEtBQ2hCOUIsK0NBQUVBLENBQUNPLGdEQUFRQSxDQUFDNEIsTUFBTSxFQUFFSCxLQUFLQyxLQUFLLENBQUNILEVBQUU7SUFJdkMsSUFBSVgsS0FBS2tCLE1BQU0sS0FBSyxHQUFHO1FBQ3JCLE9BQU9yQixFQUFFWSxJQUFJLENBQUM7WUFBRU0sT0FBTztRQUFZLEdBQUc7SUFDeEM7SUFFQSxPQUFPbEIsRUFBRVksSUFBSSxDQUFDO1FBQUVULE1BQU1BLElBQUksQ0FBQyxFQUFFO0lBQUM7QUFDaEMsR0FFRG1CLElBQUksQ0FDSCxLQUNBbEMseURBQVVBLElBQ1ZDLCtEQUFVQSxDQUNSLFFBQ0FHLDREQUFvQkEsQ0FBQzhDLElBQUksQ0FBQztJQUN4QlgsTUFBTTtJQUNOZixNQUFNO0lBQ05nQixPQUFPO0lBQ1BDLFFBQVE7QUFDVixLQUVGLE9BQU83QjtJQUNMLE1BQU1nQixPQUFPaEIsRUFBRU4sR0FBRyxDQUFDO0lBQ25CLE1BQU0sRUFBRWlDLElBQUksRUFBRWYsSUFBSSxFQUFFaUIsTUFBTSxFQUFFRCxLQUFLLEVBQUUsR0FBRzVCLEVBQUVDLEdBQUcsQ0FBQ0MsS0FBSyxDQUFDO0lBRWxELElBQUksQ0FBQ2MsS0FBS0MsS0FBSyxFQUFFSCxJQUFJO1FBQ25CLE9BQU9kLEVBQUVZLElBQUksQ0FBQztZQUFFTSxPQUFPO1FBQWUsR0FBRztJQUMzQztJQUVBLE1BQU1mLE9BQU8sTUFBTWIsMkNBQUVBLENBQ2xCbUMsTUFBTSxDQUFDbEMsZ0RBQVFBLEVBQ2ZtQyxNQUFNLENBQUM7UUFDTkM7UUFDQWY7UUFDQWdCO1FBQ0FDO1FBQ0FWLFFBQVFILEtBQUtDLEtBQUssQ0FBQ0gsRUFBRTtRQUNyQmdCLFdBQVcsSUFBSUM7UUFDZnBCLFdBQVcsSUFBSW9CO0lBQ2pCLEdBQ0NYLFNBQVM7SUFFWixJQUFJLENBQUNqQixJQUFJLENBQUMsRUFBRSxFQUFFO1FBQ1osT0FBT0gsRUFBRVksSUFBSSxDQUFDO1lBQUVNLE9BQU87UUFBdUIsR0FBRztJQUNuRDtJQUVBLE9BQU9sQixFQUFFWSxJQUFJLENBQUM7UUFBRVQsTUFBTUEsSUFBSSxDQUFDLEVBQUU7SUFBQztBQUNoQztBQUdKLGlFQUFlVixHQUFHQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGhlLWNhbnZhcy8uL3NyYy9hcHAvYXBpL1tbLi4ucm91dGVdXS9wcm9qZWN0cy50cz9lMDNiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHogfSBmcm9tIFwiem9kXCI7XHJcbmltcG9ydCB7IEhvbm8gfSBmcm9tIFwiaG9ub1wiO1xyXG5pbXBvcnQgeyBlcSwgYW5kLCBkZXNjLCBhc2MgfSBmcm9tIFwiZHJpenpsZS1vcm1cIjtcclxuaW1wb3J0IHsgdmVyaWZ5QXV0aCB9IGZyb20gXCJAaG9uby9hdXRoLWpzXCI7XHJcbmltcG9ydCB7IHpWYWxpZGF0b3IgfSBmcm9tIFwiQGhvbm8vem9kLXZhbGlkYXRvclwiO1xyXG5cclxuaW1wb3J0IHsgZGIgfSBmcm9tIFwiQC9kYi9kcml6emxlXCI7XHJcbmltcG9ydCB7IHByb2plY3RzLCBwcm9qZWN0c0luc2VydFNjaGVtYSB9IGZyb20gXCJAL2RiL3NjaGVtYVwiO1xyXG5cclxuY29uc3QgYXBwID0gbmV3IEhvbm8oKVxyXG4gIC5nZXQoXHJcbiAgICBcIi90ZW1wbGF0ZXNcIixcclxuICAgIHZlcmlmeUF1dGgoKSxcclxuICAgIHpWYWxpZGF0b3IoXHJcbiAgICAgIFwicXVlcnlcIixcclxuICAgICAgei5vYmplY3Qoe1xyXG4gICAgICAgIHBhZ2U6IHouY29lcmNlLm51bWJlcigpLFxyXG4gICAgICAgIGxpbWl0OiB6LmNvZXJjZS5udW1iZXIoKSxcclxuICAgICAgfSksXHJcbiAgICApLFxyXG4gICAgYXN5bmMgKGMpID0+IHtcclxuICAgICAgY29uc3QgeyBwYWdlLCBsaW1pdCB9ID0gYy5yZXEudmFsaWQoXCJxdWVyeVwiKTtcclxuXHJcbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCBkYlxyXG4gICAgICAgIC5zZWxlY3QoKVxyXG4gICAgICAgIC5mcm9tKHByb2plY3RzKVxyXG4gICAgICAgIC53aGVyZShlcShwcm9qZWN0cy5pc1RlbXBsYXRlLCB0cnVlKSlcclxuICAgICAgICAubGltaXQobGltaXQpXHJcbiAgICAgICAgLm9mZnNldCgocGFnZSAtMSkgKiBsaW1pdClcclxuICAgICAgICAub3JkZXJCeShcclxuICAgICAgICAgIGFzYyhwcm9qZWN0cy5pc1BybyksXHJcbiAgICAgICAgICBkZXNjKHByb2plY3RzLnVwZGF0ZWRBdCksXHJcbiAgICAgICAgKTtcclxuXHJcbiAgICAgIHJldHVybiBjLmpzb24oeyBkYXRhIH0pO1xyXG4gICAgfSxcclxuICApXHJcbiAgLmRlbGV0ZShcclxuICAgIFwiLzppZFwiLFxyXG4gICAgdmVyaWZ5QXV0aCgpLFxyXG4gICAgelZhbGlkYXRvcihcInBhcmFtXCIsIHoub2JqZWN0KHsgaWQ6IHouc3RyaW5nKCkgfSkpLFxyXG4gICAgYXN5bmMgKGMpID0+IHtcclxuICAgICAgY29uc3QgYXV0aCA9IGMuZ2V0KFwiYXV0aFVzZXJcIik7XHJcbiAgICAgIGNvbnN0IHsgaWQgfSA9IGMucmVxLnZhbGlkKFwicGFyYW1cIik7XHJcblxyXG4gICAgICBpZiAoIWF1dGgudG9rZW4/LmlkKSB7XHJcbiAgICAgICAgcmV0dXJuIGMuanNvbih7IGVycm9yOiBcIlVuYXV0aG9yaXplZFwiIH0sIDQwMSk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCBkYlxyXG4gICAgICAgIC5kZWxldGUocHJvamVjdHMpXHJcbiAgICAgICAgLndoZXJlKFxyXG4gICAgICAgICAgYW5kKFxyXG4gICAgICAgICAgICBlcShwcm9qZWN0cy5pZCwgaWQpLFxyXG4gICAgICAgICAgICBlcShwcm9qZWN0cy51c2VySWQsIGF1dGgudG9rZW4uaWQpLFxyXG4gICAgICAgICAgKSxcclxuICAgICAgICApXHJcbiAgICAgICAgLnJldHVybmluZygpO1xyXG5cclxuICAgICAgaWYgKGRhdGEubGVuZ3RoID09PSAwKSB7XHJcbiAgICAgICAgcmV0dXJuIGMuanNvbih7IGVycm9yOiBcIk5vdCBmb3VuZFwiIH0sIDQwNCk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIHJldHVybiBjLmpzb24oeyBkYXRhOiB7IGlkIH0gfSk7XHJcbiAgICB9LFxyXG4gIClcclxuICAucG9zdChcclxuICAgIFwiLzppZC9kdXBsaWNhdGVcIixcclxuICAgIHZlcmlmeUF1dGgoKSxcclxuICAgIHpWYWxpZGF0b3IoXCJwYXJhbVwiLCB6Lm9iamVjdCh7IGlkOiB6LnN0cmluZygpIH0pKSxcclxuICAgIGFzeW5jIChjKSA9PiB7XHJcbiAgICAgIGNvbnN0IGF1dGggPSBjLmdldChcImF1dGhVc2VyXCIpO1xyXG4gICAgICBjb25zdCB7IGlkIH0gPSBjLnJlcS52YWxpZChcInBhcmFtXCIpO1xyXG5cclxuICAgICAgaWYgKCFhdXRoLnRva2VuPy5pZCkge1xyXG4gICAgICAgIHJldHVybiBjLmpzb24oeyBlcnJvcjogXCJVbmF1dGhvcml6ZWRcIiB9LCA0MDEpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgZGJcclxuICAgICAgICAuc2VsZWN0KClcclxuICAgICAgICAuZnJvbShwcm9qZWN0cylcclxuICAgICAgICAud2hlcmUoXHJcbiAgICAgICAgICBhbmQoXHJcbiAgICAgICAgICAgIGVxKHByb2plY3RzLmlkLCBpZCksXHJcbiAgICAgICAgICAgIGVxKHByb2plY3RzLnVzZXJJZCwgYXV0aC50b2tlbi5pZCksXHJcbiAgICAgICAgICApLFxyXG4gICAgICAgICk7XHJcblxyXG4gICAgICBpZiAoZGF0YS5sZW5ndGggPT09IDApIHtcclxuICAgICAgICByZXR1cm4gYy5qc29uKHsgZXJyb3I6XCIgTm90IGZvdW5kXCIgfSwgNDA0KTtcclxuICAgICAgfVxyXG5cclxuICAgICAgY29uc3QgcHJvamVjdCA9IGRhdGFbMF07XHJcblxyXG4gICAgICBjb25zdCBkdXBsaWNhdGVEYXRhID0gYXdhaXQgZGJcclxuICAgICAgICAuaW5zZXJ0KHByb2plY3RzKVxyXG4gICAgICAgIC52YWx1ZXMoe1xyXG4gICAgICAgICAgbmFtZTogYENvcHkgb2YgJHtwcm9qZWN0Lm5hbWV9YCxcclxuICAgICAgICAgIGpzb246IHByb2plY3QuanNvbixcclxuICAgICAgICAgIHdpZHRoOiBwcm9qZWN0LndpZHRoLFxyXG4gICAgICAgICAgaGVpZ2h0OiBwcm9qZWN0LmhlaWdodCxcclxuICAgICAgICAgIHVzZXJJZDogYXV0aC50b2tlbi5pZCxcclxuICAgICAgICAgIGNyZWF0ZWRBdDogbmV3IERhdGUoKSxcclxuICAgICAgICAgIHVwZGF0ZWRBdDogbmV3IERhdGUoKSxcclxuICAgICAgICB9KVxyXG4gICAgICAgIC5yZXR1cm5pbmcoKTtcclxuXHJcbiAgICAgIHJldHVybiBjLmpzb24oeyBkYXRhOiBkdXBsaWNhdGVEYXRhWzBdIH0pO1xyXG4gICAgfSxcclxuICApXHJcbiAgLmdldChcclxuICAgIFwiL1wiLFxyXG4gICAgdmVyaWZ5QXV0aCgpLFxyXG4gICAgelZhbGlkYXRvcihcclxuICAgICAgXCJxdWVyeVwiLFxyXG4gICAgICB6Lm9iamVjdCh7XHJcbiAgICAgICAgcGFnZTogei5jb2VyY2UubnVtYmVyKCksXHJcbiAgICAgICAgbGltaXQ6IHouY29lcmNlLm51bWJlcigpLFxyXG4gICAgICB9KSxcclxuICAgICksXHJcbiAgICBhc3luYyAoYykgPT4ge1xyXG4gICAgICBjb25zdCBhdXRoID0gYy5nZXQoXCJhdXRoVXNlclwiKTtcclxuICAgICAgY29uc3QgeyBwYWdlLCBsaW1pdCB9ID0gYy5yZXEudmFsaWQoXCJxdWVyeVwiKTtcclxuXHJcbiAgICAgIGlmICghYXV0aC50b2tlbj8uaWQpIHtcclxuICAgICAgICByZXR1cm4gYy5qc29uKHsgZXJyb3I6IFwiVW5hdXRob3JpemVkXCIgfSwgNDAxKTtcclxuICAgICAgfVxyXG5cclxuICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IGRiXHJcbiAgICAgICAgLnNlbGVjdCgpXHJcbiAgICAgICAgLmZyb20ocHJvamVjdHMpXHJcbiAgICAgICAgLndoZXJlKGVxKHByb2plY3RzLnVzZXJJZCwgYXV0aC50b2tlbi5pZCkpXHJcbiAgICAgICAgLmxpbWl0KGxpbWl0KVxyXG4gICAgICAgIC5vZmZzZXQoKHBhZ2UgLSAxKSAqIGxpbWl0KVxyXG4gICAgICAgIC5vcmRlckJ5KGRlc2MocHJvamVjdHMudXBkYXRlZEF0KSlcclxuXHJcbiAgICAgIHJldHVybiBjLmpzb24oe1xyXG4gICAgICAgIGRhdGEsXHJcbiAgICAgICAgbmV4dFBhZ2U6IGRhdGEubGVuZ3RoID09PSBsaW1pdCA/IHBhZ2UgKyAxIDogbnVsbCxcclxuICAgICAgfSk7XHJcbiAgICB9LFxyXG4gIClcclxuICAucGF0Y2goXHJcbiAgICBcIi86aWRcIixcclxuICAgIHZlcmlmeUF1dGgoKSxcclxuICAgIHpWYWxpZGF0b3IoXHJcbiAgICAgIFwicGFyYW1cIixcclxuICAgICAgei5vYmplY3QoeyBpZDogei5zdHJpbmcoKSB9KSxcclxuICAgICksXHJcbiAgICB6VmFsaWRhdG9yKFxyXG4gICAgICBcImpzb25cIixcclxuICAgICAgcHJvamVjdHNJbnNlcnRTY2hlbWFcclxuICAgICAgICAub21pdCh7XHJcbiAgICAgICAgICBpZDogdHJ1ZSxcclxuICAgICAgICAgIHVzZXJJZDogdHJ1ZSxcclxuICAgICAgICAgIGNyZWF0ZWRBdDogdHJ1ZSxcclxuICAgICAgICAgIHVwZGF0ZWRBdDogdHJ1ZSxcclxuICAgICAgICB9KVxyXG4gICAgICAgIC5wYXJ0aWFsKClcclxuICAgICksXHJcbiAgICBhc3luYyAoYykgPT4ge1xyXG4gICAgICBjb25zdCBhdXRoID0gYy5nZXQoXCJhdXRoVXNlclwiKTtcclxuICAgICAgY29uc3QgeyBpZCB9ID0gYy5yZXEudmFsaWQoXCJwYXJhbVwiKTtcclxuICAgICAgY29uc3QgdmFsdWVzID0gYy5yZXEudmFsaWQoXCJqc29uXCIpO1xyXG5cclxuICAgICAgaWYgKCFhdXRoLnRva2VuPy5pZCkge1xyXG4gICAgICAgIHJldHVybiBjLmpzb24oeyBlcnJvcjogXCJVbmF1dGhvcml6ZWRcIiB9LCA0MDEpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgZGJcclxuICAgICAgICAudXBkYXRlKHByb2plY3RzKVxyXG4gICAgICAgIC5zZXQoe1xyXG4gICAgICAgICAgLi4udmFsdWVzLFxyXG4gICAgICAgICAgdXBkYXRlZEF0OiBuZXcgRGF0ZSgpLFxyXG4gICAgICAgIH0pXHJcbiAgICAgICAgLndoZXJlKFxyXG4gICAgICAgICAgYW5kKFxyXG4gICAgICAgICAgICBlcShwcm9qZWN0cy5pZCwgaWQpLFxyXG4gICAgICAgICAgICBlcShwcm9qZWN0cy51c2VySWQsIGF1dGgudG9rZW4uaWQpLFxyXG4gICAgICAgICAgKSxcclxuICAgICAgICApXHJcbiAgICAgICAgLnJldHVybmluZygpO1xyXG5cclxuICAgICAgaWYgKGRhdGEubGVuZ3RoID09PSAwKSB7XHJcbiAgICAgICAgcmV0dXJuIGMuanNvbih7IGVycm9yOiBcIlVuYXV0aG9yaXplZFwiIH0sIDQwMSk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIHJldHVybiBjLmpzb24oeyBkYXRhOiBkYXRhWzBdIH0pO1xyXG4gICAgfSxcclxuICApXHJcbiAgLmdldChcclxuICAgIFwiLzppZFwiLFxyXG4gICAgdmVyaWZ5QXV0aCgpLFxyXG4gICAgelZhbGlkYXRvcihcInBhcmFtXCIsIHoub2JqZWN0KHsgaWQ6IHouc3RyaW5nKCkgfSkpLFxyXG4gICAgYXN5bmMgKGMpID0+IHtcclxuICAgICAgY29uc3QgYXV0aCA9IGMuZ2V0KFwiYXV0aFVzZXJcIik7XHJcbiAgICAgIGNvbnN0IHsgaWQgfSA9IGMucmVxLnZhbGlkKFwicGFyYW1cIik7XHJcblxyXG4gICAgICBpZiAoIWF1dGgudG9rZW4/LmlkKSB7XHJcbiAgICAgICAgcmV0dXJuIGMuanNvbih7IGVycm9yOiBcIlVuYXV0aG9yaXplZFwiIH0sIDQwMSk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCBkYlxyXG4gICAgICAgIC5zZWxlY3QoKVxyXG4gICAgICAgIC5mcm9tKHByb2plY3RzKVxyXG4gICAgICAgIC53aGVyZShcclxuICAgICAgICAgIGFuZChcclxuICAgICAgICAgICAgZXEocHJvamVjdHMuaWQsIGlkKSxcclxuICAgICAgICAgICAgZXEocHJvamVjdHMudXNlcklkLCBhdXRoLnRva2VuLmlkKVxyXG4gICAgICAgICAgKVxyXG4gICAgICAgICk7XHJcblxyXG4gICAgICBpZiAoZGF0YS5sZW5ndGggPT09IDApIHtcclxuICAgICAgICByZXR1cm4gYy5qc29uKHsgZXJyb3I6IFwiTm90IGZvdW5kXCIgfSwgNDA0KTtcclxuICAgICAgfVxyXG5cclxuICAgICAgcmV0dXJuIGMuanNvbih7IGRhdGE6IGRhdGFbMF0gfSk7XHJcbiAgICB9LFxyXG4gIClcclxuICAucG9zdChcclxuICAgIFwiL1wiLFxyXG4gICAgdmVyaWZ5QXV0aCgpLFxyXG4gICAgelZhbGlkYXRvcihcclxuICAgICAgXCJqc29uXCIsXHJcbiAgICAgIHByb2plY3RzSW5zZXJ0U2NoZW1hLnBpY2soe1xyXG4gICAgICAgIG5hbWU6IHRydWUsXHJcbiAgICAgICAganNvbjogdHJ1ZSxcclxuICAgICAgICB3aWR0aDogdHJ1ZSxcclxuICAgICAgICBoZWlnaHQ6IHRydWUsXHJcbiAgICAgIH0pLFxyXG4gICAgKSxcclxuICAgIGFzeW5jIChjKSA9PiB7XHJcbiAgICAgIGNvbnN0IGF1dGggPSBjLmdldChcImF1dGhVc2VyXCIpO1xyXG4gICAgICBjb25zdCB7IG5hbWUsIGpzb24sIGhlaWdodCwgd2lkdGggfSA9IGMucmVxLnZhbGlkKFwianNvblwiKTtcclxuXHJcbiAgICAgIGlmICghYXV0aC50b2tlbj8uaWQpIHtcclxuICAgICAgICByZXR1cm4gYy5qc29uKHsgZXJyb3I6IFwiVW5hdXRob3JpemVkXCIgfSwgNDAxKTtcclxuICAgICAgfVxyXG5cclxuICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IGRiXHJcbiAgICAgICAgLmluc2VydChwcm9qZWN0cylcclxuICAgICAgICAudmFsdWVzKHtcclxuICAgICAgICAgIG5hbWUsXHJcbiAgICAgICAgICBqc29uLFxyXG4gICAgICAgICAgd2lkdGgsXHJcbiAgICAgICAgICBoZWlnaHQsXHJcbiAgICAgICAgICB1c2VySWQ6IGF1dGgudG9rZW4uaWQsXHJcbiAgICAgICAgICBjcmVhdGVkQXQ6IG5ldyBEYXRlKCksXHJcbiAgICAgICAgICB1cGRhdGVkQXQ6IG5ldyBEYXRlKCksXHJcbiAgICAgICAgfSlcclxuICAgICAgICAucmV0dXJuaW5nKCk7XHJcblxyXG4gICAgICBpZiAoIWRhdGFbMF0pIHtcclxuICAgICAgICByZXR1cm4gYy5qc29uKHsgZXJyb3I6IFwiU29tZXRoaW5nIHdlbnQgd3JvbmdcIiB9LCA0MDApO1xyXG4gICAgICB9XHJcblxyXG4gICAgICByZXR1cm4gYy5qc29uKHsgZGF0YTogZGF0YVswXSB9KTtcclxuICAgIH0sXHJcbiAgKTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGFwcDtcclxuIl0sIm5hbWVzIjpbInoiLCJIb25vIiwiZXEiLCJhbmQiLCJkZXNjIiwiYXNjIiwidmVyaWZ5QXV0aCIsInpWYWxpZGF0b3IiLCJkYiIsInByb2plY3RzIiwicHJvamVjdHNJbnNlcnRTY2hlbWEiLCJhcHAiLCJnZXQiLCJvYmplY3QiLCJwYWdlIiwiY29lcmNlIiwibnVtYmVyIiwibGltaXQiLCJjIiwicmVxIiwidmFsaWQiLCJkYXRhIiwic2VsZWN0IiwiZnJvbSIsIndoZXJlIiwiaXNUZW1wbGF0ZSIsIm9mZnNldCIsIm9yZGVyQnkiLCJpc1BybyIsInVwZGF0ZWRBdCIsImpzb24iLCJkZWxldGUiLCJpZCIsInN0cmluZyIsImF1dGgiLCJ0b2tlbiIsImVycm9yIiwidXNlcklkIiwicmV0dXJuaW5nIiwibGVuZ3RoIiwicG9zdCIsInByb2plY3QiLCJkdXBsaWNhdGVEYXRhIiwiaW5zZXJ0IiwidmFsdWVzIiwibmFtZSIsIndpZHRoIiwiaGVpZ2h0IiwiY3JlYXRlZEF0IiwiRGF0ZSIsIm5leHRQYWdlIiwicGF0Y2giLCJvbWl0IiwicGFydGlhbCIsInVwZGF0ZSIsInNldCIsInBpY2siXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/[[...route]]/projects.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/api/[[...route]]/route.ts":
/*!*******************************************!*\
  !*** ./src/app/api/[[...route]]/route.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PATCH: () => (/* binding */ PATCH),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   runtime: () => (/* binding */ runtime)\n/* harmony export */ });\n/* harmony import */ var hono__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hono */ \"(rsc)/./node_modules/hono/dist/index.js\");\n/* harmony import */ var hono_vercel__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! hono/vercel */ \"(rsc)/./node_modules/hono/dist/adapter/vercel/index.js\");\n/* harmony import */ var _hono_auth_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hono/auth-js */ \"(rsc)/./node_modules/@hono/auth-js/dist/index.mjs\");\n/* harmony import */ var _ai__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ai */ \"(rsc)/./src/app/api/[[...route]]/ai.ts\");\n/* harmony import */ var _users__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./users */ \"(rsc)/./src/app/api/[[...route]]/users.ts\");\n/* harmony import */ var _images__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./images */ \"(rsc)/./src/app/api/[[...route]]/images.ts\");\n/* harmony import */ var _projects__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./projects */ \"(rsc)/./src/app/api/[[...route]]/projects.ts\");\n/* harmony import */ var _subscriptions__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./subscriptions */ \"(rsc)/./src/app/api/[[...route]]/subscriptions.ts\");\n/* harmony import */ var _upload__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./upload */ \"(rsc)/./src/app/api/[[...route]]/upload.ts\");\n/* harmony import */ var _auth_config__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/auth.config */ \"(rsc)/./src/auth.config.ts\");\n\n\n\n\n\n\n\n\n\n\n// Revert to \"edge\" if planning on running on the edge\nconst runtime = \"nodejs\";\nfunction getAuthConfig(c) {\n    return {\n        secret: c.env.AUTH_SECRET,\n        ..._auth_config__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    };\n}\nconst app = new hono__WEBPACK_IMPORTED_MODULE_0__.Hono().basePath(\"/api\");\napp.use(\"*\", (0,_hono_auth_js__WEBPACK_IMPORTED_MODULE_2__.initAuthConfig)(getAuthConfig));\nconst routes = app.route(\"/ai\", _ai__WEBPACK_IMPORTED_MODULE_3__[\"default\"]).route(\"/users\", _users__WEBPACK_IMPORTED_MODULE_4__[\"default\"]).route(\"/images\", _images__WEBPACK_IMPORTED_MODULE_5__[\"default\"]).route(\"/projects\", _projects__WEBPACK_IMPORTED_MODULE_6__[\"default\"]).route(\"/subscriptions\", _subscriptions__WEBPACK_IMPORTED_MODULE_7__[\"default\"]).route(\"/upload\", _upload__WEBPACK_IMPORTED_MODULE_8__[\"default\"]);\nconst GET = (0,hono_vercel__WEBPACK_IMPORTED_MODULE_1__.handle)(app);\nconst POST = (0,hono_vercel__WEBPACK_IMPORTED_MODULE_1__.handle)(app);\nconst PATCH = (0,hono_vercel__WEBPACK_IMPORTED_MODULE_1__.handle)(app);\nconst DELETE = (0,hono_vercel__WEBPACK_IMPORTED_MODULE_1__.handle)(app);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/[[...route]]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/api/[[...route]]/subscriptions.ts":
/*!***************************************************!*\
  !*** ./src/app/api/[[...route]]/subscriptions.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var hono__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hono */ \"(rsc)/./node_modules/hono/dist/index.js\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/conditions.js\");\n/* harmony import */ var _hono_auth_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @hono/auth-js */ \"(rsc)/./node_modules/@hono/auth-js/dist/index.mjs\");\n/* harmony import */ var _features_subscriptions_lib__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/features/subscriptions/lib */ \"(rsc)/./src/features/subscriptions/lib.ts\");\n/* harmony import */ var _lib_stripe__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/stripe */ \"(rsc)/./src/lib/stripe.ts\");\n/* harmony import */ var _db_drizzle__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/db/drizzle */ \"(rsc)/./src/db/drizzle.ts\");\n/* harmony import */ var _db_schema__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/db/schema */ \"(rsc)/./src/db/schema.ts\");\n\n\n\n\n\n\n\nconst app = new hono__WEBPACK_IMPORTED_MODULE_0__.Hono().post(\"/billing\", (0,_hono_auth_js__WEBPACK_IMPORTED_MODULE_1__.verifyAuth)(), async (c)=>{\n    const auth = c.get(\"authUser\");\n    if (!auth.token?.id) {\n        return c.json({\n            error: \"Unauthorized\"\n        }, 401);\n    }\n    const [subscription] = await _db_drizzle__WEBPACK_IMPORTED_MODULE_4__.db.select().from(_db_schema__WEBPACK_IMPORTED_MODULE_5__.subscriptions).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_6__.eq)(_db_schema__WEBPACK_IMPORTED_MODULE_5__.subscriptions.userId, auth.token.id));\n    if (!subscription) {\n        return c.json({\n            error: \"No subscription found\"\n        }, 404);\n    }\n    const session = await _lib_stripe__WEBPACK_IMPORTED_MODULE_3__.stripe.billingPortal.sessions.create({\n        customer: subscription.customerId,\n        return_url: `${\"http://localhost:3000\"}`\n    });\n    if (!session.url) {\n        return c.json({\n            error: \"Failed to create session\"\n        }, 400);\n    }\n    return c.json({\n        data: session.url\n    });\n}).get(\"/current\", (0,_hono_auth_js__WEBPACK_IMPORTED_MODULE_1__.verifyAuth)(), async (c)=>{\n    const auth = c.get(\"authUser\");\n    if (!auth.token?.id) {\n        return c.json({\n            error: \"Unauthorized\"\n        }, 401);\n    }\n    const [subscription] = await _db_drizzle__WEBPACK_IMPORTED_MODULE_4__.db.select().from(_db_schema__WEBPACK_IMPORTED_MODULE_5__.subscriptions).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_6__.eq)(_db_schema__WEBPACK_IMPORTED_MODULE_5__.subscriptions.userId, auth.token.id));\n    const active = (0,_features_subscriptions_lib__WEBPACK_IMPORTED_MODULE_2__.checkIsActive)(subscription);\n    return c.json({\n        data: {\n            ...subscription,\n            active\n        }\n    });\n}).post(\"/checkout\", (0,_hono_auth_js__WEBPACK_IMPORTED_MODULE_1__.verifyAuth)(), async (c)=>{\n    const auth = c.get(\"authUser\");\n    if (!auth.token?.id) {\n        return c.json({\n            error: \"Unauthorized\"\n        }, 401);\n    }\n    const session = await _lib_stripe__WEBPACK_IMPORTED_MODULE_3__.stripe.checkout.sessions.create({\n        success_url: `${\"http://localhost:3000\"}?success=1`,\n        cancel_url: `${\"http://localhost:3000\"}?canceled=1`,\n        payment_method_types: [\n            \"card\",\n            \"paypal\"\n        ],\n        mode: \"subscription\",\n        billing_address_collection: \"auto\",\n        customer_email: auth.token.email || \"\",\n        line_items: [\n            {\n                price: process.env.STRIPE_PRICE_ID,\n                quantity: 1\n            }\n        ],\n        metadata: {\n            userId: auth.token.id\n        }\n    });\n    const url = session.url;\n    if (!url) {\n        return c.json({\n            error: \"Failed to create session\"\n        }, 400);\n    }\n    return c.json({\n        data: url\n    });\n}).post(\"/webhook\", async (c)=>{\n    const body = await c.req.text();\n    const signature = c.req.header(\"Stripe-Signature\");\n    let event;\n    try {\n        event = _lib_stripe__WEBPACK_IMPORTED_MODULE_3__.stripe.webhooks.constructEvent(body, signature, process.env.STRIPE_WEBHOOK_SECRET);\n    } catch (error) {\n        return c.json({\n            error: \"Invalid signature\"\n        }, 400);\n    }\n    const session = event.data.object;\n    if (event.type === \"checkout.session.completed\") {\n        const subscription = await _lib_stripe__WEBPACK_IMPORTED_MODULE_3__.stripe.subscriptions.retrieve(session.subscription);\n        if (!session?.metadata?.userId) {\n            return c.json({\n                error: \"Invalid session\"\n            }, 400);\n        }\n        await _db_drizzle__WEBPACK_IMPORTED_MODULE_4__.db.insert(_db_schema__WEBPACK_IMPORTED_MODULE_5__.subscriptions).values({\n            status: subscription.status,\n            userId: session.metadata.userId,\n            subscriptionId: subscription.id,\n            customerId: subscription.customer,\n            priceId: subscription.items.data[0].price.product,\n            currentPeriodEnd: new Date(subscription.current_period_end * 1000),\n            createdAt: new Date(),\n            updatedAt: new Date()\n        });\n    }\n    if (event.type === \"invoice.payment_succeeded\") {\n        const subscription = await _lib_stripe__WEBPACK_IMPORTED_MODULE_3__.stripe.subscriptions.retrieve(session.subscription);\n        if (!session?.metadata?.userId) {\n            return c.json({\n                error: \"Invalid session\"\n            }, 400);\n        }\n        await _db_drizzle__WEBPACK_IMPORTED_MODULE_4__.db.update(_db_schema__WEBPACK_IMPORTED_MODULE_5__.subscriptions).set({\n            status: subscription.status,\n            currentPeriodEnd: new Date(subscription.current_period_end * 1000),\n            updatedAt: new Date()\n        }).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_6__.eq)(_db_schema__WEBPACK_IMPORTED_MODULE_5__.subscriptions.id, subscription.id));\n    }\n    return c.json(null, 200);\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (app);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/[[...route]]/subscriptions.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/api/[[...route]]/upload.ts":
/*!********************************************!*\
  !*** ./src/app/api/[[...route]]/upload.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var hono__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hono */ \"(rsc)/./node_modules/hono/dist/index.js\");\n/* harmony import */ var _hono_auth_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @hono/auth-js */ \"(rsc)/./node_modules/@hono/auth-js/dist/index.mjs\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fs/promises */ \"fs/promises\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fs_promises__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var uuidv4__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! uuidv4 */ \"(rsc)/./node_modules/uuidv4/build/lib/uuidv4.js\");\n/* harmony import */ var _db_drizzle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/db/drizzle */ \"(rsc)/./src/db/drizzle.ts\");\n/* harmony import */ var _db_schema__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/db/schema */ \"(rsc)/./src/db/schema.ts\");\n\n\n\n\n\n\n\nconst app = new hono__WEBPACK_IMPORTED_MODULE_0__.Hono().post(\"/\", (0,_hono_auth_js__WEBPACK_IMPORTED_MODULE_1__.verifyAuth)(), async (c)=>{\n    const auth = c.get(\"authUser\");\n    if (!auth.token?.id) {\n        return c.json({\n            error: \"Unauthorized\"\n        }, 401);\n    }\n    try {\n        const formData = await c.req.formData();\n        const file = formData.get(\"file\");\n        if (!file) {\n            return c.json({\n                error: \"No file provided\"\n            }, 400);\n        }\n        // Validate file type\n        if (!file.type.startsWith(\"image/\")) {\n            return c.json({\n                error: \"File must be an image\"\n            }, 400);\n        }\n        // Validate file size (max 4MB)\n        const maxSize = 4 * 1024 * 1024; // 4MB\n        if (file.size > maxSize) {\n            return c.json({\n                error: \"File size must be less than 4MB\"\n            }, 400);\n        }\n        // Generate unique filename\n        const fileExtension = file.name.split(\".\").pop() || \"jpg\";\n        const filename = `${(0,uuidv4__WEBPACK_IMPORTED_MODULE_4__.uuid)()}.${fileExtension}`;\n        // Convert file to buffer\n        const bytes = await file.arrayBuffer();\n        const buffer = Buffer.from(bytes);\n        // Save file to public/uploads directory\n        const uploadPath = (0,path__WEBPACK_IMPORTED_MODULE_3__.join)(process.cwd(), \"public\", \"uploads\", filename);\n        await (0,fs_promises__WEBPACK_IMPORTED_MODULE_2__.writeFile)(uploadPath, buffer);\n        // Save file metadata to database\n        const url = `/uploads/${filename}`;\n        const [uploadedImage] = await _db_drizzle__WEBPACK_IMPORTED_MODULE_5__.db.insert(_db_schema__WEBPACK_IMPORTED_MODULE_6__.uploadedImages).values({\n            userId: auth.token.id,\n            filename,\n            originalName: file.name,\n            url,\n            size: file.size,\n            mimeType: file.type,\n            createdAt: new Date()\n        }).returning();\n        return c.json({\n            data: {\n                id: uploadedImage.id,\n                url: uploadedImage.url,\n                filename: uploadedImage.filename,\n                originalName: uploadedImage.originalName\n            }\n        });\n    } catch (error) {\n        console.error(\"Upload error:\", error);\n        return c.json({\n            error: \"Failed to upload file\"\n        }, 500);\n    }\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (app);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/[[...route]]/upload.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/api/[[...route]]/users.ts":
/*!*******************************************!*\
  !*** ./src/app/api/[[...route]]/users.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/lib/index.mjs\");\n/* harmony import */ var hono__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hono */ \"(rsc)/./node_modules/hono/dist/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/conditions.js\");\n/* harmony import */ var _hono_zod_validator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hono/zod-validator */ \"(rsc)/./node_modules/@hono/zod-validator/dist/esm/index.js\");\n/* harmony import */ var _db_drizzle__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/db/drizzle */ \"(rsc)/./src/db/drizzle.ts\");\n/* harmony import */ var _db_schema__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/db/schema */ \"(rsc)/./src/db/schema.ts\");\n\n\n\n\n\n\n\nconst app = new hono__WEBPACK_IMPORTED_MODULE_0__.Hono().post(\"/\", (0,_hono_zod_validator__WEBPACK_IMPORTED_MODULE_2__.zValidator)(\"json\", zod__WEBPACK_IMPORTED_MODULE_5__.z.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_5__.z.string(),\n    email: zod__WEBPACK_IMPORTED_MODULE_5__.z.string().email(),\n    password: zod__WEBPACK_IMPORTED_MODULE_5__.z.string().min(3).max(20)\n})), async (c)=>{\n    const { name, email, password } = c.req.valid(\"json\");\n    const hashedPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().hash(password, 12);\n    const query = await _db_drizzle__WEBPACK_IMPORTED_MODULE_3__.db.select().from(_db_schema__WEBPACK_IMPORTED_MODULE_4__.users).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_6__.eq)(_db_schema__WEBPACK_IMPORTED_MODULE_4__.users.email, email));\n    if (query[0]) {\n        return c.json({\n            error: \"Email already in use\"\n        }, 400);\n    }\n    await _db_drizzle__WEBPACK_IMPORTED_MODULE_3__.db.insert(_db_schema__WEBPACK_IMPORTED_MODULE_4__.users).values({\n        email,\n        name,\n        password: hashedPassword\n    });\n    return c.json(null, 200);\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (app);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/[[...route]]/users.ts\n");

/***/ }),

/***/ "(rsc)/./src/auth.config.ts":
/*!****************************!*\
  !*** ./src/auth.config.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/lib/index.mjs\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/conditions.js\");\n/* harmony import */ var next_auth_providers_github__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/github */ \"(rsc)/./node_modules/next-auth/providers/github.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _auth_drizzle_adapter__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @auth/drizzle-adapter */ \"(rsc)/./node_modules/@auth/drizzle-adapter/index.js\");\n/* harmony import */ var _db_drizzle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/db/drizzle */ \"(rsc)/./src/db/drizzle.ts\");\n/* harmony import */ var _db_schema__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/db/schema */ \"(rsc)/./src/db/schema.ts\");\n\n\n\n\n\n\n\n\n\nconst CredentialsSchema = zod__WEBPACK_IMPORTED_MODULE_7__.z.object({\n    email: zod__WEBPACK_IMPORTED_MODULE_7__.z.string().email(),\n    password: zod__WEBPACK_IMPORTED_MODULE_7__.z.string()\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n    adapter: (0,_auth_drizzle_adapter__WEBPACK_IMPORTED_MODULE_4__.DrizzleAdapter)(_db_drizzle__WEBPACK_IMPORTED_MODULE_5__.db),\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_3__[\"default\"])({\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                pasword: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                const validatedFields = CredentialsSchema.safeParse(credentials);\n                if (!validatedFields.success) {\n                    return null;\n                }\n                const { email, password } = validatedFields.data;\n                const query = await _db_drizzle__WEBPACK_IMPORTED_MODULE_5__.db.select().from(_db_schema__WEBPACK_IMPORTED_MODULE_6__.users).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_8__.eq)(_db_schema__WEBPACK_IMPORTED_MODULE_6__.users.email, email));\n                const user = query[0];\n                if (!user || !user.password) {\n                    return null;\n                }\n                const passwordsMatch = await bcryptjs__WEBPACK_IMPORTED_MODULE_0___default().compare(password, user.password);\n                if (!passwordsMatch) {\n                    return null;\n                }\n                return user;\n            }\n        }),\n        next_auth_providers_github__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n        next_auth_providers_google__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n    ],\n    pages: {\n        signIn: \"/sign-in\",\n        error: \"/sign-in\"\n    },\n    session: {\n        strategy: \"jwt\"\n    },\n    callbacks: {\n        session ({ session, token }) {\n            if (token.id) {\n                session.user.id = token.id;\n            }\n            return session;\n        },\n        jwt ({ token, user }) {\n            if (user) {\n                token.id = user.id;\n            }\n            return token;\n        }\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/auth.config.ts\n");

/***/ }),

/***/ "(rsc)/./src/db/drizzle.ts":
/*!***************************!*\
  !*** ./src/db/drizzle.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   db: () => (/* binding */ db)\n/* harmony export */ });\n/* harmony import */ var _neondatabase_serverless__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @neondatabase/serverless */ \"(rsc)/./node_modules/@neondatabase/serverless/index.mjs\");\n/* harmony import */ var drizzle_orm_neon_http__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! drizzle-orm/neon-http */ \"(rsc)/./node_modules/drizzle-orm/neon-http/driver.js\");\n\n\nconst sql = (0,_neondatabase_serverless__WEBPACK_IMPORTED_MODULE_0__.neon)(process.env.DATABASE_URL);\nconst db = (0,drizzle_orm_neon_http__WEBPACK_IMPORTED_MODULE_1__.drizzle)(sql);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvZGIvZHJpenpsZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBZ0Q7QUFDQTtBQUVoRCxNQUFNRSxNQUFNRiw4REFBSUEsQ0FBQ0csUUFBUUMsR0FBRyxDQUFDQyxZQUFZO0FBQ2xDLE1BQU1DLEtBQUtMLDhEQUFPQSxDQUFDQyxLQUFLIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGhlLWNhbnZhcy8uL3NyYy9kYi9kcml6emxlLnRzPzg4MDgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgbmVvbiB9IGZyb20gXCJAbmVvbmRhdGFiYXNlL3NlcnZlcmxlc3NcIjtcclxuaW1wb3J0IHsgZHJpenpsZSB9IGZyb20gXCJkcml6emxlLW9ybS9uZW9uLWh0dHBcIjtcclxuXHJcbmNvbnN0IHNxbCA9IG5lb24ocHJvY2Vzcy5lbnYuREFUQUJBU0VfVVJMISk7XHJcbmV4cG9ydCBjb25zdCBkYiA9IGRyaXp6bGUoc3FsKTtcclxuIl0sIm5hbWVzIjpbIm5lb24iLCJkcml6emxlIiwic3FsIiwicHJvY2VzcyIsImVudiIsIkRBVEFCQVNFX1VSTCIsImRiIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/db/drizzle.ts\n");

/***/ }),

/***/ "(rsc)/./src/db/schema.ts":
/*!**************************!*\
  !*** ./src/db/schema.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   accounts: () => (/* binding */ accounts),\n/* harmony export */   authenticators: () => (/* binding */ authenticators),\n/* harmony export */   projects: () => (/* binding */ projects),\n/* harmony export */   projectsInsertSchema: () => (/* binding */ projectsInsertSchema),\n/* harmony export */   projectsRelations: () => (/* binding */ projectsRelations),\n/* harmony export */   sessions: () => (/* binding */ sessions),\n/* harmony export */   subscriptions: () => (/* binding */ subscriptions),\n/* harmony export */   uploadedImages: () => (/* binding */ uploadedImages),\n/* harmony export */   uploadedImagesInsertSchema: () => (/* binding */ uploadedImagesInsertSchema),\n/* harmony export */   uploadedImagesRelations: () => (/* binding */ uploadedImagesRelations),\n/* harmony export */   users: () => (/* binding */ users),\n/* harmony export */   usersRelations: () => (/* binding */ usersRelations),\n/* harmony export */   verificationTokens: () => (/* binding */ verificationTokens)\n/* harmony export */ });\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/relations.js\");\n/* harmony import */ var drizzle_zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! drizzle-zod */ \"(rsc)/./node_modules/drizzle-zod/index.mjs\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/table.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/text.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/timestamp.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/integer.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/primary-keys.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/boolean.js\");\n\n\n\nconst users = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)(\"user\", {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"id\").primaryKey().$defaultFn(()=>crypto.randomUUID()),\n    name: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"name\"),\n    email: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"email\").notNull(),\n    emailVerified: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)(\"emailVerified\", {\n        mode: \"date\"\n    }),\n    image: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"image\"),\n    password: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"password\")\n});\nconst usersRelations = (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.relations)(users, ({ many })=>({\n        projects: many(projects)\n    }));\nconst accounts = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)(\"account\", {\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"userId\").notNull().references(()=>users.id, {\n        onDelete: \"cascade\"\n    }),\n    type: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"type\").$type().notNull(),\n    provider: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"provider\").notNull(),\n    providerAccountId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"providerAccountId\").notNull(),\n    refresh_token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"refresh_token\"),\n    access_token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"access_token\"),\n    expires_at: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.integer)(\"expires_at\"),\n    token_type: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"token_type\"),\n    scope: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"scope\"),\n    id_token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"id_token\"),\n    session_state: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"session_state\")\n}, (account)=>({\n        compoundKey: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.primaryKey)({\n            columns: [\n                account.provider,\n                account.providerAccountId\n            ]\n        })\n    }));\nconst sessions = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)(\"session\", {\n    sessionToken: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"sessionToken\").primaryKey(),\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"userId\").notNull().references(()=>users.id, {\n        onDelete: \"cascade\"\n    }),\n    expires: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)(\"expires\", {\n        mode: \"date\"\n    }).notNull()\n});\nconst verificationTokens = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)(\"verificationToken\", {\n    identifier: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"identifier\").notNull(),\n    token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"token\").notNull(),\n    expires: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)(\"expires\", {\n        mode: \"date\"\n    }).notNull()\n}, (verificationToken)=>({\n        compositePk: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.primaryKey)({\n            columns: [\n                verificationToken.identifier,\n                verificationToken.token\n            ]\n        })\n    }));\nconst authenticators = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)(\"authenticator\", {\n    credentialID: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"credentialID\").notNull().unique(),\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"userId\").notNull().references(()=>users.id, {\n        onDelete: \"cascade\"\n    }),\n    providerAccountId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"providerAccountId\").notNull(),\n    credentialPublicKey: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"credentialPublicKey\").notNull(),\n    counter: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.integer)(\"counter\").notNull(),\n    credentialDeviceType: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"credentialDeviceType\").notNull(),\n    credentialBackedUp: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__.boolean)(\"credentialBackedUp\").notNull(),\n    transports: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"transports\")\n}, (authenticator)=>({\n        compositePK: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.primaryKey)({\n            columns: [\n                authenticator.userId,\n                authenticator.credentialID\n            ]\n        })\n    }));\nconst projects = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)(\"project\", {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"id\").primaryKey().$defaultFn(()=>crypto.randomUUID()),\n    name: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"name\").notNull(),\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"userId\").notNull().references(()=>users.id, {\n        onDelete: \"cascade\"\n    }),\n    json: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"json\").notNull(),\n    height: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.integer)(\"height\").notNull(),\n    width: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.integer)(\"width\").notNull(),\n    thumbnailUrl: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"thumbnailUrl\"),\n    isTemplate: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__.boolean)(\"isTemplate\"),\n    isPro: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__.boolean)(\"isPro\"),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)(\"createdAt\", {\n        mode: \"date\"\n    }).notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)(\"updatedAt\", {\n        mode: \"date\"\n    }).notNull()\n});\nconst projectsRelations = (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.relations)(projects, ({ one })=>({\n        user: one(users, {\n            fields: [\n                projects.userId\n            ],\n            references: [\n                users.id\n            ]\n        })\n    }));\nconst projectsInsertSchema = (0,drizzle_zod__WEBPACK_IMPORTED_MODULE_0__.createInsertSchema)(projects);\nconst subscriptions = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)(\"subscription\", {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"id\").primaryKey().$defaultFn(()=>crypto.randomUUID()),\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"userId\").notNull().references(()=>users.id, {\n        onDelete: \"cascade\"\n    }),\n    subscriptionId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"subscriptionId\").notNull(),\n    customerId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"customerId\").notNull(),\n    priceId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"priceId\").notNull(),\n    status: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"status\").notNull(),\n    currentPeriodEnd: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)(\"currentPeriodEnd\", {\n        mode: \"date\"\n    }),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)(\"createdAt\", {\n        mode: \"date\"\n    }).notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)(\"updatedAt\", {\n        mode: \"date\"\n    }).notNull()\n});\nconst uploadedImages = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)(\"uploaded_image\", {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"id\").primaryKey().$defaultFn(()=>crypto.randomUUID()),\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"userId\").notNull().references(()=>users.id, {\n        onDelete: \"cascade\"\n    }),\n    filename: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"filename\").notNull(),\n    originalName: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"originalName\").notNull(),\n    url: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"url\").notNull(),\n    size: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.integer)(\"size\").notNull(),\n    mimeType: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)(\"mimeType\").notNull(),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)(\"createdAt\", {\n        mode: \"date\"\n    }).notNull()\n});\nconst uploadedImagesRelations = (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.relations)(uploadedImages, ({ one })=>({\n        user: one(users, {\n            fields: [\n                uploadedImages.userId\n            ],\n            references: [\n                users.id\n            ]\n        })\n    }));\nconst uploadedImagesInsertSchema = (0,drizzle_zod__WEBPACK_IMPORTED_MODULE_0__.createInsertSchema)(uploadedImages);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvZGIvc2NoZW1hLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUF3QztBQUNTO0FBUXJCO0FBR3JCLE1BQU1RLFFBQVFKLDREQUFPQSxDQUFDLFFBQVE7SUFDbkNLLElBQUlKLHlEQUFJQSxDQUFDLE1BQ05DLFVBQVUsR0FDVkksVUFBVSxDQUFDLElBQU1DLE9BQU9DLFVBQVU7SUFDckNDLE1BQU1SLHlEQUFJQSxDQUFDO0lBQ1hTLE9BQU9ULHlEQUFJQSxDQUFDLFNBQVNVLE9BQU87SUFDNUJDLGVBQWViLDhEQUFTQSxDQUFDLGlCQUFpQjtRQUFFYyxNQUFNO0lBQU87SUFDekRDLE9BQU9iLHlEQUFJQSxDQUFDO0lBQ1pjLFVBQVVkLHlEQUFJQSxDQUFDO0FBQ2pCLEdBQUc7QUFFSSxNQUFNZSxpQkFBaUJwQixzREFBU0EsQ0FBQ1EsT0FBTyxDQUFDLEVBQUVhLElBQUksRUFBRSxHQUFNO1FBQzVEQyxVQUFVRCxLQUFLQztJQUNqQixJQUFJO0FBRUcsTUFBTUMsV0FBV25CLDREQUFPQSxDQUM3QixXQUNBO0lBQ0VvQixRQUFRbkIseURBQUlBLENBQUMsVUFDVlUsT0FBTyxHQUNQVSxVQUFVLENBQUMsSUFBTWpCLE1BQU1DLEVBQUUsRUFBRTtRQUFFaUIsVUFBVTtJQUFVO0lBQ3BEQyxNQUFNdEIseURBQUlBLENBQUMsUUFBUXVCLEtBQUssR0FBdUJiLE9BQU87SUFDdERjLFVBQVV4Qix5REFBSUEsQ0FBQyxZQUFZVSxPQUFPO0lBQ2xDZSxtQkFBbUJ6Qix5REFBSUEsQ0FBQyxxQkFBcUJVLE9BQU87SUFDcERnQixlQUFlMUIseURBQUlBLENBQUM7SUFDcEIyQixjQUFjM0IseURBQUlBLENBQUM7SUFDbkI0QixZQUFZMUIsNERBQU9BLENBQUM7SUFDcEIyQixZQUFZN0IseURBQUlBLENBQUM7SUFDakI4QixPQUFPOUIseURBQUlBLENBQUM7SUFDWitCLFVBQVUvQix5REFBSUEsQ0FBQztJQUNmZ0MsZUFBZWhDLHlEQUFJQSxDQUFDO0FBQ3RCLEdBQ0EsQ0FBQ2lDLFVBQWE7UUFDWkMsYUFBYWpDLCtEQUFVQSxDQUFDO1lBQ3RCa0MsU0FBUztnQkFBQ0YsUUFBUVQsUUFBUTtnQkFBRVMsUUFBUVIsaUJBQWlCO2FBQUM7UUFDeEQ7SUFDRixJQUNEO0FBRU0sTUFBTVcsV0FBV3JDLDREQUFPQSxDQUFDLFdBQVc7SUFDekNzQyxjQUFjckMseURBQUlBLENBQUMsZ0JBQWdCQyxVQUFVO0lBQzdDa0IsUUFBUW5CLHlEQUFJQSxDQUFDLFVBQ1ZVLE9BQU8sR0FDUFUsVUFBVSxDQUFDLElBQU1qQixNQUFNQyxFQUFFLEVBQUU7UUFBRWlCLFVBQVU7SUFBVTtJQUNwRGlCLFNBQVN4Qyw4REFBU0EsQ0FBQyxXQUFXO1FBQUVjLE1BQU07SUFBTyxHQUFHRixPQUFPO0FBQ3pELEdBQUU7QUFFSyxNQUFNNkIscUJBQXFCeEMsNERBQU9BLENBQ3ZDLHFCQUNBO0lBQ0V5QyxZQUFZeEMseURBQUlBLENBQUMsY0FBY1UsT0FBTztJQUN0QytCLE9BQU96Qyx5REFBSUEsQ0FBQyxTQUFTVSxPQUFPO0lBQzVCNEIsU0FBU3hDLDhEQUFTQSxDQUFDLFdBQVc7UUFBRWMsTUFBTTtJQUFPLEdBQUdGLE9BQU87QUFDekQsR0FDQSxDQUFDZ0Msb0JBQXVCO1FBQ3RCQyxhQUFhMUMsK0RBQVVBLENBQUM7WUFDdEJrQyxTQUFTO2dCQUFDTyxrQkFBa0JGLFVBQVU7Z0JBQUVFLGtCQUFrQkQsS0FBSzthQUFDO1FBQ2xFO0lBQ0YsSUFDRDtBQUVNLE1BQU1HLGlCQUFpQjdDLDREQUFPQSxDQUNuQyxpQkFDQTtJQUNFOEMsY0FBYzdDLHlEQUFJQSxDQUFDLGdCQUFnQlUsT0FBTyxHQUFHb0MsTUFBTTtJQUNuRDNCLFFBQVFuQix5REFBSUEsQ0FBQyxVQUNWVSxPQUFPLEdBQ1BVLFVBQVUsQ0FBQyxJQUFNakIsTUFBTUMsRUFBRSxFQUFFO1FBQUVpQixVQUFVO0lBQVU7SUFDcERJLG1CQUFtQnpCLHlEQUFJQSxDQUFDLHFCQUFxQlUsT0FBTztJQUNwRHFDLHFCQUFxQi9DLHlEQUFJQSxDQUFDLHVCQUF1QlUsT0FBTztJQUN4RHNDLFNBQVM5Qyw0REFBT0EsQ0FBQyxXQUFXUSxPQUFPO0lBQ25DdUMsc0JBQXNCakQseURBQUlBLENBQUMsd0JBQXdCVSxPQUFPO0lBQzFEd0Msb0JBQW9CckQsNERBQU9BLENBQUMsc0JBQXNCYSxPQUFPO0lBQ3pEeUMsWUFBWW5ELHlEQUFJQSxDQUFDO0FBQ25CLEdBQ0EsQ0FBQ29ELGdCQUFtQjtRQUNsQkMsYUFBYXBELCtEQUFVQSxDQUFDO1lBQ3RCa0MsU0FBUztnQkFBQ2lCLGNBQWNqQyxNQUFNO2dCQUFFaUMsY0FBY1AsWUFBWTthQUFDO1FBQzdEO0lBQ0YsSUFDRDtBQUVNLE1BQU01QixXQUFXbEIsNERBQU9BLENBQUMsV0FBVztJQUN6Q0ssSUFBSUoseURBQUlBLENBQUMsTUFDTkMsVUFBVSxHQUNWSSxVQUFVLENBQUMsSUFBTUMsT0FBT0MsVUFBVTtJQUNyQ0MsTUFBTVIseURBQUlBLENBQUMsUUFBUVUsT0FBTztJQUMxQlMsUUFBUW5CLHlEQUFJQSxDQUFDLFVBQ1ZVLE9BQU8sR0FDUFUsVUFBVSxDQUFDLElBQU1qQixNQUFNQyxFQUFFLEVBQUU7UUFDMUJpQixVQUFVO0lBQ1o7SUFDRmlDLE1BQU10RCx5REFBSUEsQ0FBQyxRQUFRVSxPQUFPO0lBQzFCNkMsUUFBUXJELDREQUFPQSxDQUFDLFVBQVVRLE9BQU87SUFDakM4QyxPQUFPdEQsNERBQU9BLENBQUMsU0FBU1EsT0FBTztJQUMvQitDLGNBQWN6RCx5REFBSUEsQ0FBQztJQUNuQjBELFlBQVk3RCw0REFBT0EsQ0FBQztJQUNwQjhELE9BQU85RCw0REFBT0EsQ0FBQztJQUNmK0QsV0FBVzlELDhEQUFTQSxDQUFDLGFBQWE7UUFBRWMsTUFBTTtJQUFPLEdBQUdGLE9BQU87SUFDM0RtRCxXQUFXL0QsOERBQVNBLENBQUMsYUFBYTtRQUFFYyxNQUFNO0lBQU8sR0FBR0YsT0FBTztBQUM3RCxHQUFHO0FBRUksTUFBTW9ELG9CQUFvQm5FLHNEQUFTQSxDQUFDc0IsVUFBVSxDQUFDLEVBQUU4QyxHQUFHLEVBQUUsR0FBTTtRQUNqRUMsTUFBTUQsSUFBSTVELE9BQU87WUFDZjhELFFBQVE7Z0JBQUNoRCxTQUFTRSxNQUFNO2FBQUM7WUFDekJDLFlBQVk7Z0JBQUNqQixNQUFNQyxFQUFFO2FBQUM7UUFDeEI7SUFDRixJQUFJO0FBRUcsTUFBTThELHVCQUF1QnRFLCtEQUFrQkEsQ0FBQ3FCLFVBQVU7QUFFMUQsTUFBTWtELGdCQUFnQnBFLDREQUFPQSxDQUFDLGdCQUFnQjtJQUNuREssSUFBSUoseURBQUlBLENBQUMsTUFDTkMsVUFBVSxHQUNWSSxVQUFVLENBQUMsSUFBTUMsT0FBT0MsVUFBVTtJQUNyQ1ksUUFBUW5CLHlEQUFJQSxDQUFDLFVBQ1ZVLE9BQU8sR0FDUFUsVUFBVSxDQUFDLElBQU1qQixNQUFNQyxFQUFFLEVBQUU7UUFDMUJpQixVQUFVO0lBQ1o7SUFDRitDLGdCQUFnQnBFLHlEQUFJQSxDQUFDLGtCQUFrQlUsT0FBTztJQUM5QzJELFlBQVlyRSx5REFBSUEsQ0FBQyxjQUFjVSxPQUFPO0lBQ3RDNEQsU0FBU3RFLHlEQUFJQSxDQUFDLFdBQVdVLE9BQU87SUFDaEM2RCxRQUFRdkUseURBQUlBLENBQUMsVUFBVVUsT0FBTztJQUM5QjhELGtCQUFrQjFFLDhEQUFTQSxDQUFDLG9CQUFvQjtRQUFFYyxNQUFNO0lBQU87SUFDL0RnRCxXQUFXOUQsOERBQVNBLENBQUMsYUFBYTtRQUFFYyxNQUFNO0lBQU8sR0FBR0YsT0FBTztJQUMzRG1ELFdBQVcvRCw4REFBU0EsQ0FBQyxhQUFhO1FBQUVjLE1BQU07SUFBTyxHQUFHRixPQUFPO0FBQzdELEdBQUc7QUFFSSxNQUFNK0QsaUJBQWlCMUUsNERBQU9BLENBQUMsa0JBQWtCO0lBQ3RESyxJQUFJSix5REFBSUEsQ0FBQyxNQUNOQyxVQUFVLEdBQ1ZJLFVBQVUsQ0FBQyxJQUFNQyxPQUFPQyxVQUFVO0lBQ3JDWSxRQUFRbkIseURBQUlBLENBQUMsVUFDVlUsT0FBTyxHQUNQVSxVQUFVLENBQUMsSUFBTWpCLE1BQU1DLEVBQUUsRUFBRTtRQUMxQmlCLFVBQVU7SUFDWjtJQUNGcUQsVUFBVTFFLHlEQUFJQSxDQUFDLFlBQVlVLE9BQU87SUFDbENpRSxjQUFjM0UseURBQUlBLENBQUMsZ0JBQWdCVSxPQUFPO0lBQzFDa0UsS0FBSzVFLHlEQUFJQSxDQUFDLE9BQU9VLE9BQU87SUFDeEJtRSxNQUFNM0UsNERBQU9BLENBQUMsUUFBUVEsT0FBTztJQUM3Qm9FLFVBQVU5RSx5REFBSUEsQ0FBQyxZQUFZVSxPQUFPO0lBQ2xDa0QsV0FBVzlELDhEQUFTQSxDQUFDLGFBQWE7UUFBRWMsTUFBTTtJQUFPLEdBQUdGLE9BQU87QUFDN0QsR0FBRztBQUVJLE1BQU1xRSwwQkFBMEJwRixzREFBU0EsQ0FBQzhFLGdCQUFnQixDQUFDLEVBQUVWLEdBQUcsRUFBRSxHQUFNO1FBQzdFQyxNQUFNRCxJQUFJNUQsT0FBTztZQUNmOEQsUUFBUTtnQkFBQ1EsZUFBZXRELE1BQU07YUFBQztZQUMvQkMsWUFBWTtnQkFBQ2pCLE1BQU1DLEVBQUU7YUFBQztRQUN4QjtJQUNGLElBQUk7QUFFRyxNQUFNNEUsNkJBQTZCcEYsK0RBQWtCQSxDQUFDNkUsZ0JBQWdCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGhlLWNhbnZhcy8uL3NyYy9kYi9zY2hlbWEudHM/ZjAxNCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyByZWxhdGlvbnMgfSBmcm9tIFwiZHJpenpsZS1vcm1cIjtcclxuaW1wb3J0IHsgY3JlYXRlSW5zZXJ0U2NoZW1hIH0gZnJvbSBcImRyaXp6bGUtem9kXCI7XHJcbmltcG9ydCB7XHJcbiAgYm9vbGVhbixcclxuICB0aW1lc3RhbXAsXHJcbiAgcGdUYWJsZSxcclxuICB0ZXh0LFxyXG4gIHByaW1hcnlLZXksXHJcbiAgaW50ZWdlcixcclxufSBmcm9tIFwiZHJpenpsZS1vcm0vcGctY29yZVwiXHJcbmltcG9ydCB0eXBlIHsgQWRhcHRlckFjY291bnRUeXBlIH0gZnJvbSBcIm5leHQtYXV0aC9hZGFwdGVyc1wiXHJcbiBcclxuZXhwb3J0IGNvbnN0IHVzZXJzID0gcGdUYWJsZShcInVzZXJcIiwge1xyXG4gIGlkOiB0ZXh0KFwiaWRcIilcclxuICAgIC5wcmltYXJ5S2V5KClcclxuICAgIC4kZGVmYXVsdEZuKCgpID0+IGNyeXB0by5yYW5kb21VVUlEKCkpLFxyXG4gIG5hbWU6IHRleHQoXCJuYW1lXCIpLFxyXG4gIGVtYWlsOiB0ZXh0KFwiZW1haWxcIikubm90TnVsbCgpLFxyXG4gIGVtYWlsVmVyaWZpZWQ6IHRpbWVzdGFtcChcImVtYWlsVmVyaWZpZWRcIiwgeyBtb2RlOiBcImRhdGVcIiB9KSxcclxuICBpbWFnZTogdGV4dChcImltYWdlXCIpLFxyXG4gIHBhc3N3b3JkOiB0ZXh0KFwicGFzc3dvcmRcIiksIFxyXG59KTtcclxuXHJcbmV4cG9ydCBjb25zdCB1c2Vyc1JlbGF0aW9ucyA9IHJlbGF0aW9ucyh1c2VycywgKHsgbWFueSB9KSA9PiAoe1xyXG4gIHByb2plY3RzOiBtYW55KHByb2plY3RzKSxcclxufSkpO1xyXG5cclxuZXhwb3J0IGNvbnN0IGFjY291bnRzID0gcGdUYWJsZShcclxuICBcImFjY291bnRcIixcclxuICB7XHJcbiAgICB1c2VySWQ6IHRleHQoXCJ1c2VySWRcIilcclxuICAgICAgLm5vdE51bGwoKVxyXG4gICAgICAucmVmZXJlbmNlcygoKSA9PiB1c2Vycy5pZCwgeyBvbkRlbGV0ZTogXCJjYXNjYWRlXCIgfSksXHJcbiAgICB0eXBlOiB0ZXh0KFwidHlwZVwiKS4kdHlwZTxBZGFwdGVyQWNjb3VudFR5cGU+KCkubm90TnVsbCgpLFxyXG4gICAgcHJvdmlkZXI6IHRleHQoXCJwcm92aWRlclwiKS5ub3ROdWxsKCksXHJcbiAgICBwcm92aWRlckFjY291bnRJZDogdGV4dChcInByb3ZpZGVyQWNjb3VudElkXCIpLm5vdE51bGwoKSxcclxuICAgIHJlZnJlc2hfdG9rZW46IHRleHQoXCJyZWZyZXNoX3Rva2VuXCIpLFxyXG4gICAgYWNjZXNzX3Rva2VuOiB0ZXh0KFwiYWNjZXNzX3Rva2VuXCIpLFxyXG4gICAgZXhwaXJlc19hdDogaW50ZWdlcihcImV4cGlyZXNfYXRcIiksXHJcbiAgICB0b2tlbl90eXBlOiB0ZXh0KFwidG9rZW5fdHlwZVwiKSxcclxuICAgIHNjb3BlOiB0ZXh0KFwic2NvcGVcIiksXHJcbiAgICBpZF90b2tlbjogdGV4dChcImlkX3Rva2VuXCIpLFxyXG4gICAgc2Vzc2lvbl9zdGF0ZTogdGV4dChcInNlc3Npb25fc3RhdGVcIiksXHJcbiAgfSxcclxuICAoYWNjb3VudCkgPT4gKHtcclxuICAgIGNvbXBvdW5kS2V5OiBwcmltYXJ5S2V5KHtcclxuICAgICAgY29sdW1uczogW2FjY291bnQucHJvdmlkZXIsIGFjY291bnQucHJvdmlkZXJBY2NvdW50SWRdLFxyXG4gICAgfSksXHJcbiAgfSlcclxuKVxyXG4gXHJcbmV4cG9ydCBjb25zdCBzZXNzaW9ucyA9IHBnVGFibGUoXCJzZXNzaW9uXCIsIHtcclxuICBzZXNzaW9uVG9rZW46IHRleHQoXCJzZXNzaW9uVG9rZW5cIikucHJpbWFyeUtleSgpLFxyXG4gIHVzZXJJZDogdGV4dChcInVzZXJJZFwiKVxyXG4gICAgLm5vdE51bGwoKVxyXG4gICAgLnJlZmVyZW5jZXMoKCkgPT4gdXNlcnMuaWQsIHsgb25EZWxldGU6IFwiY2FzY2FkZVwiIH0pLFxyXG4gIGV4cGlyZXM6IHRpbWVzdGFtcChcImV4cGlyZXNcIiwgeyBtb2RlOiBcImRhdGVcIiB9KS5ub3ROdWxsKCksXHJcbn0pXHJcbiBcclxuZXhwb3J0IGNvbnN0IHZlcmlmaWNhdGlvblRva2VucyA9IHBnVGFibGUoXHJcbiAgXCJ2ZXJpZmljYXRpb25Ub2tlblwiLFxyXG4gIHtcclxuICAgIGlkZW50aWZpZXI6IHRleHQoXCJpZGVudGlmaWVyXCIpLm5vdE51bGwoKSxcclxuICAgIHRva2VuOiB0ZXh0KFwidG9rZW5cIikubm90TnVsbCgpLFxyXG4gICAgZXhwaXJlczogdGltZXN0YW1wKFwiZXhwaXJlc1wiLCB7IG1vZGU6IFwiZGF0ZVwiIH0pLm5vdE51bGwoKSxcclxuICB9LFxyXG4gICh2ZXJpZmljYXRpb25Ub2tlbikgPT4gKHtcclxuICAgIGNvbXBvc2l0ZVBrOiBwcmltYXJ5S2V5KHtcclxuICAgICAgY29sdW1uczogW3ZlcmlmaWNhdGlvblRva2VuLmlkZW50aWZpZXIsIHZlcmlmaWNhdGlvblRva2VuLnRva2VuXSxcclxuICAgIH0pLFxyXG4gIH0pXHJcbilcclxuIFxyXG5leHBvcnQgY29uc3QgYXV0aGVudGljYXRvcnMgPSBwZ1RhYmxlKFxyXG4gIFwiYXV0aGVudGljYXRvclwiLFxyXG4gIHtcclxuICAgIGNyZWRlbnRpYWxJRDogdGV4dChcImNyZWRlbnRpYWxJRFwiKS5ub3ROdWxsKCkudW5pcXVlKCksXHJcbiAgICB1c2VySWQ6IHRleHQoXCJ1c2VySWRcIilcclxuICAgICAgLm5vdE51bGwoKVxyXG4gICAgICAucmVmZXJlbmNlcygoKSA9PiB1c2Vycy5pZCwgeyBvbkRlbGV0ZTogXCJjYXNjYWRlXCIgfSksXHJcbiAgICBwcm92aWRlckFjY291bnRJZDogdGV4dChcInByb3ZpZGVyQWNjb3VudElkXCIpLm5vdE51bGwoKSxcclxuICAgIGNyZWRlbnRpYWxQdWJsaWNLZXk6IHRleHQoXCJjcmVkZW50aWFsUHVibGljS2V5XCIpLm5vdE51bGwoKSxcclxuICAgIGNvdW50ZXI6IGludGVnZXIoXCJjb3VudGVyXCIpLm5vdE51bGwoKSxcclxuICAgIGNyZWRlbnRpYWxEZXZpY2VUeXBlOiB0ZXh0KFwiY3JlZGVudGlhbERldmljZVR5cGVcIikubm90TnVsbCgpLFxyXG4gICAgY3JlZGVudGlhbEJhY2tlZFVwOiBib29sZWFuKFwiY3JlZGVudGlhbEJhY2tlZFVwXCIpLm5vdE51bGwoKSxcclxuICAgIHRyYW5zcG9ydHM6IHRleHQoXCJ0cmFuc3BvcnRzXCIpLFxyXG4gIH0sXHJcbiAgKGF1dGhlbnRpY2F0b3IpID0+ICh7XHJcbiAgICBjb21wb3NpdGVQSzogcHJpbWFyeUtleSh7XHJcbiAgICAgIGNvbHVtbnM6IFthdXRoZW50aWNhdG9yLnVzZXJJZCwgYXV0aGVudGljYXRvci5jcmVkZW50aWFsSURdLFxyXG4gICAgfSksXHJcbiAgfSlcclxuKVxyXG5cclxuZXhwb3J0IGNvbnN0IHByb2plY3RzID0gcGdUYWJsZShcInByb2plY3RcIiwge1xyXG4gIGlkOiB0ZXh0KFwiaWRcIilcclxuICAgIC5wcmltYXJ5S2V5KClcclxuICAgIC4kZGVmYXVsdEZuKCgpID0+IGNyeXB0by5yYW5kb21VVUlEKCkpLFxyXG4gIG5hbWU6IHRleHQoXCJuYW1lXCIpLm5vdE51bGwoKSxcclxuICB1c2VySWQ6IHRleHQoXCJ1c2VySWRcIilcclxuICAgIC5ub3ROdWxsKClcclxuICAgIC5yZWZlcmVuY2VzKCgpID0+IHVzZXJzLmlkLCB7XHJcbiAgICAgIG9uRGVsZXRlOiBcImNhc2NhZGVcIixcclxuICAgIH0pLFxyXG4gIGpzb246IHRleHQoXCJqc29uXCIpLm5vdE51bGwoKSxcclxuICBoZWlnaHQ6IGludGVnZXIoXCJoZWlnaHRcIikubm90TnVsbCgpLFxyXG4gIHdpZHRoOiBpbnRlZ2VyKFwid2lkdGhcIikubm90TnVsbCgpLFxyXG4gIHRodW1ibmFpbFVybDogdGV4dChcInRodW1ibmFpbFVybFwiKSxcclxuICBpc1RlbXBsYXRlOiBib29sZWFuKFwiaXNUZW1wbGF0ZVwiKSxcclxuICBpc1BybzogYm9vbGVhbihcImlzUHJvXCIpLFxyXG4gIGNyZWF0ZWRBdDogdGltZXN0YW1wKFwiY3JlYXRlZEF0XCIsIHsgbW9kZTogXCJkYXRlXCIgfSkubm90TnVsbCgpLFxyXG4gIHVwZGF0ZWRBdDogdGltZXN0YW1wKFwidXBkYXRlZEF0XCIsIHsgbW9kZTogXCJkYXRlXCIgfSkubm90TnVsbCgpLFxyXG59KTtcclxuXHJcbmV4cG9ydCBjb25zdCBwcm9qZWN0c1JlbGF0aW9ucyA9IHJlbGF0aW9ucyhwcm9qZWN0cywgKHsgb25lIH0pID0+ICh7XHJcbiAgdXNlcjogb25lKHVzZXJzLCB7XHJcbiAgICBmaWVsZHM6IFtwcm9qZWN0cy51c2VySWRdLFxyXG4gICAgcmVmZXJlbmNlczogW3VzZXJzLmlkXSxcclxuICB9KSxcclxufSkpO1xyXG5cclxuZXhwb3J0IGNvbnN0IHByb2plY3RzSW5zZXJ0U2NoZW1hID0gY3JlYXRlSW5zZXJ0U2NoZW1hKHByb2plY3RzKTtcclxuXHJcbmV4cG9ydCBjb25zdCBzdWJzY3JpcHRpb25zID0gcGdUYWJsZShcInN1YnNjcmlwdGlvblwiLCB7XHJcbiAgaWQ6IHRleHQoXCJpZFwiKVxyXG4gICAgLnByaW1hcnlLZXkoKVxyXG4gICAgLiRkZWZhdWx0Rm4oKCkgPT4gY3J5cHRvLnJhbmRvbVVVSUQoKSksXHJcbiAgdXNlcklkOiB0ZXh0KFwidXNlcklkXCIpXHJcbiAgICAubm90TnVsbCgpXHJcbiAgICAucmVmZXJlbmNlcygoKSA9PiB1c2Vycy5pZCwge1xyXG4gICAgICBvbkRlbGV0ZTogXCJjYXNjYWRlXCJcclxuICAgIH0pLFxyXG4gIHN1YnNjcmlwdGlvbklkOiB0ZXh0KFwic3Vic2NyaXB0aW9uSWRcIikubm90TnVsbCgpLFxyXG4gIGN1c3RvbWVySWQ6IHRleHQoXCJjdXN0b21lcklkXCIpLm5vdE51bGwoKSxcclxuICBwcmljZUlkOiB0ZXh0KFwicHJpY2VJZFwiKS5ub3ROdWxsKCksXHJcbiAgc3RhdHVzOiB0ZXh0KFwic3RhdHVzXCIpLm5vdE51bGwoKSxcclxuICBjdXJyZW50UGVyaW9kRW5kOiB0aW1lc3RhbXAoXCJjdXJyZW50UGVyaW9kRW5kXCIsIHsgbW9kZTogXCJkYXRlXCIgfSksXHJcbiAgY3JlYXRlZEF0OiB0aW1lc3RhbXAoXCJjcmVhdGVkQXRcIiwgeyBtb2RlOiBcImRhdGVcIiB9KS5ub3ROdWxsKCksXHJcbiAgdXBkYXRlZEF0OiB0aW1lc3RhbXAoXCJ1cGRhdGVkQXRcIiwgeyBtb2RlOiBcImRhdGVcIiB9KS5ub3ROdWxsKCksXHJcbn0pO1xyXG5cclxuZXhwb3J0IGNvbnN0IHVwbG9hZGVkSW1hZ2VzID0gcGdUYWJsZShcInVwbG9hZGVkX2ltYWdlXCIsIHtcclxuICBpZDogdGV4dChcImlkXCIpXHJcbiAgICAucHJpbWFyeUtleSgpXHJcbiAgICAuJGRlZmF1bHRGbigoKSA9PiBjcnlwdG8ucmFuZG9tVVVJRCgpKSxcclxuICB1c2VySWQ6IHRleHQoXCJ1c2VySWRcIilcclxuICAgIC5ub3ROdWxsKClcclxuICAgIC5yZWZlcmVuY2VzKCgpID0+IHVzZXJzLmlkLCB7XHJcbiAgICAgIG9uRGVsZXRlOiBcImNhc2NhZGVcIlxyXG4gICAgfSksXHJcbiAgZmlsZW5hbWU6IHRleHQoXCJmaWxlbmFtZVwiKS5ub3ROdWxsKCksXHJcbiAgb3JpZ2luYWxOYW1lOiB0ZXh0KFwib3JpZ2luYWxOYW1lXCIpLm5vdE51bGwoKSxcclxuICB1cmw6IHRleHQoXCJ1cmxcIikubm90TnVsbCgpLFxyXG4gIHNpemU6IGludGVnZXIoXCJzaXplXCIpLm5vdE51bGwoKSxcclxuICBtaW1lVHlwZTogdGV4dChcIm1pbWVUeXBlXCIpLm5vdE51bGwoKSxcclxuICBjcmVhdGVkQXQ6IHRpbWVzdGFtcChcImNyZWF0ZWRBdFwiLCB7IG1vZGU6IFwiZGF0ZVwiIH0pLm5vdE51bGwoKSxcclxufSk7XHJcblxyXG5leHBvcnQgY29uc3QgdXBsb2FkZWRJbWFnZXNSZWxhdGlvbnMgPSByZWxhdGlvbnModXBsb2FkZWRJbWFnZXMsICh7IG9uZSB9KSA9PiAoe1xyXG4gIHVzZXI6IG9uZSh1c2Vycywge1xyXG4gICAgZmllbGRzOiBbdXBsb2FkZWRJbWFnZXMudXNlcklkXSxcclxuICAgIHJlZmVyZW5jZXM6IFt1c2Vycy5pZF0sXHJcbiAgfSksXHJcbn0pKTtcclxuXHJcbmV4cG9ydCBjb25zdCB1cGxvYWRlZEltYWdlc0luc2VydFNjaGVtYSA9IGNyZWF0ZUluc2VydFNjaGVtYSh1cGxvYWRlZEltYWdlcyk7XHJcbiJdLCJuYW1lcyI6WyJyZWxhdGlvbnMiLCJjcmVhdGVJbnNlcnRTY2hlbWEiLCJib29sZWFuIiwidGltZXN0YW1wIiwicGdUYWJsZSIsInRleHQiLCJwcmltYXJ5S2V5IiwiaW50ZWdlciIsInVzZXJzIiwiaWQiLCIkZGVmYXVsdEZuIiwiY3J5cHRvIiwicmFuZG9tVVVJRCIsIm5hbWUiLCJlbWFpbCIsIm5vdE51bGwiLCJlbWFpbFZlcmlmaWVkIiwibW9kZSIsImltYWdlIiwicGFzc3dvcmQiLCJ1c2Vyc1JlbGF0aW9ucyIsIm1hbnkiLCJwcm9qZWN0cyIsImFjY291bnRzIiwidXNlcklkIiwicmVmZXJlbmNlcyIsIm9uRGVsZXRlIiwidHlwZSIsIiR0eXBlIiwicHJvdmlkZXIiLCJwcm92aWRlckFjY291bnRJZCIsInJlZnJlc2hfdG9rZW4iLCJhY2Nlc3NfdG9rZW4iLCJleHBpcmVzX2F0IiwidG9rZW5fdHlwZSIsInNjb3BlIiwiaWRfdG9rZW4iLCJzZXNzaW9uX3N0YXRlIiwiYWNjb3VudCIsImNvbXBvdW5kS2V5IiwiY29sdW1ucyIsInNlc3Npb25zIiwic2Vzc2lvblRva2VuIiwiZXhwaXJlcyIsInZlcmlmaWNhdGlvblRva2VucyIsImlkZW50aWZpZXIiLCJ0b2tlbiIsInZlcmlmaWNhdGlvblRva2VuIiwiY29tcG9zaXRlUGsiLCJhdXRoZW50aWNhdG9ycyIsImNyZWRlbnRpYWxJRCIsInVuaXF1ZSIsImNyZWRlbnRpYWxQdWJsaWNLZXkiLCJjb3VudGVyIiwiY3JlZGVudGlhbERldmljZVR5cGUiLCJjcmVkZW50aWFsQmFja2VkVXAiLCJ0cmFuc3BvcnRzIiwiYXV0aGVudGljYXRvciIsImNvbXBvc2l0ZVBLIiwianNvbiIsImhlaWdodCIsIndpZHRoIiwidGh1bWJuYWlsVXJsIiwiaXNUZW1wbGF0ZSIsImlzUHJvIiwiY3JlYXRlZEF0IiwidXBkYXRlZEF0IiwicHJvamVjdHNSZWxhdGlvbnMiLCJvbmUiLCJ1c2VyIiwiZmllbGRzIiwicHJvamVjdHNJbnNlcnRTY2hlbWEiLCJzdWJzY3JpcHRpb25zIiwic3Vic2NyaXB0aW9uSWQiLCJjdXN0b21lcklkIiwicHJpY2VJZCIsInN0YXR1cyIsImN1cnJlbnRQZXJpb2RFbmQiLCJ1cGxvYWRlZEltYWdlcyIsImZpbGVuYW1lIiwib3JpZ2luYWxOYW1lIiwidXJsIiwic2l6ZSIsIm1pbWVUeXBlIiwidXBsb2FkZWRJbWFnZXNSZWxhdGlvbnMiLCJ1cGxvYWRlZEltYWdlc0luc2VydFNjaGVtYSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/db/schema.ts\n");

/***/ }),

/***/ "(rsc)/./src/features/subscriptions/lib.ts":
/*!*******************************************!*\
  !*** ./src/features/subscriptions/lib.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkIsActive: () => (/* binding */ checkIsActive)\n/* harmony export */ });\nconst DAY_IN_MS = 86400000;\nconst checkIsActive = (subscription)=>{\n    let active = false;\n    if (subscription && subscription.priceId && subscription.currentPeriodEnd) {\n        active = subscription.currentPeriodEnd.getTime() + DAY_IN_MS > Date.now();\n    }\n    return active;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvZmVhdHVyZXMvc3Vic2NyaXB0aW9ucy9saWIudHMiLCJtYXBwaW5ncyI6Ijs7OztBQUVBLE1BQU1BLFlBQVk7QUFFWCxNQUFNQyxnQkFBZ0IsQ0FDM0JDO0lBRUEsSUFBSUMsU0FBUztJQUViLElBQ0VELGdCQUNBQSxhQUFhRSxPQUFPLElBQ3BCRixhQUFhRyxnQkFBZ0IsRUFDN0I7UUFDQUYsU0FBU0QsYUFBYUcsZ0JBQWdCLENBQUNDLE9BQU8sS0FBS04sWUFBWU8sS0FBS0MsR0FBRztJQUN6RTtJQUVBLE9BQU9MO0FBQ1QsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL3RoZS1jYW52YXMvLi9zcmMvZmVhdHVyZXMvc3Vic2NyaXB0aW9ucy9saWIudHM/M2NjNCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBzdWJzY3JpcHRpb25zIH0gZnJvbSBcIkAvZGIvc2NoZW1hXCI7XHJcblxyXG5jb25zdCBEQVlfSU5fTVMgPSA4Nl80MDBfMDAwO1xyXG5cclxuZXhwb3J0IGNvbnN0IGNoZWNrSXNBY3RpdmUgPSAoXHJcbiAgc3Vic2NyaXB0aW9uOiB0eXBlb2Ygc3Vic2NyaXB0aW9ucy4kaW5mZXJTZWxlY3QsXHJcbikgPT4ge1xyXG4gIGxldCBhY3RpdmUgPSBmYWxzZTtcclxuXHJcbiAgaWYgKFxyXG4gICAgc3Vic2NyaXB0aW9uICYmXHJcbiAgICBzdWJzY3JpcHRpb24ucHJpY2VJZCAmJlxyXG4gICAgc3Vic2NyaXB0aW9uLmN1cnJlbnRQZXJpb2RFbmRcclxuICApIHtcclxuICAgIGFjdGl2ZSA9IHN1YnNjcmlwdGlvbi5jdXJyZW50UGVyaW9kRW5kLmdldFRpbWUoKSArIERBWV9JTl9NUyA+IERhdGUubm93KCk7XHJcbiAgfVxyXG5cclxuICByZXR1cm4gYWN0aXZlO1xyXG59O1xyXG4iXSwibmFtZXMiOlsiREFZX0lOX01TIiwiY2hlY2tJc0FjdGl2ZSIsInN1YnNjcmlwdGlvbiIsImFjdGl2ZSIsInByaWNlSWQiLCJjdXJyZW50UGVyaW9kRW5kIiwiZ2V0VGltZSIsIkRhdGUiLCJub3ciXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/features/subscriptions/lib.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/replicate.ts":
/*!******************************!*\
  !*** ./src/lib/replicate.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   replicate: () => (/* binding */ replicate)\n/* harmony export */ });\n/* harmony import */ var replicate__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! replicate */ \"(rsc)/./node_modules/replicate/index.js\");\n/* harmony import */ var replicate__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(replicate__WEBPACK_IMPORTED_MODULE_0__);\n\nconst replicate = new (replicate__WEBPACK_IMPORTED_MODULE_0___default())({\n    auth: process.env.REPLICATE_API_TOKEN\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3JlcGxpY2F0ZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBa0M7QUFFM0IsTUFBTUMsWUFBWSxJQUFJRCxrREFBU0EsQ0FBQztJQUNyQ0UsTUFBTUMsUUFBUUMsR0FBRyxDQUFDQyxtQkFBbUI7QUFDdkMsR0FBRyIsInNvdXJjZXMiOlsid2VicGFjazovL3RoZS1jYW52YXMvLi9zcmMvbGliL3JlcGxpY2F0ZS50cz81ODBmIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZXBsaWNhdGUgZnJvbSBcInJlcGxpY2F0ZVwiO1xyXG5cclxuZXhwb3J0IGNvbnN0IHJlcGxpY2F0ZSA9IG5ldyBSZXBsaWNhdGUoe1xyXG4gIGF1dGg6IHByb2Nlc3MuZW52LlJFUExJQ0FURV9BUElfVE9LRU4sXHJcbn0pO1xyXG4iXSwibmFtZXMiOlsiUmVwbGljYXRlIiwicmVwbGljYXRlIiwiYXV0aCIsInByb2Nlc3MiLCJlbnYiLCJSRVBMSUNBVEVfQVBJX1RPS0VOIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/replicate.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/stripe.ts":
/*!***************************!*\
  !*** ./src/lib/stripe.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   stripe: () => (/* binding */ stripe)\n/* harmony export */ });\n/* harmony import */ var stripe__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! stripe */ \"(rsc)/./node_modules/stripe/esm/stripe.esm.node.js\");\n\nconst stripe = new stripe__WEBPACK_IMPORTED_MODULE_0__[\"default\"](process.env.STRIPE_SECRET_KEY, {\n    apiVersion: \"2024-06-20\",\n    typescript: true\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3N0cmlwZS50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUE0QjtBQUVyQixNQUFNQyxTQUFTLElBQUlELDhDQUFNQSxDQUFDRSxRQUFRQyxHQUFHLENBQUNDLGlCQUFpQixFQUFHO0lBQy9EQyxZQUFZO0lBQ1pDLFlBQVk7QUFDZCxHQUFHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGhlLWNhbnZhcy8uL3NyYy9saWIvc3RyaXBlLnRzPzc5OGEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFN0cmlwZSBmcm9tIFwic3RyaXBlXCI7XHJcblxyXG5leHBvcnQgY29uc3Qgc3RyaXBlID0gbmV3IFN0cmlwZShwcm9jZXNzLmVudi5TVFJJUEVfU0VDUkVUX0tFWSEsIHtcclxuICBhcGlWZXJzaW9uOiBcIjIwMjQtMDYtMjBcIixcclxuICB0eXBlc2NyaXB0OiB0cnVlLFxyXG59KTtcclxuIl0sIm5hbWVzIjpbIlN0cmlwZSIsInN0cmlwZSIsInByb2Nlc3MiLCJlbnYiLCJTVFJJUEVfU0VDUkVUX0tFWSIsImFwaVZlcnNpb24iLCJ0eXBlc2NyaXB0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/stripe.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/unsplash.ts":
/*!*****************************!*\
  !*** ./src/lib/unsplash.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   unsplash: () => (/* binding */ unsplash)\n/* harmony export */ });\n/* harmony import */ var unsplash_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unsplash-js */ \"(rsc)/./node_modules/unsplash-js/dist/unsplash-js.esm.js\");\n\nconst unsplash = (0,unsplash_js__WEBPACK_IMPORTED_MODULE_0__.createApi)({\n    accessKey: process.env.NEXT_PUBLIC_UNSPLASH_ACCESS_KEY,\n    fetch: fetch\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3Vuc3BsYXNoLnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXdDO0FBRWpDLE1BQU1DLFdBQVdELHNEQUFTQSxDQUFDO0lBQ2hDRSxXQUFXQyxRQUFRQyxHQUFHLENBQUNDLCtCQUErQjtJQUN0REMsT0FBT0E7QUFDVCxHQUFHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGhlLWNhbnZhcy8uL3NyYy9saWIvdW5zcGxhc2gudHM/MGIxMSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVBcGkgfSBmcm9tIFwidW5zcGxhc2gtanNcIjtcclxuXHJcbmV4cG9ydCBjb25zdCB1bnNwbGFzaCA9IGNyZWF0ZUFwaSh7XHJcbiAgYWNjZXNzS2V5OiBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19VTlNQTEFTSF9BQ0NFU1NfS0VZISxcclxuICBmZXRjaDogZmV0Y2gsXHJcbn0pO1xyXG4iXSwibmFtZXMiOlsiY3JlYXRlQXBpIiwidW5zcGxhc2giLCJhY2Nlc3NLZXkiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfVU5TUExBU0hfQUNDRVNTX0tFWSIsImZldGNoIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/unsplash.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/drizzle-orm","vendor-chunks/next-auth","vendor-chunks/@auth","vendor-chunks/zod","vendor-chunks/@neondatabase","vendor-chunks/jose","vendor-chunks/oauth4webapi","vendor-chunks/bcryptjs","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/cookie","vendor-chunks/@panva","vendor-chunks/drizzle-zod","vendor-chunks/hono","vendor-chunks/uuidv4","vendor-chunks/stripe","vendor-chunks/replicate","vendor-chunks/es-errors","vendor-chunks/qs","vendor-chunks/@hono","vendor-chunks/object-inspect","vendor-chunks/has-symbols","vendor-chunks/function-bind","vendor-chunks/call-bind","vendor-chunks/unsplash-js","vendor-chunks/side-channel","vendor-chunks/set-function-length","vendor-chunks/hasown","vendor-chunks/has-proto","vendor-chunks/has-property-descriptors","vendor-chunks/gopd","vendor-chunks/get-intrinsic","vendor-chunks/es-define-property","vendor-chunks/define-data-property"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2F%5B%5B...route%5D%5D%2Froute&page=%2Fapi%2F%5B%5B...route%5D%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2F%5B%5B...route%5D%5D%2Froute.ts&appDir=C%3A%5CUsers%5Canwar%5CDocuments%5CSKETTCHA%5Ccanva-clone%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Canwar%5CDocuments%5CSKETTCHA%5Ccanva-clone&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();