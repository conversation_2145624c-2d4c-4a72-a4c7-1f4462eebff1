import { fabric } from "fabric";
import { useEffect } from "react";

interface UseCanvasEventsProps {
  save: () => void;
  canvas: fabric.Canvas | null;
  setSelectedObjects: (objects: fabric.Object[]) => void;
  clearSelectionCallback?: () => void;
  setCanvasIsSelected?: (isSelected: boolean) => void;
};

export const useCanvasEvents = ({
  save,
  canvas,
  setSelectedObjects,
  clearSelectionCallback,
  setCanvasIsSelected,
}: UseCanvasEventsProps) => {
  useEffect(() => {
    if (canvas) {
      // Object events
      canvas.on("object:added", () => save());
      canvas.on("object:removed", () => save());
      canvas.on("object:modified", () => save());

      // Selection events
      canvas.on("selection:created", (e) => {
        setSelectedObjects(e.selected || []);
        setCanvasIsSelected?.(false);
      });
      canvas.on("selection:updated", (e) => {
        setSelectedObjects(e.selected || []);
        setCanvasIsSelected?.(false);
      });
      canvas.on("selection:cleared", () => {
        setSelectedObjects([]);
        clearSelectionCallback?.();
        setCanvasIsSelected?.(false);
      });

      // Canvas click detection
      const handleMouseDown = (e: any) => {
        const target = e.target;
        const workspace = canvas.getObjects().find((obj) => obj.name === "clip");

        // Check if clicked on canvas background (workspace) or empty area
        if (!target || target === workspace) {
          setCanvasIsSelected?.(true);
        } else {
          setCanvasIsSelected?.(false);
        }
      };

      canvas.on("mouse:down", handleMouseDown);

      // Mouse wheel zoom functionality
      const handleMouseWheel = (opt: any) => {
        const delta = opt.e.deltaY;
        let zoom = canvas.getZoom();

        // Much more gentle zoom sensitivity for laptops
        const zoomStep = 0.02; // Reduced from 0.1 to 0.02 for smoother control

        if (delta < 0) {
          // Zoom in
          zoom += zoomStep;
        } else {
          // Zoom out
          zoom -= zoomStep;
        }

        // Limit zoom levels (same as button zoom limits)
        if (zoom > 1) zoom = 1;
        if (zoom < 0.2) zoom = 0.2;

        // Zoom to mouse pointer position
        canvas.zoomToPoint(
          { x: opt.e.offsetX, y: opt.e.offsetY },
          zoom
        );

        opt.e.preventDefault();
        opt.e.stopPropagation();
      };

      // Add mouse wheel event
      canvas.on("mouse:wheel", handleMouseWheel);
    }

    return () => {
      if (canvas) {
        canvas.off("object:added");
        canvas.off("object:removed");
        canvas.off("object:modified");
        canvas.off("selection:created");
        canvas.off("selection:updated");
        canvas.off("selection:cleared");
        canvas.off("mouse:wheel");
        canvas.off("mouse:down");
      }
    };
  },
  [
    save,
    canvas,
    clearSelectionCallback,
    setSelectedObjects, // No need for this, this is from setState
    setCanvasIsSelected
  ]);
};
