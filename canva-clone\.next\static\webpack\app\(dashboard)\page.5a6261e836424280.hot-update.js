/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/page",{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Canwar%5C%5CDocuments%5C%5CSKETTCHA%5C%5Ccanva-clone%5C%5Csrc%5C%5Capp%5C%5C(dashboard)%5C%5Cbanner.tsx%22%2C%22ids%22%3A%5B%22Banner%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Canwar%5C%5CDocuments%5C%5CSKETTCHA%5C%5Ccanva-clone%5C%5Csrc%5C%5Capp%5C%5C(dashboard)%5C%5Cprojects-section.tsx%22%2C%22ids%22%3A%5B%22ProjectsSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Canwar%5C%5CDocuments%5C%5CSKETTCHA%5C%5Ccanva-clone%5C%5Csrc%5C%5Capp%5C%5C(dashboard)%5C%5Cquick-templates.tsx%22%2C%22ids%22%3A%5B%22QuickTemplates%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Canwar%5C%5CDocuments%5C%5CSKETTCHA%5C%5Ccanva-clone%5C%5Csrc%5C%5Capp%5C%5C(dashboard)%5C%5Ctemplates-section.tsx%22%2C%22ids%22%3A%5B%22TemplatesSection%22%5D%7D&server=false!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Canwar%5C%5CDocuments%5C%5CSKETTCHA%5C%5Ccanva-clone%5C%5Csrc%5C%5Capp%5C%5C(dashboard)%5C%5Cbanner.tsx%22%2C%22ids%22%3A%5B%22Banner%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Canwar%5C%5CDocuments%5C%5CSKETTCHA%5C%5Ccanva-clone%5C%5Csrc%5C%5Capp%5C%5C(dashboard)%5C%5Cprojects-section.tsx%22%2C%22ids%22%3A%5B%22ProjectsSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Canwar%5C%5CDocuments%5C%5CSKETTCHA%5C%5Ccanva-clone%5C%5Csrc%5C%5Capp%5C%5C(dashboard)%5C%5Cquick-templates.tsx%22%2C%22ids%22%3A%5B%22QuickTemplates%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Canwar%5C%5CDocuments%5C%5CSKETTCHA%5C%5Ccanva-clone%5C%5Csrc%5C%5Capp%5C%5C(dashboard)%5C%5Ctemplates-section.tsx%22%2C%22ids%22%3A%5B%22TemplatesSection%22%5D%7D&server=false! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(dashboard)/banner.tsx */ \"(app-pages-browser)/./src/app/(dashboard)/banner.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(dashboard)/projects-section.tsx */ \"(app-pages-browser)/./src/app/(dashboard)/projects-section.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(dashboard)/quick-templates.tsx */ \"(app-pages-browser)/./src/app/(dashboard)/quick-templates.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(dashboard)/templates-section.tsx */ \"(app-pages-browser)/./src/app/(dashboard)/templates-section.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Canwar%5C%5CDocuments%5C%5CSKETTCHA%5C%5Ccanva-clone%5C%5Csrc%5C%5Capp%5C%5C(dashboard)%5C%5Cbanner.tsx%22%2C%22ids%22%3A%5B%22Banner%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Canwar%5C%5CDocuments%5C%5CSKETTCHA%5C%5Ccanva-clone%5C%5Csrc%5C%5Capp%5C%5C(dashboard)%5C%5Cprojects-section.tsx%22%2C%22ids%22%3A%5B%22ProjectsSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Canwar%5C%5CDocuments%5C%5CSKETTCHA%5C%5Ccanva-clone%5C%5Csrc%5C%5Capp%5C%5C(dashboard)%5C%5Cquick-templates.tsx%22%2C%22ids%22%3A%5B%22QuickTemplates%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Canwar%5C%5CDocuments%5C%5CSKETTCHA%5C%5Ccanva-clone%5C%5Csrc%5C%5Capp%5C%5C(dashboard)%5C%5Ctemplates-section.tsx%22%2C%22ids%22%3A%5B%22TemplatesSection%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/(dashboard)/quick-templates.tsx":
/*!*************************************************!*\
  !*** ./src/app/(dashboard)/quick-templates.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuickTemplates: function() { return /* binding */ QuickTemplates; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Instagram_Loader2_Monitor_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Instagram,Loader2,Monitor,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/instagram.js\");\n/* harmony import */ var _barrel_optimize_names_Instagram_Loader2_Monitor_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Instagram,Loader2,Monitor,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* harmony import */ var _barrel_optimize_names_Instagram_Loader2_Monitor_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Instagram,Loader2,Monitor,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/monitor.js\");\n/* harmony import */ var _barrel_optimize_names_Instagram_Loader2_Monitor_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Instagram,Loader2,Monitor,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _features_projects_api_use_create_project__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/projects/api/use-create-project */ \"(app-pages-browser)/./src/features/projects/api/use-create-project.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ QuickTemplates auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst quickTemplates = [\n    {\n        id: \"instagram-post\",\n        name: \"Instagram Post\",\n        description: \"1080 x 1080 px\",\n        width: 1080,\n        height: 1080,\n        icon: _barrel_optimize_names_Instagram_Loader2_Monitor_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        color: \"bg-gradient-to-br from-purple-500 to-pink-500\"\n    },\n    {\n        id: \"instagram-story\",\n        name: \"Instagram Story\",\n        description: \"1080 x 1920 px\",\n        width: 1080,\n        height: 1920,\n        icon: _barrel_optimize_names_Instagram_Loader2_Monitor_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        color: \"bg-gradient-to-br from-blue-500 to-purple-500\"\n    },\n    {\n        id: \"presentation\",\n        name: \"Presentation\",\n        description: \"1920 x 1080 px\",\n        width: 1920,\n        height: 1080,\n        icon: _barrel_optimize_names_Instagram_Loader2_Monitor_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        color: \"bg-gradient-to-br from-green-500 to-blue-500\"\n    }\n];\nconst QuickTemplates = ()=>{\n    _s();\n    const [selectedTemplate, setSelectedTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const mutation = (0,_features_projects_api_use_create_project__WEBPACK_IMPORTED_MODULE_3__.useCreateProject)();\n    const handleTemplateSelect = (template)=>{\n        setSelectedTemplate(template.id);\n        setLoading(true);\n        mutation.mutate({\n            name: \"\".concat(template.name, \" project\"),\n            json: \"\",\n            width: template.width,\n            height: template.height\n        }, {\n            onSuccess: (param)=>{\n                let { data } = param;\n                router.push(\"/editor/\".concat(data.id));\n            },\n            onError: ()=>{\n                setLoading(false);\n                setSelectedTemplate(null);\n            }\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"font-semibold text-lg\",\n                children: \"Quick Start\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\quick-templates.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                children: quickTemplates.map((template)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(QuickTemplateCard, {\n                        template: template,\n                        isSelected: selectedTemplate === template.id,\n                        isLoading: loading && selectedTemplate === template.id,\n                        onClick: ()=>handleTemplateSelect(template),\n                        disabled: loading\n                    }, template.id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\quick-templates.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\quick-templates.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\quick-templates.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, undefined);\n};\n_s(QuickTemplates, \"wRkydUrBue7hRv7ETo3gNTbbdNk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        _features_projects_api_use_create_project__WEBPACK_IMPORTED_MODULE_3__.useCreateProject\n    ];\n});\n_c = QuickTemplates;\nconst QuickTemplateCard = (param)=>{\n    let { template, isSelected, isLoading, onClick, disabled } = param;\n    const Icon = template.icon;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n        variant: \"outline\",\n        onClick: onClick,\n        disabled: disabled,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"relative h-24 p-4 flex items-center space-x-4 transition-all duration-200 hover:shadow-md\", isSelected && \"ring-2 ring-blue-500 bg-blue-50\", disabled && \"opacity-50 cursor-not-allowed\"),\n        children: [\n            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-white/80 flex items-center justify-center rounded-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Instagram_Loader2_Monitor_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"size-6 animate-spin text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\quick-templates.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\quick-templates.tsx\",\n                lineNumber: 128,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"rounded-lg flex items-center justify-center size-12 text-white\", template.color),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                    className: \"size-6\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\quick-templates.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\quick-templates.tsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-left space-y-1 flex-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"font-medium text-gray-900 text-sm\",\n                        children: template.name\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\quick-templates.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500 text-xs\",\n                        children: template.description\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\quick-templates.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\quick-templates.tsx\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\quick-templates.tsx\",\n        lineNumber: 117,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = QuickTemplateCard;\nvar _c, _c1;\n$RefreshReg$(_c, \"QuickTemplates\");\n$RefreshReg$(_c1, \"QuickTemplateCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/quick-templates.tsx\n"));

/***/ })

});