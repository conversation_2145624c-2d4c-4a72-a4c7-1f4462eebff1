"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/page",{

/***/ "(app-pages-browser)/./src/app/(dashboard)/template-categories.tsx":
/*!*****************************************************!*\
  !*** ./src/app/(dashboard)/template-categories.tsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TemplateCategories: function() { return /* binding */ TemplateCategories; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_CreditCard_FileText_Instagram_Loader2_Monitor_Smartphone_Square_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,FileText,Instagram,Loader2,Monitor,Smartphone,Square!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/instagram.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_FileText_Instagram_Loader2_Monitor_Smartphone_Square_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,FileText,Instagram,Loader2,Monitor,Smartphone,Square!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_FileText_Instagram_Loader2_Monitor_Smartphone_Square_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,FileText,Instagram,Loader2,Monitor,Smartphone,Square!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/monitor.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_FileText_Instagram_Loader2_Monitor_Smartphone_Square_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,FileText,Instagram,Loader2,Monitor,Smartphone,Square!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_FileText_Instagram_Loader2_Monitor_Smartphone_Square_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,FileText,Instagram,Loader2,Monitor,Smartphone,Square!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_FileText_Instagram_Loader2_Monitor_Smartphone_Square_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,FileText,Instagram,Loader2,Monitor,Smartphone,Square!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_FileText_Instagram_Loader2_Monitor_Smartphone_Square_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,FileText,Instagram,Loader2,Monitor,Smartphone,Square!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _features_projects_api_use_create_project__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/projects/api/use-create-project */ \"(app-pages-browser)/./src/features/projects/api/use-create-project.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ TemplateCategories auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst templateCategories = [\n    {\n        id: \"instagram-post\",\n        name: \"Instagram Post\",\n        description: \"1080 x 1080 px\",\n        width: 1080,\n        height: 1080,\n        icon: _barrel_optimize_names_CreditCard_FileText_Instagram_Loader2_Monitor_Smartphone_Square_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        popular: true\n    },\n    {\n        id: \"instagram-story\",\n        name: \"Instagram Story\",\n        description: \"1080 x 1920 px\",\n        width: 1080,\n        height: 1920,\n        icon: _barrel_optimize_names_CreditCard_FileText_Instagram_Loader2_Monitor_Smartphone_Square_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        popular: true\n    },\n    {\n        id: \"presentation\",\n        name: \"Presentation\",\n        description: \"1920 x 1080 px\",\n        width: 1920,\n        height: 1080,\n        icon: _barrel_optimize_names_CreditCard_FileText_Instagram_Loader2_Monitor_Smartphone_Square_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        popular: true\n    },\n    {\n        id: \"document\",\n        name: \"Document\",\n        description: \"2480 x 3508 px (A4)\",\n        width: 2480,\n        height: 3508,\n        icon: _barrel_optimize_names_CreditCard_FileText_Instagram_Loader2_Monitor_Smartphone_Square_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    },\n    {\n        id: \"poster\",\n        name: \"Poster\",\n        description: \"1654 x 2339 px\",\n        width: 1654,\n        height: 2339,\n        icon: _barrel_optimize_names_CreditCard_FileText_Instagram_Loader2_Monitor_Smartphone_Square_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    },\n    {\n        id: \"business-card\",\n        name: \"Business Card\",\n        description: \"1050 x 600 px\",\n        width: 1050,\n        height: 600,\n        icon: _barrel_optimize_names_CreditCard_FileText_Instagram_Loader2_Monitor_Smartphone_Square_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n    },\n    {\n        id: \"facebook-post\",\n        name: \"Facebook Post\",\n        description: \"1200 x 630 px\",\n        width: 1200,\n        height: 630,\n        icon: _barrel_optimize_names_CreditCard_FileText_Instagram_Loader2_Monitor_Smartphone_Square_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    },\n    {\n        id: \"youtube-thumbnail\",\n        name: \"YouTube Thumbnail\",\n        description: \"1280 x 720 px\",\n        width: 1280,\n        height: 720,\n        icon: _barrel_optimize_names_CreditCard_FileText_Instagram_Loader2_Monitor_Smartphone_Square_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    },\n    {\n        id: \"custom\",\n        name: \"Custom Size\",\n        description: \"900 x 1200 px\",\n        width: 900,\n        height: 1200,\n        icon: _barrel_optimize_names_CreditCard_FileText_Instagram_Loader2_Monitor_Smartphone_Square_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    }\n];\nconst TemplateCategories = ()=>{\n    _s();\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const mutation = (0,_features_projects_api_use_create_project__WEBPACK_IMPORTED_MODULE_3__.useCreateProject)();\n    const handleCategorySelect = (category)=>{\n        setSelectedCategory(category.id);\n        setLoading(true);\n        mutation.mutate({\n            name: \"\".concat(category.name, \" project\"),\n            json: \"\",\n            width: category.width,\n            height: category.height\n        }, {\n            onSuccess: (param)=>{\n                let { data } = param;\n                router.push(\"/editor/\".concat(data.id));\n            },\n            onError: ()=>{\n                setLoading(false);\n                setSelectedCategory(null);\n            }\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900\",\n                        children: \"Choose a template size\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\template-categories.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Select the perfect canvas size for your design\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\template-categories.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\template-categories.tsx\",\n                lineNumber: 130,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-800\",\n                        children: \"Popular\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\template-categories.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                        children: templateCategories.filter((category)=>category.popular).map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TemplateCard, {\n                                category: category,\n                                isSelected: selectedCategory === category.id,\n                                isLoading: loading && selectedCategory === category.id,\n                                onClick: ()=>handleCategorySelect(category),\n                                disabled: loading\n                            }, category.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\template-categories.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\template-categories.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\template-categories.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-800\",\n                        children: \"All Templates\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\template-categories.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4\",\n                        children: templateCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TemplateCard, {\n                                category: category,\n                                isSelected: selectedCategory === category.id,\n                                isLoading: loading && selectedCategory === category.id,\n                                onClick: ()=>handleCategorySelect(category),\n                                disabled: loading,\n                                compact: true\n                            }, category.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\template-categories.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\template-categories.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\template-categories.tsx\",\n                lineNumber: 155,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\template-categories.tsx\",\n        lineNumber: 129,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TemplateCategories, \"Y4no2UY2hwCwbpDAW1d8pZCOeg8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        _features_projects_api_use_create_project__WEBPACK_IMPORTED_MODULE_3__.useCreateProject\n    ];\n});\n_c = TemplateCategories;\nconst TemplateCard = (param)=>{\n    let { category, isSelected, isLoading, onClick, disabled, compact = false } = param;\n    const Icon = category.icon;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n        variant: \"outline\",\n        onClick: onClick,\n        disabled: disabled,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"relative h-auto p-4 flex flex-col items-center space-y-3 transition-all duration-200 hover:shadow-md\", compact ? \"aspect-square\" : \"aspect-[4/3]\", isSelected && \"ring-2 ring-blue-500 bg-blue-50\", disabled && \"opacity-50 cursor-not-allowed\"),\n        children: [\n            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-white/80 flex items-center justify-center rounded-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_FileText_Instagram_Loader2_Monitor_Smartphone_Square_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"size-6 animate-spin text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\template-categories.tsx\",\n                    lineNumber: 208,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\template-categories.tsx\",\n                lineNumber: 207,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"rounded-lg bg-gray-100 flex items-center justify-center\", compact ? \"size-8\" : \"size-12\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"text-gray-600\", compact ? \"size-4\" : \"size-6\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\template-categories.tsx\",\n                    lineNumber: 216,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\template-categories.tsx\",\n                lineNumber: 212,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"font-medium text-gray-900\", compact ? \"text-xs\" : \"text-sm\"),\n                        children: category.name\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\template-categories.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"text-gray-500\", compact ? \"text-xs\" : \"text-xs\"),\n                        children: category.description\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\template-categories.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\template-categories.tsx\",\n                lineNumber: 222,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\template-categories.tsx\",\n        lineNumber: 195,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = TemplateCard;\nvar _c, _c1;\n$RefreshReg$(_c, \"TemplateCategories\");\n$RefreshReg$(_c1, \"TemplateCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/template-categories.tsx\n"));

/***/ })

});