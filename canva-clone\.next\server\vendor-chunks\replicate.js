/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/replicate";
exports.ids = ["vendor-chunks/replicate"];
exports.modules = {

/***/ "(rsc)/./node_modules/replicate/index.js":
/*!*****************************************!*\
  !*** ./node_modules/replicate/index.js ***!
  \*****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const ApiError = __webpack_require__(/*! ./lib/error */ \"(rsc)/./node_modules/replicate/lib/error.js\");\nconst ModelVersionIdentifier = __webpack_require__(/*! ./lib/identifier */ \"(rsc)/./node_modules/replicate/lib/identifier.js\");\nconst { createReadableStream } = __webpack_require__(/*! ./lib/stream */ \"(rsc)/./node_modules/replicate/lib/stream.js\");\nconst {\n  withAutomaticRetries,\n  validateWebhook,\n  parseProgressFromLogs,\n  streamAsyncIterator,\n} = __webpack_require__(/*! ./lib/util */ \"(rsc)/./node_modules/replicate/lib/util.js\");\n\nconst accounts = __webpack_require__(/*! ./lib/accounts */ \"(rsc)/./node_modules/replicate/lib/accounts.js\");\nconst collections = __webpack_require__(/*! ./lib/collections */ \"(rsc)/./node_modules/replicate/lib/collections.js\");\nconst deployments = __webpack_require__(/*! ./lib/deployments */ \"(rsc)/./node_modules/replicate/lib/deployments.js\");\nconst files = __webpack_require__(/*! ./lib/files */ \"(rsc)/./node_modules/replicate/lib/files.js\");\nconst hardware = __webpack_require__(/*! ./lib/hardware */ \"(rsc)/./node_modules/replicate/lib/hardware.js\");\nconst models = __webpack_require__(/*! ./lib/models */ \"(rsc)/./node_modules/replicate/lib/models.js\");\nconst predictions = __webpack_require__(/*! ./lib/predictions */ \"(rsc)/./node_modules/replicate/lib/predictions.js\");\nconst trainings = __webpack_require__(/*! ./lib/trainings */ \"(rsc)/./node_modules/replicate/lib/trainings.js\");\nconst webhooks = __webpack_require__(/*! ./lib/webhooks */ \"(rsc)/./node_modules/replicate/lib/webhooks.js\");\n\nconst packageJSON = __webpack_require__(/*! ./package.json */ \"(rsc)/./node_modules/replicate/package.json\");\n\n/**\n * Replicate API client library\n *\n * @see https://replicate.com/docs/reference/http\n * @example\n * // Create a new Replicate API client instance\n * const Replicate = require(\"replicate\");\n * const replicate = new Replicate({\n *     // get your token from https://replicate.com/account\n *     auth: process.env.REPLICATE_API_TOKEN,\n *     userAgent: \"my-app/1.2.3\"\n * });\n *\n * // Run a model and await the result:\n * const model = 'owner/model:version-id'\n * const input = {text: 'Hello, world!'}\n * const output = await replicate.run(model, { input });\n */\nclass Replicate {\n  /**\n   * Create a new Replicate API client instance.\n   *\n   * @param {object} options - Configuration options for the client\n   * @param {string} options.auth - API access token. Defaults to the `REPLICATE_API_TOKEN` environment variable.\n   * @param {string} options.userAgent - Identifier of your app\n   * @param {string} [options.baseUrl] - Defaults to https://api.replicate.com/v1\n   * @param {Function} [options.fetch] - Fetch function to use. Defaults to `globalThis.fetch`\n   * @param {\"default\" | \"upload\" | \"data-uri\"} [options.fileEncodingStrategy] - Determines the file encoding strategy to use\n   */\n  constructor(options = {}) {\n    this.auth =\n      options.auth ||\n      (typeof process !== \"undefined\" ? process.env.REPLICATE_API_TOKEN : null);\n    this.userAgent =\n      options.userAgent || `replicate-javascript/${packageJSON.version}`;\n    this.baseUrl = options.baseUrl || \"https://api.replicate.com/v1\";\n    this.fetch = options.fetch || globalThis.fetch;\n    this.fileEncodingStrategy = options.fileEncodingStrategy ?? \"default\";\n\n    this.accounts = {\n      current: accounts.current.bind(this),\n    };\n\n    this.collections = {\n      list: collections.list.bind(this),\n      get: collections.get.bind(this),\n    };\n\n    this.deployments = {\n      get: deployments.get.bind(this),\n      create: deployments.create.bind(this),\n      update: deployments.update.bind(this),\n      delete: deployments.delete.bind(this),\n      list: deployments.list.bind(this),\n      predictions: {\n        create: deployments.predictions.create.bind(this),\n      },\n    };\n\n    this.files = {\n      create: files.create.bind(this),\n      get: files.get.bind(this),\n      list: files.list.bind(this),\n      delete: files.delete.bind(this),\n    };\n\n    this.hardware = {\n      list: hardware.list.bind(this),\n    };\n\n    this.models = {\n      get: models.get.bind(this),\n      list: models.list.bind(this),\n      create: models.create.bind(this),\n      versions: {\n        list: models.versions.list.bind(this),\n        get: models.versions.get.bind(this),\n      },\n    };\n\n    this.predictions = {\n      create: predictions.create.bind(this),\n      get: predictions.get.bind(this),\n      cancel: predictions.cancel.bind(this),\n      list: predictions.list.bind(this),\n    };\n\n    this.trainings = {\n      create: trainings.create.bind(this),\n      get: trainings.get.bind(this),\n      cancel: trainings.cancel.bind(this),\n      list: trainings.list.bind(this),\n    };\n\n    this.webhooks = {\n      default: {\n        secret: {\n          get: webhooks.default.secret.get.bind(this),\n        },\n      },\n    };\n  }\n\n  /**\n   * Run a model and wait for its output.\n   *\n   * @param {string} ref - Required. The model version identifier in the format \"owner/name\" or \"owner/name:version\"\n   * @param {object} options\n   * @param {object} options.input - Required. An object with the model inputs\n   * @param {object} [options.wait] - Options for waiting for the prediction to finish\n   * @param {number} [options.wait.interval] - Polling interval in milliseconds. Defaults to 500\n   * @param {string} [options.webhook] - An HTTPS URL for receiving a webhook when the prediction has new output\n   * @param {string[]} [options.webhook_events_filter] - You can change which events trigger webhook requests by specifying webhook events (`start`|`output`|`logs`|`completed`)\n   * @param {AbortSignal} [options.signal] - AbortSignal to cancel the prediction\n   * @param {Function} [progress] - Callback function that receives the prediction object as it's updated. The function is called when the prediction is created, each time its updated while polling for completion, and when it's completed.\n   * @throws {Error} If the reference is invalid\n   * @throws {Error} If the prediction failed\n   * @returns {Promise<object>} - Resolves with the output of running the model\n   */\n  async run(ref, options, progress) {\n    const { wait, signal, ...data } = options;\n\n    const identifier = ModelVersionIdentifier.parse(ref);\n\n    let prediction;\n    if (identifier.version) {\n      prediction = await this.predictions.create({\n        ...data,\n        version: identifier.version,\n      });\n    } else if (identifier.owner && identifier.name) {\n      prediction = await this.predictions.create({\n        ...data,\n        model: `${identifier.owner}/${identifier.name}`,\n      });\n    } else {\n      throw new Error(\"Invalid model version identifier\");\n    }\n\n    // Call progress callback with the initial prediction object\n    if (progress) {\n      progress(prediction);\n    }\n\n    prediction = await this.wait(\n      prediction,\n      wait || {},\n      async (updatedPrediction) => {\n        // Call progress callback with the updated prediction object\n        if (progress) {\n          progress(updatedPrediction);\n        }\n\n        // We handle the cancel later in the function.\n        if (signal && signal.aborted) {\n          return true; // stop polling\n        }\n\n        return false; // continue polling\n      }\n    );\n\n    if (signal && signal.aborted) {\n      prediction = await this.predictions.cancel(prediction.id);\n    }\n\n    // Call progress callback with the completed prediction object\n    if (progress) {\n      progress(prediction);\n    }\n\n    if (prediction.status === \"failed\") {\n      throw new Error(`Prediction failed: ${prediction.error}`);\n    }\n\n    return prediction.output;\n  }\n\n  /**\n   * Make a request to the Replicate API.\n   *\n   * @param {string} route - REST API endpoint path\n   * @param {object} options - Request parameters\n   * @param {string} [options.method] - HTTP method. Defaults to GET\n   * @param {object} [options.params] - Query parameters\n   * @param {object|Headers} [options.headers] - HTTP headers\n   * @param {object} [options.data] - Body parameters\n   * @returns {Promise<Response>} - Resolves with the response object\n   * @throws {ApiError} If the request failed\n   */\n  async request(route, options) {\n    const { auth, baseUrl, userAgent } = this;\n\n    let url;\n    if (route instanceof URL) {\n      url = route;\n    } else {\n      url = new URL(\n        route.startsWith(\"/\") ? route.slice(1) : route,\n        baseUrl.endsWith(\"/\") ? baseUrl : `${baseUrl}/`\n      );\n    }\n\n    const { method = \"GET\", params = {}, data } = options;\n\n    for (const [key, value] of Object.entries(params)) {\n      url.searchParams.append(key, value);\n    }\n\n    const headers = {\n      \"Content-Type\": \"application/json\",\n      \"User-Agent\": userAgent,\n    };\n    if (auth) {\n      headers[\"Authorization\"] = `Bearer ${auth}`;\n    }\n    if (options.headers) {\n      for (const [key, value] of Object.entries(options.headers)) {\n        headers[key] = value;\n      }\n    }\n\n    let body = undefined;\n    if (data instanceof FormData) {\n      body = data;\n      // biome-ignore lint/performance/noDelete:\n      delete headers[\"Content-Type\"]; // Use automatic content type header\n    } else if (data) {\n      body = JSON.stringify(data);\n    }\n\n    const init = {\n      method,\n      headers,\n      body,\n    };\n\n    const shouldRetry =\n      method === \"GET\"\n        ? (response) => response.status === 429 || response.status >= 500\n        : (response) => response.status === 429;\n\n    // Workaround to fix `TypeError: Illegal invocation` error in Cloudflare Workers\n    // https://github.com/replicate/replicate-javascript/issues/134\n    const _fetch = this.fetch; // eslint-disable-line no-underscore-dangle\n    const response = await withAutomaticRetries(async () => _fetch(url, init), {\n      shouldRetry,\n    });\n\n    if (!response.ok) {\n      const request = new Request(url, init);\n      const responseText = await response.text();\n      throw new ApiError(\n        `Request to ${url} failed with status ${response.status} ${response.statusText}: ${responseText}.`,\n        request,\n        response\n      );\n    }\n\n    return response;\n  }\n\n  /**\n   * Stream a model and wait for its output.\n   *\n   * @param {string} identifier - Required. The model version identifier in the format \"{owner}/{name}:{version}\"\n   * @param {object} options\n   * @param {object} options.input - Required. An object with the model inputs\n   * @param {string} [options.webhook] - An HTTPS URL for receiving a webhook when the prediction has new output\n   * @param {string[]} [options.webhook_events_filter] - You can change which events trigger webhook requests by specifying webhook events (`start`|`output`|`logs`|`completed`)\n   * @param {AbortSignal} [options.signal] - AbortSignal to cancel the prediction\n   * @throws {Error} If the prediction failed\n   * @yields {ServerSentEvent} Each streamed event from the prediction\n   */\n  async *stream(ref, options) {\n    const { wait, signal, ...data } = options;\n\n    const identifier = ModelVersionIdentifier.parse(ref);\n\n    let prediction;\n    if (identifier.version) {\n      prediction = await this.predictions.create({\n        ...data,\n        version: identifier.version,\n        stream: true,\n      });\n    } else if (identifier.owner && identifier.name) {\n      prediction = await this.predictions.create({\n        ...data,\n        model: `${identifier.owner}/${identifier.name}`,\n        stream: true,\n      });\n    } else {\n      throw new Error(\"Invalid model version identifier\");\n    }\n\n    if (prediction.urls && prediction.urls.stream) {\n      const stream = createReadableStream({\n        url: prediction.urls.stream,\n        fetch: this.fetch,\n        ...(signal ? { options: { signal } } : {}),\n      });\n\n      yield* streamAsyncIterator(stream);\n    } else {\n      throw new Error(\"Prediction does not support streaming\");\n    }\n  }\n\n  /**\n   * Paginate through a list of results.\n   *\n   * @generator\n   * @example\n   * for await (const page of replicate.paginate(replicate.predictions.list) {\n   *    console.log(page);\n   * }\n   * @param {Function} endpoint - Function that returns a promise for the next page of results\n   * @yields {object[]} Each page of results\n   */\n  async *paginate(endpoint) {\n    const response = await endpoint();\n    yield response.results;\n    if (response.next) {\n      const nextPage = () =>\n        this.request(response.next, { method: \"GET\" }).then((r) => r.json());\n      yield* this.paginate(nextPage);\n    }\n  }\n\n  /**\n   * Wait for a prediction to finish.\n   *\n   * If the prediction has already finished,\n   * this function returns immediately.\n   * Otherwise, it polls the API until the prediction finishes.\n   *\n   * @async\n   * @param {object} prediction - Prediction object\n   * @param {object} options - Options\n   * @param {number} [options.interval] - Polling interval in milliseconds. Defaults to 500\n   * @param {Function} [stop] - Async callback function that is called after each polling attempt. Receives the prediction object as an argument. Return false to cancel polling.\n   * @throws {Error} If the prediction doesn't complete within the maximum number of attempts\n   * @throws {Error} If the prediction failed\n   * @returns {Promise<object>} Resolves with the completed prediction object\n   */\n  async wait(prediction, options, stop) {\n    const { id } = prediction;\n    if (!id) {\n      throw new Error(\"Invalid prediction\");\n    }\n\n    if (\n      prediction.status === \"succeeded\" ||\n      prediction.status === \"failed\" ||\n      prediction.status === \"canceled\"\n    ) {\n      return prediction;\n    }\n\n    // eslint-disable-next-line no-promise-executor-return\n    const sleep = (ms) => new Promise((resolve) => setTimeout(resolve, ms));\n\n    const interval = (options && options.interval) || 500;\n\n    let updatedPrediction = await this.predictions.get(id);\n\n    while (\n      updatedPrediction.status !== \"succeeded\" &&\n      updatedPrediction.status !== \"failed\" &&\n      updatedPrediction.status !== \"canceled\"\n    ) {\n      /* eslint-disable no-await-in-loop */\n      if (stop && (await stop(updatedPrediction)) === true) {\n        break;\n      }\n\n      await sleep(interval);\n      updatedPrediction = await this.predictions.get(prediction.id);\n      /* eslint-enable no-await-in-loop */\n    }\n\n    if (updatedPrediction.status === \"failed\") {\n      throw new Error(`Prediction failed: ${updatedPrediction.error}`);\n    }\n\n    return updatedPrediction;\n  }\n}\n\nmodule.exports = Replicate;\nmodule.exports.validateWebhook = validateWebhook;\nmodule.exports.parseProgressFromLogs = parseProgressFromLogs;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/replicate/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/replicate/lib/accounts.js":
/*!************************************************!*\
  !*** ./node_modules/replicate/lib/accounts.js ***!
  \************************************************/
/***/ ((module) => {

eval("/**\n * Get the current account\n *\n * @returns {Promise<object>} Resolves with the current account\n */\nasync function getCurrentAccount() {\n  const response = await this.request(\"/account\", {\n    method: \"GET\",\n  });\n\n  return response.json();\n}\n\nmodule.exports = {\n  current: getCurrentAccount,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcmVwbGljYXRlL2xpYi9hY2NvdW50cy5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLGlCQUFpQjtBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQTs7QUFFQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90aGUtY2FudmFzLy4vbm9kZV9tb2R1bGVzL3JlcGxpY2F0ZS9saWIvYWNjb3VudHMuanM/MmZhYyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEdldCB0aGUgY3VycmVudCBhY2NvdW50XG4gKlxuICogQHJldHVybnMge1Byb21pc2U8b2JqZWN0Pn0gUmVzb2x2ZXMgd2l0aCB0aGUgY3VycmVudCBhY2NvdW50XG4gKi9cbmFzeW5jIGZ1bmN0aW9uIGdldEN1cnJlbnRBY2NvdW50KCkge1xuICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMucmVxdWVzdChcIi9hY2NvdW50XCIsIHtcbiAgICBtZXRob2Q6IFwiR0VUXCIsXG4gIH0pO1xuXG4gIHJldHVybiByZXNwb25zZS5qc29uKCk7XG59XG5cbm1vZHVsZS5leHBvcnRzID0ge1xuICBjdXJyZW50OiBnZXRDdXJyZW50QWNjb3VudCxcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/replicate/lib/accounts.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/replicate/lib/collections.js":
/*!***************************************************!*\
  !*** ./node_modules/replicate/lib/collections.js ***!
  \***************************************************/
/***/ ((module) => {

eval("/**\n * Fetch a model collection\n *\n * @param {string} collection_slug - Required. The slug of the collection. See http://replicate.com/collections\n * @returns {Promise<object>} - Resolves with the collection data\n */\nasync function getCollection(collection_slug) {\n  const response = await this.request(`/collections/${collection_slug}`, {\n    method: \"GET\",\n  });\n\n  return response.json();\n}\n\n/**\n * Fetch a list of model collections\n *\n * @returns {Promise<object>} - Resolves with the collections data\n */\nasync function listCollections() {\n  const response = await this.request(\"/collections\", {\n    method: \"GET\",\n  });\n\n  return response.json();\n}\n\nmodule.exports = { get: getCollection, list: listCollections };\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcmVwbGljYXRlL2xpYi9jb2xsZWN0aW9ucy5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsYUFBYSxpQkFBaUI7QUFDOUI7QUFDQTtBQUNBLHNEQUFzRCxnQkFBZ0I7QUFDdEU7QUFDQSxHQUFHOztBQUVIO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsYUFBYSxpQkFBaUI7QUFDOUI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0E7O0FBRUEsbUJBQW1CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGhlLWNhbnZhcy8uL25vZGVfbW9kdWxlcy9yZXBsaWNhdGUvbGliL2NvbGxlY3Rpb25zLmpzPzhhYmIiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBGZXRjaCBhIG1vZGVsIGNvbGxlY3Rpb25cbiAqXG4gKiBAcGFyYW0ge3N0cmluZ30gY29sbGVjdGlvbl9zbHVnIC0gUmVxdWlyZWQuIFRoZSBzbHVnIG9mIHRoZSBjb2xsZWN0aW9uLiBTZWUgaHR0cDovL3JlcGxpY2F0ZS5jb20vY29sbGVjdGlvbnNcbiAqIEByZXR1cm5zIHtQcm9taXNlPG9iamVjdD59IC0gUmVzb2x2ZXMgd2l0aCB0aGUgY29sbGVjdGlvbiBkYXRhXG4gKi9cbmFzeW5jIGZ1bmN0aW9uIGdldENvbGxlY3Rpb24oY29sbGVjdGlvbl9zbHVnKSB7XG4gIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5yZXF1ZXN0KGAvY29sbGVjdGlvbnMvJHtjb2xsZWN0aW9uX3NsdWd9YCwge1xuICAgIG1ldGhvZDogXCJHRVRcIixcbiAgfSk7XG5cbiAgcmV0dXJuIHJlc3BvbnNlLmpzb24oKTtcbn1cblxuLyoqXG4gKiBGZXRjaCBhIGxpc3Qgb2YgbW9kZWwgY29sbGVjdGlvbnNcbiAqXG4gKiBAcmV0dXJucyB7UHJvbWlzZTxvYmplY3Q+fSAtIFJlc29sdmVzIHdpdGggdGhlIGNvbGxlY3Rpb25zIGRhdGFcbiAqL1xuYXN5bmMgZnVuY3Rpb24gbGlzdENvbGxlY3Rpb25zKCkge1xuICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMucmVxdWVzdChcIi9jb2xsZWN0aW9uc1wiLCB7XG4gICAgbWV0aG9kOiBcIkdFVFwiLFxuICB9KTtcblxuICByZXR1cm4gcmVzcG9uc2UuanNvbigpO1xufVxuXG5tb2R1bGUuZXhwb3J0cyA9IHsgZ2V0OiBnZXRDb2xsZWN0aW9uLCBsaXN0OiBsaXN0Q29sbGVjdGlvbnMgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/replicate/lib/collections.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/replicate/lib/deployments.js":
/*!***************************************************!*\
  !*** ./node_modules/replicate/lib/deployments.js ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const { transformFileInputs } = __webpack_require__(/*! ./util */ \"(rsc)/./node_modules/replicate/lib/util.js\");\n\n/**\n * Create a new prediction with a deployment\n *\n * @param {string} deployment_owner - Required. The username of the user or organization who owns the deployment\n * @param {string} deployment_name - Required. The name of the deployment\n * @param {object} options\n * @param {object} options.input - Required. An object with the model inputs\n * @param {boolean} [options.stream] - Whether to stream the prediction output. Defaults to false\n * @param {string} [options.webhook] - An HTTPS URL for receiving a webhook when the prediction has new output\n * @param {string[]} [options.webhook_events_filter] - You can change which events trigger webhook requests by specifying webhook events (`start`|`output`|`logs`|`completed`)\n * @returns {Promise<object>} Resolves with the created prediction data\n */\nasync function createPrediction(deployment_owner, deployment_name, options) {\n  const { stream, input, ...data } = options;\n\n  if (data.webhook) {\n    try {\n      // eslint-disable-next-line no-new\n      new URL(data.webhook);\n    } catch (err) {\n      throw new Error(\"Invalid webhook URL\");\n    }\n  }\n\n  const response = await this.request(\n    `/deployments/${deployment_owner}/${deployment_name}/predictions`,\n    {\n      method: \"POST\",\n      data: {\n        ...data,\n        input: await transformFileInputs(\n          this,\n          input,\n          this.fileEncodingStrategy\n        ),\n        stream,\n      },\n    }\n  );\n\n  return response.json();\n}\n\n/**\n * Get a deployment\n *\n * @param {string} deployment_owner - Required. The username of the user or organization who owns the deployment\n * @param {string} deployment_name - Required. The name of the deployment\n * @returns {Promise<object>} Resolves with the deployment data\n */\nasync function getDeployment(deployment_owner, deployment_name) {\n  const response = await this.request(\n    `/deployments/${deployment_owner}/${deployment_name}`,\n    {\n      method: \"GET\",\n    }\n  );\n\n  return response.json();\n}\n\n/**\n * @typedef {Object} DeploymentCreateRequest - Request body for `deployments.create`\n * @property {string} name - the name of the deployment\n * @property {string} model - the full name of the model that you want to deploy e.g. stability-ai/sdxl\n * @property {string} version - the 64-character string ID of the model version that you want to deploy\n * @property {string} hardware - the SKU for the hardware used to run the model, via `replicate.hardware.list()`\n * @property {number} min_instances - the minimum number of instances for scaling\n * @property {number} max_instances - the maximum number of instances for scaling\n */\n\n/**\n * Create a deployment\n *\n * @param {DeploymentCreateRequest} config - Required. The deployment config.\n * @returns {Promise<object>} Resolves with the deployment data\n */\nasync function createDeployment(deployment_config) {\n  const response = await this.request(\"/deployments\", {\n    method: \"POST\",\n    data: deployment_config,\n  });\n\n  return response.json();\n}\n\n/**\n * @typedef {Object} DeploymentUpdateRequest - Request body for `deployments.update`\n * @property {string} version - the 64-character string ID of the model version that you want to deploy\n * @property {string} hardware - the SKU for the hardware used to run the model, via `replicate.hardware.list()`\n * @property {number} min_instances - the minimum number of instances for scaling\n * @property {number} max_instances - the maximum number of instances for scaling\n */\n\n/**\n * Update an existing deployment\n *\n * @param {string} deployment_owner - Required. The username of the user or organization who owns the deployment\n * @param {string} deployment_name - Required. The name of the deployment\n * @param {DeploymentUpdateRequest} deployment_config - Required. The deployment changes.\n * @returns {Promise<object>} Resolves with the deployment data\n */\nasync function updateDeployment(\n  deployment_owner,\n  deployment_name,\n  deployment_config\n) {\n  const response = await this.request(\n    `/deployments/${deployment_owner}/${deployment_name}`,\n    {\n      method: \"PATCH\",\n      data: deployment_config,\n    }\n  );\n\n  return response.json();\n}\n\n/**\n * Delete a deployment\n *\n * @param {string} deployment_owner - Required. The username of the user or organization who owns the deployment\n * @param {string} deployment_name - Required. The name of the deployment\n * @returns {Promise<boolean>} Resolves with true if the deployment was deleted\n */\nasync function deleteDeployment(deployment_owner, deployment_name) {\n  const response = await this.request(\n    `/deployments/${deployment_owner}/${deployment_name}`,\n    {\n      method: \"DELETE\",\n    }\n  );\n\n  return response.status === 204;\n}\n\n/**\n * List all deployments\n *\n * @returns {Promise<object>} - Resolves with a page of deployments\n */\nasync function listDeployments() {\n  const response = await this.request(\"/deployments\", {\n    method: \"GET\",\n  });\n\n  return response.json();\n}\n\nmodule.exports = {\n  predictions: {\n    create: createPrediction,\n  },\n  get: getDeployment,\n  create: createDeployment,\n  update: updateDeployment,\n  list: listDeployments,\n  delete: deleteDeployment,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/replicate/lib/deployments.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/replicate/lib/error.js":
/*!*********************************************!*\
  !*** ./node_modules/replicate/lib/error.js ***!
  \*********************************************/
/***/ ((module) => {

eval("/**\n * A representation of an API error.\n */\nclass ApiError extends Error {\n  /**\n   * Creates a representation of an API error.\n   *\n   * @param {string} message - Error message\n   * @param {Request} request - HTTP request\n   * @param {Response} response - HTTP response\n   * @returns {ApiError} - An instance of ApiError\n   */\n  constructor(message, request, response) {\n    super(message);\n    this.name = \"ApiError\";\n    this.request = request;\n    this.response = response;\n  }\n}\n\nmodule.exports = ApiError;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcmVwbGljYXRlL2xpYi9lcnJvci5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWEsUUFBUTtBQUNyQixhQUFhLFNBQVM7QUFDdEIsYUFBYSxVQUFVO0FBQ3ZCLGVBQWUsVUFBVTtBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGhlLWNhbnZhcy8uL25vZGVfbW9kdWxlcy9yZXBsaWNhdGUvbGliL2Vycm9yLmpzPzg3MjAiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBBIHJlcHJlc2VudGF0aW9uIG9mIGFuIEFQSSBlcnJvci5cbiAqL1xuY2xhc3MgQXBpRXJyb3IgZXh0ZW5kcyBFcnJvciB7XG4gIC8qKlxuICAgKiBDcmVhdGVzIGEgcmVwcmVzZW50YXRpb24gb2YgYW4gQVBJIGVycm9yLlxuICAgKlxuICAgKiBAcGFyYW0ge3N0cmluZ30gbWVzc2FnZSAtIEVycm9yIG1lc3NhZ2VcbiAgICogQHBhcmFtIHtSZXF1ZXN0fSByZXF1ZXN0IC0gSFRUUCByZXF1ZXN0XG4gICAqIEBwYXJhbSB7UmVzcG9uc2V9IHJlc3BvbnNlIC0gSFRUUCByZXNwb25zZVxuICAgKiBAcmV0dXJucyB7QXBpRXJyb3J9IC0gQW4gaW5zdGFuY2Ugb2YgQXBpRXJyb3JcbiAgICovXG4gIGNvbnN0cnVjdG9yKG1lc3NhZ2UsIHJlcXVlc3QsIHJlc3BvbnNlKSB7XG4gICAgc3VwZXIobWVzc2FnZSk7XG4gICAgdGhpcy5uYW1lID0gXCJBcGlFcnJvclwiO1xuICAgIHRoaXMucmVxdWVzdCA9IHJlcXVlc3Q7XG4gICAgdGhpcy5yZXNwb25zZSA9IHJlc3BvbnNlO1xuICB9XG59XG5cbm1vZHVsZS5leHBvcnRzID0gQXBpRXJyb3I7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/replicate/lib/error.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/replicate/lib/files.js":
/*!*********************************************!*\
  !*** ./node_modules/replicate/lib/files.js ***!
  \*********************************************/
/***/ ((module) => {

eval("/**\n * Create a file\n *\n * @param {object} file - Required. The file object.\n * @param {object} metadata - Optional. User-provided metadata associated with the file.\n * @returns {Promise<object>} - Resolves with the file data\n */\nasync function createFile(file, metadata = {}) {\n  const form = new FormData();\n\n  let filename;\n  let blob;\n  if (file instanceof Blob) {\n    filename = file.name || `blob_${Date.now()}`;\n    blob = file;\n  } else if (Buffer.isBuffer(file)) {\n    filename = `buffer_${Date.now()}`;\n    const bytes = new Uint8Array(file);\n    blob = new Blob([bytes], {\n      type: \"application/octet-stream\",\n      name: filename,\n    });\n  } else {\n    throw new Error(\"Invalid file argument, must be a Blob, File or Buffer\");\n  }\n\n  form.append(\"content\", blob, filename);\n  form.append(\n    \"metadata\",\n    new Blob([JSON.stringify(metadata)], { type: \"application/json\" })\n  );\n\n  const response = await this.request(\"/files\", {\n    method: \"POST\",\n    data: form,\n    headers: {\n      \"Content-Type\": \"multipart/form-data\",\n    },\n  });\n\n  return response.json();\n}\n\n/**\n * List all files\n *\n * @returns {Promise<object>} - Resolves with the files data\n */\nasync function listFiles() {\n  const response = await this.request(\"/files\", {\n    method: \"GET\",\n  });\n\n  return response.json();\n}\n\n/**\n * Get a file\n *\n * @param {string} file_id - Required. The ID of the file.\n * @returns {Promise<object>} - Resolves with the file data\n */\nasync function getFile(file_id) {\n  const response = await this.request(`/files/${file_id}`, {\n    method: \"GET\",\n  });\n\n  return response.json();\n}\n\n/**\n * Delete a file\n *\n * @param {string} file_id - Required. The ID of the file.\n * @returns {Promise<boolean>} - Resolves with true if the file was deleted\n */\nasync function deleteFile(file_id) {\n  const response = await this.request(`/files/${file_id}`, {\n    method: \"DELETE\",\n  });\n\n  return response.status === 204;\n}\n\nmodule.exports = {\n  create: createFile,\n  list: listFiles,\n  get: getFile,\n  delete: deleteFile,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/replicate/lib/files.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/replicate/lib/hardware.js":
/*!************************************************!*\
  !*** ./node_modules/replicate/lib/hardware.js ***!
  \************************************************/
/***/ ((module) => {

eval("/**\n * List hardware\n *\n * @returns {Promise<object[]>} Resolves with the array of hardware\n */\nasync function listHardware() {\n  const response = await this.request(\"/hardware\", {\n    method: \"GET\",\n  });\n\n  return response.json();\n}\n\nmodule.exports = {\n  list: listHardware,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcmVwbGljYXRlL2xpYi9oYXJkd2FyZS5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLG1CQUFtQjtBQUNoQztBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQTs7QUFFQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90aGUtY2FudmFzLy4vbm9kZV9tb2R1bGVzL3JlcGxpY2F0ZS9saWIvaGFyZHdhcmUuanM/NzUyYiJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIExpc3QgaGFyZHdhcmVcbiAqXG4gKiBAcmV0dXJucyB7UHJvbWlzZTxvYmplY3RbXT59IFJlc29sdmVzIHdpdGggdGhlIGFycmF5IG9mIGhhcmR3YXJlXG4gKi9cbmFzeW5jIGZ1bmN0aW9uIGxpc3RIYXJkd2FyZSgpIHtcbiAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLnJlcXVlc3QoXCIvaGFyZHdhcmVcIiwge1xuICAgIG1ldGhvZDogXCJHRVRcIixcbiAgfSk7XG5cbiAgcmV0dXJuIHJlc3BvbnNlLmpzb24oKTtcbn1cblxubW9kdWxlLmV4cG9ydHMgPSB7XG4gIGxpc3Q6IGxpc3RIYXJkd2FyZSxcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/replicate/lib/hardware.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/replicate/lib/identifier.js":
/*!**************************************************!*\
  !*** ./node_modules/replicate/lib/identifier.js ***!
  \**************************************************/
/***/ ((module) => {

eval("/*\n * A reference to a model version in the format `owner/name` or `owner/name:version`.\n */\nclass ModelVersionIdentifier {\n  /*\n   * @param {string} Required. The model owner.\n   * @param {string} Required. The model name.\n   * @param {string} The model version.\n   */\n  constructor(owner, name, version = null) {\n    this.owner = owner;\n    this.name = name;\n    this.version = version;\n  }\n\n  /*\n   * Parse a reference to a model version\n   *\n   * @param {string}\n   * @returns {ModelVersionIdentifier}\n   * @throws {Error} If the reference is invalid.\n   */\n  static parse(ref) {\n    const match = ref.match(\n      /^(?<owner>[^/]+)\\/(?<name>[^/:]+)(:(?<version>.+))?$/\n    );\n    if (!match) {\n      throw new Error(\n        `Invalid reference to model version: ${ref}. Expected format: owner/name or owner/name:version`\n      );\n    }\n\n    const { owner, name, version } = match.groups;\n\n    return new ModelVersionIdentifier(owner, name, version);\n  }\n}\n\nmodule.exports = ModelVersionIdentifier;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcmVwbGljYXRlL2xpYi9pZGVudGlmaWVyLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLFFBQVE7QUFDckIsYUFBYSxRQUFRO0FBQ3JCLGFBQWEsUUFBUTtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiLGVBQWU7QUFDZixjQUFjLE9BQU87QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrQ0FBK0MsSUFBSTtBQUNuRDtBQUNBOztBQUVBLFlBQVksdUJBQXVCOztBQUVuQztBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90aGUtY2FudmFzLy4vbm9kZV9tb2R1bGVzL3JlcGxpY2F0ZS9saWIvaWRlbnRpZmllci5qcz84OTFjIl0sInNvdXJjZXNDb250ZW50IjpbIi8qXG4gKiBBIHJlZmVyZW5jZSB0byBhIG1vZGVsIHZlcnNpb24gaW4gdGhlIGZvcm1hdCBgb3duZXIvbmFtZWAgb3IgYG93bmVyL25hbWU6dmVyc2lvbmAuXG4gKi9cbmNsYXNzIE1vZGVsVmVyc2lvbklkZW50aWZpZXIge1xuICAvKlxuICAgKiBAcGFyYW0ge3N0cmluZ30gUmVxdWlyZWQuIFRoZSBtb2RlbCBvd25lci5cbiAgICogQHBhcmFtIHtzdHJpbmd9IFJlcXVpcmVkLiBUaGUgbW9kZWwgbmFtZS5cbiAgICogQHBhcmFtIHtzdHJpbmd9IFRoZSBtb2RlbCB2ZXJzaW9uLlxuICAgKi9cbiAgY29uc3RydWN0b3Iob3duZXIsIG5hbWUsIHZlcnNpb24gPSBudWxsKSB7XG4gICAgdGhpcy5vd25lciA9IG93bmVyO1xuICAgIHRoaXMubmFtZSA9IG5hbWU7XG4gICAgdGhpcy52ZXJzaW9uID0gdmVyc2lvbjtcbiAgfVxuXG4gIC8qXG4gICAqIFBhcnNlIGEgcmVmZXJlbmNlIHRvIGEgbW9kZWwgdmVyc2lvblxuICAgKlxuICAgKiBAcGFyYW0ge3N0cmluZ31cbiAgICogQHJldHVybnMge01vZGVsVmVyc2lvbklkZW50aWZpZXJ9XG4gICAqIEB0aHJvd3Mge0Vycm9yfSBJZiB0aGUgcmVmZXJlbmNlIGlzIGludmFsaWQuXG4gICAqL1xuICBzdGF0aWMgcGFyc2UocmVmKSB7XG4gICAgY29uc3QgbWF0Y2ggPSByZWYubWF0Y2goXG4gICAgICAvXig/PG93bmVyPlteL10rKVxcLyg/PG5hbWU+W14vOl0rKSg6KD88dmVyc2lvbj4uKykpPyQvXG4gICAgKTtcbiAgICBpZiAoIW1hdGNoKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAgIGBJbnZhbGlkIHJlZmVyZW5jZSB0byBtb2RlbCB2ZXJzaW9uOiAke3JlZn0uIEV4cGVjdGVkIGZvcm1hdDogb3duZXIvbmFtZSBvciBvd25lci9uYW1lOnZlcnNpb25gXG4gICAgICApO1xuICAgIH1cblxuICAgIGNvbnN0IHsgb3duZXIsIG5hbWUsIHZlcnNpb24gfSA9IG1hdGNoLmdyb3VwcztcblxuICAgIHJldHVybiBuZXcgTW9kZWxWZXJzaW9uSWRlbnRpZmllcihvd25lciwgbmFtZSwgdmVyc2lvbik7XG4gIH1cbn1cblxubW9kdWxlLmV4cG9ydHMgPSBNb2RlbFZlcnNpb25JZGVudGlmaWVyO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/replicate/lib/identifier.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/replicate/lib/models.js":
/*!**********************************************!*\
  !*** ./node_modules/replicate/lib/models.js ***!
  \**********************************************/
/***/ ((module) => {

eval("/**\n * Get information about a model\n *\n * @param {string} model_owner - Required. The name of the user or organization that owns the model\n * @param {string} model_name - Required. The name of the model\n * @returns {Promise<object>} Resolves with the model data\n */\nasync function getModel(model_owner, model_name) {\n  const response = await this.request(`/models/${model_owner}/${model_name}`, {\n    method: \"GET\",\n  });\n\n  return response.json();\n}\n\n/**\n * List model versions\n *\n * @param {string} model_owner - Required. The name of the user or organization that owns the model\n * @param {string} model_name - Required. The name of the model\n * @returns {Promise<object>} Resolves with the list of model versions\n */\nasync function listModelVersions(model_owner, model_name) {\n  const response = await this.request(\n    `/models/${model_owner}/${model_name}/versions`,\n    {\n      method: \"GET\",\n    }\n  );\n\n  return response.json();\n}\n\n/**\n * Get a specific model version\n *\n * @param {string} model_owner - Required. The name of the user or organization that owns the model\n * @param {string} model_name - Required. The name of the model\n * @param {string} version_id - Required. The model version\n * @returns {Promise<object>} Resolves with the model version data\n */\nasync function getModelVersion(model_owner, model_name, version_id) {\n  const response = await this.request(\n    `/models/${model_owner}/${model_name}/versions/${version_id}`,\n    {\n      method: \"GET\",\n    }\n  );\n\n  return response.json();\n}\n\n/**\n * List all public models\n *\n * @returns {Promise<object>} Resolves with the model version data\n */\nasync function listModels() {\n  const response = await this.request(\"/models\", {\n    method: \"GET\",\n  });\n\n  return response.json();\n}\n\n/**\n * Create a new model\n *\n * @param {string} model_owner - Required. The name of the user or organization that will own the model. This must be the same as the user or organization that is making the API request. In other words, the API token used in the request must belong to this user or organization.\n * @param {string} model_name - Required. The name of the model. This must be unique among all models owned by the user or organization.\n * @param {object} options\n * @param {(\"public\"|\"private\")} options.visibility - Required. Whether the model should be public or private. A public model can be viewed and run by anyone, whereas a private model can be viewed and run only by the user or organization members that own the model.\n * @param {string} options.hardware - Required. The SKU for the hardware used to run the model. Possible values can be found by calling `Replicate.hardware.list()`.\n * @param {string} options.description - A description of the model.\n * @param {string} options.github_url - A URL for the model's source code on GitHub.\n * @param {string} options.paper_url - A URL for the model's paper.\n * @param {string} options.license_url - A URL for the model's license.\n * @param {string} options.cover_image_url - A URL for the model's cover image. This should be an image file.\n * @returns {Promise<object>} Resolves with the model version data\n */\nasync function createModel(model_owner, model_name, options) {\n  const data = { owner: model_owner, name: model_name, ...options };\n\n  const response = await this.request(\"/models\", {\n    method: \"POST\",\n    data,\n  });\n\n  return response.json();\n}\n\nmodule.exports = {\n  get: getModel,\n  list: listModels,\n  create: createModel,\n  versions: { list: listModelVersions, get: getModelVersion },\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/replicate/lib/models.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/replicate/lib/predictions.js":
/*!***************************************************!*\
  !*** ./node_modules/replicate/lib/predictions.js ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const { transformFileInputs } = __webpack_require__(/*! ./util */ \"(rsc)/./node_modules/replicate/lib/util.js\");\n\n/**\n * Create a new prediction\n *\n * @param {object} options\n * @param {string} options.model - The model.\n * @param {string} options.version - The model version.\n * @param {object} options.input - Required. An object with the model inputs\n * @param {string} [options.webhook] - An HTTPS URL for receiving a webhook when the prediction has new output\n * @param {string[]} [options.webhook_events_filter] - You can change which events trigger webhook requests by specifying webhook events (`start`|`output`|`logs`|`completed`)\n * @param {boolean} [options.stream] - Whether to stream the prediction output. Defaults to false\n * @returns {Promise<object>} Resolves with the created prediction\n */\nasync function createPrediction(options) {\n  const { model, version, stream, input, ...data } = options;\n\n  if (data.webhook) {\n    try {\n      // eslint-disable-next-line no-new\n      new URL(data.webhook);\n    } catch (err) {\n      throw new Error(\"Invalid webhook URL\");\n    }\n  }\n\n  let response;\n  if (version) {\n    response = await this.request(\"/predictions\", {\n      method: \"POST\",\n      data: {\n        ...data,\n        input: await transformFileInputs(\n          this,\n          input,\n          this.fileEncodingStrategy\n        ),\n        version,\n        stream,\n      },\n    });\n  } else if (model) {\n    response = await this.request(`/models/${model}/predictions`, {\n      method: \"POST\",\n      data: {\n        ...data,\n        input: await transformFileInputs(\n          this,\n          input,\n          this.fileEncodingStrategy\n        ),\n        stream,\n      },\n    });\n  } else {\n    throw new Error(\"Either model or version must be specified\");\n  }\n\n  return response.json();\n}\n\n/**\n * Fetch a prediction by ID\n *\n * @param {number} prediction_id - Required. The prediction ID\n * @returns {Promise<object>} Resolves with the prediction data\n */\nasync function getPrediction(prediction_id) {\n  const response = await this.request(`/predictions/${prediction_id}`, {\n    method: \"GET\",\n  });\n\n  return response.json();\n}\n\n/**\n * Cancel a prediction by ID\n *\n * @param {string} prediction_id - Required. The training ID\n * @returns {Promise<object>} Resolves with the data for the training\n */\nasync function cancelPrediction(prediction_id) {\n  const response = await this.request(`/predictions/${prediction_id}/cancel`, {\n    method: \"POST\",\n  });\n\n  return response.json();\n}\n\n/**\n * List all predictions\n *\n * @returns {Promise<object>} - Resolves with a page of predictions\n */\nasync function listPredictions() {\n  const response = await this.request(\"/predictions\", {\n    method: \"GET\",\n  });\n\n  return response.json();\n}\n\nmodule.exports = {\n  create: createPrediction,\n  get: getPrediction,\n  cancel: cancelPrediction,\n  list: listPredictions,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/replicate/lib/predictions.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/replicate/lib/stream.js":
/*!**********************************************!*\
  !*** ./node_modules/replicate/lib/stream.js ***!
  \**********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Attempt to use readable-stream if available, attempt to use the built-in stream module.\n\nconst ApiError = __webpack_require__(/*! ./error */ \"(rsc)/./node_modules/replicate/lib/error.js\");\nconst { streamAsyncIterator } = __webpack_require__(/*! ./util */ \"(rsc)/./node_modules/replicate/lib/util.js\");\nconst {\n  EventSourceParserStream,\n} = __webpack_require__(/*! ../vendor/eventsource-parser/stream */ \"(rsc)/./node_modules/replicate/vendor/eventsource-parser/stream.js\");\nconst { TextDecoderStream } =\n  typeof globalThis.TextDecoderStream === \"undefined\"\n    ? __webpack_require__(/*! ../vendor/streams-text-encoding/text-decoder-stream */ \"(rsc)/./node_modules/replicate/vendor/streams-text-encoding/text-decoder-stream.js\")\n    : globalThis;\n\n/**\n * A server-sent event.\n */\nclass ServerSentEvent {\n  /**\n   * Create a new server-sent event.\n   *\n   * @param {string} event The event name.\n   * @param {string} data The event data.\n   * @param {string} id The event ID.\n   * @param {number} retry The retry time.\n   */\n  constructor(event, data, id, retry) {\n    this.event = event;\n    this.data = data;\n    this.id = id;\n    this.retry = retry;\n  }\n\n  /**\n   * Convert the event to a string.\n   */\n  toString() {\n    if (this.event === \"output\") {\n      return this.data;\n    }\n\n    return \"\";\n  }\n}\n\n/**\n * Create a new stream of server-sent events.\n *\n * @param {object} config\n * @param {string} config.url The URL to connect to.\n * @param {typeof fetch} [config.fetch] The URL to connect to.\n * @param {object} [config.options] The EventSource options.\n * @returns {ReadableStream<ServerSentEvent> & AsyncIterable<ServerSentEvent>}\n */\nfunction createReadableStream({ url, fetch, options = {} }) {\n  return new ReadableStream({\n    async start(controller) {\n      const init = {\n        ...options,\n        headers: {\n          ...options.headers,\n          Accept: \"text/event-stream\",\n        },\n      };\n      const response = await fetch(url, init);\n\n      if (!response.ok) {\n        const text = await response.text();\n        const request = new Request(url, init);\n        controller.error(\n          new ApiError(\n            `Request to ${url} failed with status ${response.status}: ${text}`,\n            request,\n            response\n          )\n        );\n      }\n\n      const stream = response.body\n        .pipeThrough(new TextDecoderStream())\n        .pipeThrough(new EventSourceParserStream());\n\n      for await (const event of streamAsyncIterator(stream)) {\n        if (event.event === \"error\") {\n          controller.error(new Error(event.data));\n          break;\n        }\n\n        controller.enqueue(\n          new ServerSentEvent(event.event, event.data, event.id)\n        );\n\n        if (event.event === \"done\") {\n          break;\n        }\n      }\n\n      controller.close();\n    },\n  });\n}\n\nmodule.exports = {\n  createReadableStream,\n  ServerSentEvent,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/replicate/lib/stream.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/replicate/lib/trainings.js":
/*!*************************************************!*\
  !*** ./node_modules/replicate/lib/trainings.js ***!
  \*************************************************/
/***/ ((module) => {

eval("/**\n * Create a new training\n *\n * @param {string} model_owner - Required. The username of the user or organization who owns the model\n * @param {string} model_name - Required. The name of the model\n * @param {string} version_id - Required. The version ID\n * @param {object} options\n * @param {string} options.destination - Required. The destination for the trained version in the form \"{username}/{model_name}\"\n * @param {object} options.input - Required. An object with the model inputs\n * @param {string} [options.webhook] - An HTTPS URL for receiving a webhook when the training updates\n * @param {string[]} [options.webhook_events_filter] - You can change which events trigger webhook requests by specifying webhook events (`start`|`output`|`logs`|`completed`)\n * @returns {Promise<object>} Resolves with the data for the created training\n */\nasync function createTraining(model_owner, model_name, version_id, options) {\n  const { ...data } = options;\n\n  if (data.webhook) {\n    try {\n      // eslint-disable-next-line no-new\n      new URL(data.webhook);\n    } catch (err) {\n      throw new Error(\"Invalid webhook URL\");\n    }\n  }\n\n  const response = await this.request(\n    `/models/${model_owner}/${model_name}/versions/${version_id}/trainings`,\n    {\n      method: \"POST\",\n      data,\n    }\n  );\n\n  return response.json();\n}\n\n/**\n * Fetch a training by ID\n *\n * @param {string} training_id - Required. The training ID\n * @returns {Promise<object>} Resolves with the data for the training\n */\nasync function getTraining(training_id) {\n  const response = await this.request(`/trainings/${training_id}`, {\n    method: \"GET\",\n  });\n\n  return response.json();\n}\n\n/**\n * Cancel a training by ID\n *\n * @param {string} training_id - Required. The training ID\n * @returns {Promise<object>} Resolves with the data for the training\n */\nasync function cancelTraining(training_id) {\n  const response = await this.request(`/trainings/${training_id}/cancel`, {\n    method: \"POST\",\n  });\n\n  return response.json();\n}\n\n/**\n * List all trainings\n *\n * @returns {Promise<object>} - Resolves with a page of trainings\n */\nasync function listTrainings() {\n  const response = await this.request(\"/trainings\", {\n    method: \"GET\",\n  });\n\n  return response.json();\n}\n\nmodule.exports = {\n  create: createTraining,\n  get: getTraining,\n  cancel: cancelTraining,\n  list: listTrainings,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/replicate/lib/trainings.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/replicate/lib/util.js":
/*!********************************************!*\
  !*** ./node_modules/replicate/lib/util.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const ApiError = __webpack_require__(/*! ./error */ \"(rsc)/./node_modules/replicate/lib/error.js\");\nconst { create: createFile } = __webpack_require__(/*! ./files */ \"(rsc)/./node_modules/replicate/lib/files.js\");\n\n/**\n * @see {@link validateWebhook}\n * @overload\n * @param {object} requestData - The request data\n * @param {string} requestData.id - The webhook ID header from the incoming request.\n * @param {string} requestData.timestamp - The webhook timestamp header from the incoming request.\n * @param {string} requestData.body - The raw body of the incoming webhook request.\n * @param {string} requestData.secret - The webhook secret, obtained from `replicate.webhooks.defaul.secret` method.\n * @param {string} requestData.signature - The webhook signature header from the incoming request, comprising one or more space-delimited signatures.\n */\n\n/**\n * @see {@link validateWebhook}\n * @overload\n * @param {object} requestData - The request object\n * @param {object} requestData.headers - The request headers\n * @param {string} requestData.headers[\"webhook-id\"] - The webhook ID header from the incoming request\n * @param {string} requestData.headers[\"webhook-timestamp\"] - The webhook timestamp header from the incoming request\n * @param {string} requestData.headers[\"webhook-signature\"] - The webhook signature header from the incoming request, comprising one or more space-delimited signatures\n * @param {string} requestData.body - The raw body of the incoming webhook request\n * @param {string} secret - The webhook secret, obtained from `replicate.webhooks.defaul.secret` method\n */\n\n/**\n * Validate a webhook signature\n *\n * @returns {Promise<boolean>} - True if the signature is valid\n * @throws {Error} - If the request is missing required headers, body, or secret\n */\nasync function validateWebhook(requestData, secret) {\n  let { id, timestamp, body, signature } = requestData;\n  const signingSecret = secret || requestData.secret;\n\n  if (requestData && requestData.headers && requestData.body) {\n    id = requestData.headers.get(\"webhook-id\");\n    timestamp = requestData.headers.get(\"webhook-timestamp\");\n    signature = requestData.headers.get(\"webhook-signature\");\n    body = requestData.body;\n  }\n\n  if (body instanceof ReadableStream || body.readable) {\n    try {\n      body = await new Response(body).text();\n    } catch (err) {\n      throw new Error(`Error reading body: ${err.message}`);\n    }\n  } else if (isTypedArray(body)) {\n    body = await new Blob([body]).text();\n  } else if (typeof body !== \"string\") {\n    throw new Error(\"Invalid body type\");\n  }\n\n  if (!id || !timestamp || !signature) {\n    throw new Error(\"Missing required webhook headers\");\n  }\n\n  if (!body) {\n    throw new Error(\"Missing required body\");\n  }\n\n  if (!signingSecret) {\n    throw new Error(\"Missing required secret\");\n  }\n\n  const signedContent = `${id}.${timestamp}.${body}`;\n\n  const computedSignature = await createHMACSHA256(signingSecret.split(\"_\").pop(), signedContent);\n\n  const expectedSignatures = signature.split(\" \").map((sig) => sig.split(\",\")[1]);\n\n  return expectedSignatures.some((expectedSignature) => expectedSignature === computedSignature);\n}\n\n/**\n * @param {string} secret - base64 encoded string\n * @param {string} data - text body of request\n */\nasync function createHMACSHA256(secret, data) {\n  const encoder = new TextEncoder();\n  let crypto = globalThis.crypto;\n\n  // In Node 18 the `crypto` global is behind a --no-experimental-global-webcrypto flag\n  if (typeof crypto === \"undefined\" && \"function\" === \"function\") {\n    // NOTE: Webpack (primarily as it's used by Next.js) and perhaps some\n    // other bundlers do not currently support the `node` protocol and will\n    // error if it's found in the source. Other platforms like CloudFlare\n    // will only support requires when using the node protocol.\n    //\n    // As this line is purely to support Node 18.x we make an indirect request\n    // to the require function which fools Webpack...\n    //\n    // We may be able to remove this in future as it looks like Webpack is getting\n    // support for requiring using the `node:` protocol.\n    // See: https://github.com/webpack/webpack/issues/18277\n    crypto = __webpack_require__(\"(rsc)/./node_modules/replicate/lib sync recursive\").call(null, \"node:crypto\").webcrypto;\n  }\n\n  const key = await crypto.subtle.importKey(\"raw\", base64ToBytes(secret), { name: \"HMAC\", hash: \"SHA-256\" }, false, [\n    \"sign\",\n  ]);\n\n  const signature = await crypto.subtle.sign(\"HMAC\", key, encoder.encode(data));\n  return bytesToBase64(signature);\n}\n\n/**\n * Convert a base64 encoded string into bytes.\n *\n * @param {string} the base64 encoded string\n * @return {Uint8Array}\n *\n * Two functions for encoding/decoding base64 strings using web standards. Not\n * intended to be used to encode/decode arbitrary string data.\n * See: https://developer.mozilla.org/en-US/docs/Glossary/Base64#javascript_support\n * See: https://stackoverflow.com/a/31621532\n *\n * Performance might take a hit because of the conversion to string and then to binary,\n * if this is the case we might want to look at an alternative solution.\n * See: https://jsben.ch/wnaZC\n */\nfunction base64ToBytes(base64) {\n  return Uint8Array.from(atob(base64), (m) => m.codePointAt(0));\n}\n\n/**\n * Convert a base64 encoded string into bytes.\n *\n * See {@link base64ToBytes} for caveats.\n *\n * @param {Uint8Array | ArrayBuffer} the base64 encoded string\n * @return {string}\n */\nfunction bytesToBase64(bytes) {\n  return btoa(String.fromCharCode.apply(null, new Uint8Array(bytes)));\n}\n\n/**\n * Automatically retry a request if it fails with an appropriate status code.\n *\n * A GET request is retried if it fails with a 429 or 5xx status code.\n * A non-GET request is retried only if it fails with a 429 status code.\n *\n * If the response sets a Retry-After header,\n * the request is retried after the number of seconds specified in the header.\n * Otherwise, the request is retried after the specified interval,\n * with exponential backoff and jitter.\n *\n * @param {Function} request - A function that returns a Promise that resolves with a Response object\n * @param {object} options\n * @param {Function} [options.shouldRetry] - A function that returns true if the request should be retried\n * @param {number} [options.maxRetries] - Maximum number of retries. Defaults to 5\n * @param {number} [options.interval] - Interval between retries in milliseconds. Defaults to 500\n * @returns {Promise<Response>} - Resolves with the response object\n * @throws {ApiError} If the request failed\n */\nasync function withAutomaticRetries(request, options = {}) {\n  const shouldRetry = options.shouldRetry || (() => false);\n  const maxRetries = options.maxRetries || 5;\n  const interval = options.interval || 500;\n  const jitter = options.jitter || 100;\n\n  // eslint-disable-next-line no-promise-executor-return\n  const sleep = (ms) => new Promise((resolve) => setTimeout(resolve, ms));\n\n  let attempts = 0;\n  do {\n    let delay = interval * 2 ** attempts + Math.random() * jitter;\n\n    /* eslint-disable no-await-in-loop */\n    try {\n      const response = await request();\n      if (response.ok || !shouldRetry(response)) {\n        return response;\n      }\n    } catch (error) {\n      if (error instanceof ApiError) {\n        const retryAfter = error.response.headers.get(\"Retry-After\");\n        if (retryAfter) {\n          if (!Number.isInteger(retryAfter)) {\n            // Retry-After is a date\n            const date = new Date(retryAfter);\n            if (!Number.isNaN(date.getTime())) {\n              delay = date.getTime() - new Date().getTime();\n            }\n          } else {\n            // Retry-After is a number of seconds\n            delay = retryAfter * 1000;\n          }\n        }\n      }\n    }\n\n    if (Number.isInteger(maxRetries) && maxRetries > 0) {\n      if (Number.isInteger(delay) && delay > 0) {\n        await sleep(interval * 2 ** (options.maxRetries - maxRetries));\n      }\n      attempts += 1;\n    }\n  } while (attempts < maxRetries);\n\n  return request();\n}\n\n/**\n * Walks the inputs and, for any File or Blob, tries to upload it to Replicate\n * and replaces the input with the URL of the uploaded file.\n *\n * @param {Replicate} client - The client used to upload the file\n * @param {object} inputs - The inputs to transform\n * @param {\"default\" | \"upload\" | \"data-uri\"} strategy - Whether to upload files to Replicate, encode as dataURIs or try both.\n * @returns {object} - The transformed inputs\n * @throws {ApiError} If the request to upload the file fails\n */\nasync function transformFileInputs(client, inputs, strategy) {\n  switch (strategy) {\n    case \"data-uri\":\n      return await transformFileInputsToBase64EncodedDataURIs(client, inputs);\n    case \"upload\":\n      return await transformFileInputsToReplicateFileURLs(client, inputs);\n    case \"default\":\n      try {\n        return await transformFileInputsToReplicateFileURLs(client, inputs);\n      } catch (error) {\n        if (error instanceof ApiError && error.response.status >= 400 && error.response.status < 500) {\n          throw error;\n        }\n        return await transformFileInputsToBase64EncodedDataURIs(inputs);\n      }\n    default:\n      throw new Error(`Unexpected file upload strategy: ${strategy}`);\n  }\n}\n\n/**\n * Walks the inputs and, for any File or Blob, tries to upload it to Replicate\n * and replaces the input with the URL of the uploaded file.\n *\n * @param {Replicate} client - The client used to upload the file\n * @param {object} inputs - The inputs to transform\n * @returns {object} - The transformed inputs\n * @throws {ApiError} If the request to upload the file fails\n */\nasync function transformFileInputsToReplicateFileURLs(client, inputs) {\n  return await transform(inputs, async (value) => {\n    if (value instanceof Blob || value instanceof Buffer) {\n      const file = await createFile.call(client, value);\n      return file.urls.get;\n    }\n\n    return value;\n  });\n}\n\nconst MAX_DATA_URI_SIZE = 10_000_000;\n\n/**\n * Walks the inputs and transforms any binary data found into a\n * base64-encoded data URI.\n *\n * @param {object} inputs - The inputs to transform\n * @returns {object} - The transformed inputs\n * @throws {Error} If the size of inputs exceeds a given threshould set by MAX_DATA_URI_SIZE\n */\nasync function transformFileInputsToBase64EncodedDataURIs(inputs) {\n  let totalBytes = 0;\n  return await transform(inputs, async (value) => {\n    let buffer;\n    let mime;\n\n    if (value instanceof Blob) {\n      // Currently we use a NodeJS only API for base64 encoding, as\n      // we move to support the browser we could support either using\n      // btoa (which does string encoding), the FileReader API or\n      // a JavaScript implenentation like base64-js.\n      // See: https://developer.mozilla.org/en-US/docs/Glossary/Base64\n      // See: https://github.com/beatgammit/base64-js\n      buffer = await value.arrayBuffer();\n      mime = value.type;\n    } else if (isTypedArray(value)) {\n      buffer = value;\n    } else {\n      return value;\n    }\n\n    totalBytes += buffer.byteLength;\n    if (totalBytes > MAX_DATA_URI_SIZE) {\n      throw new Error(\n        `Combined filesize of prediction ${totalBytes} bytes exceeds 10mb limit for inline encoding, please provide URLs instead`,\n      );\n    }\n\n    const data = bytesToBase64(buffer);\n    mime = mime ?? \"application/octet-stream\";\n\n    return `data:${mime};base64,${data}`;\n  });\n}\n\n// Walk a JavaScript object and transform the leaf values.\nasync function transform(value, mapper) {\n  if (Array.isArray(value)) {\n    const copy = [];\n    for (const val of value) {\n      const transformed = await transform(val, mapper);\n      copy.push(transformed);\n    }\n    return copy;\n  }\n\n  if (isPlainObject(value)) {\n    const copy = {};\n    for (const key of Object.keys(value)) {\n      copy[key] = await transform(value[key], mapper);\n    }\n    return copy;\n  }\n\n  return await mapper(value);\n}\n\nfunction isTypedArray(arr) {\n  return (\n    arr instanceof Int8Array ||\n    arr instanceof Int16Array ||\n    arr instanceof Int32Array ||\n    arr instanceof Uint8Array ||\n    arr instanceof Uint8ClampedArray ||\n    arr instanceof Uint16Array ||\n    arr instanceof Uint32Array ||\n    arr instanceof Float32Array ||\n    arr instanceof Float64Array\n  );\n}\n\n// Test for a plain JS object.\n// Source: lodash.isPlainObject\nfunction isPlainObject(value) {\n  const isObjectLike = typeof value === \"object\" && value !== null;\n  if (!isObjectLike || String(value) !== \"[object Object]\") {\n    return false;\n  }\n  const proto = Object.getPrototypeOf(value);\n  if (proto === null) {\n    return true;\n  }\n  const Ctor = Object.prototype.hasOwnProperty.call(proto, \"constructor\") && proto.constructor;\n  return (\n    typeof Ctor === \"function\" &&\n    Ctor instanceof Ctor &&\n    Function.prototype.toString.call(Ctor) === Function.prototype.toString.call(Object)\n  );\n}\n\n/**\n * Parse progress from prediction logs.\n *\n * This function supports log statements in the following format,\n * which are generated by https://github.com/tqdm/tqdm and similar libraries:\n *\n * ```\n * 76%|████████████████████████████         | 7568/10000 [00:33<00:10, 229.00it/s]\n * ```\n *\n * @example\n * const progress = parseProgressFromLogs(\"76%|████████████████████████████         | 7568/10000 [00:33<00:10, 229.00it/s]\");\n * console.log(progress);\n * // {\n * //   percentage: 0.76,\n * //   current: 7568,\n * //   total: 10000,\n * // }\n *\n * @param {object|string} input - A prediction object or string.\n * @returns {(object|null)} - An object with the percentage, current, and total, or null if no progress can be parsed.\n */\nfunction parseProgressFromLogs(input) {\n  const logs = typeof input === \"object\" && input.logs ? input.logs : input;\n  if (!logs || typeof logs !== \"string\") {\n    return null;\n  }\n\n  const pattern = /^\\s*(\\d+)%\\s*\\|.+?\\|\\s*(\\d+)\\/(\\d+)/;\n  const lines = logs.split(\"\\n\").reverse();\n\n  for (const line of lines) {\n    const matches = line.match(pattern);\n\n    if (matches && matches.length === 4) {\n      return {\n        percentage: parseInt(matches[1], 10) / 100,\n        current: parseInt(matches[2], 10),\n        total: parseInt(matches[3], 10),\n      };\n    }\n  }\n\n  return null;\n}\n\n/**\n * Helper to make any `ReadableStream` iterable, this is supported\n * by most server runtimes but browsers still haven't implemented\n * it yet.\n * See: https://developer.mozilla.org/en-US/docs/Web/API/ReadableStream#browser_compatibility\n *\n * @template T\n * @param {ReadableStream<T>} stream an instance of a `ReadableStream`\n * @yields {T} a chunk/event from the stream\n */\nasync function* streamAsyncIterator(stream) {\n  const reader = stream.getReader();\n  try {\n    while (true) {\n      const { done, value } = await reader.read();\n      if (done) return;\n      yield value;\n    }\n  } finally {\n    reader.releaseLock();\n  }\n}\n\nmodule.exports = {\n  transformFileInputs,\n  validateWebhook,\n  withAutomaticRetries,\n  parseProgressFromLogs,\n  streamAsyncIterator,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/replicate/lib/util.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/replicate/lib/webhooks.js":
/*!************************************************!*\
  !*** ./node_modules/replicate/lib/webhooks.js ***!
  \************************************************/
/***/ ((module) => {

eval("/**\n * Get the default webhook signing secret\n *\n * @returns {Promise<object>} Resolves with the signing secret for the default webhook\n */\nasync function getDefaultWebhookSecret() {\n  const response = await this.request(\"/webhooks/default/secret\", {\n    method: \"GET\",\n  });\n\n  return response.json();\n}\n\nmodule.exports = {\n  default: {\n    secret: {\n      get: getDefaultWebhookSecret,\n    },\n  },\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcmVwbGljYXRlL2xpYi93ZWJob29rcy5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLGlCQUFpQjtBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCxHQUFHO0FBQ0giLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90aGUtY2FudmFzLy4vbm9kZV9tb2R1bGVzL3JlcGxpY2F0ZS9saWIvd2ViaG9va3MuanM/ZDcxOCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEdldCB0aGUgZGVmYXVsdCB3ZWJob29rIHNpZ25pbmcgc2VjcmV0XG4gKlxuICogQHJldHVybnMge1Byb21pc2U8b2JqZWN0Pn0gUmVzb2x2ZXMgd2l0aCB0aGUgc2lnbmluZyBzZWNyZXQgZm9yIHRoZSBkZWZhdWx0IHdlYmhvb2tcbiAqL1xuYXN5bmMgZnVuY3Rpb24gZ2V0RGVmYXVsdFdlYmhvb2tTZWNyZXQoKSB7XG4gIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5yZXF1ZXN0KFwiL3dlYmhvb2tzL2RlZmF1bHQvc2VjcmV0XCIsIHtcbiAgICBtZXRob2Q6IFwiR0VUXCIsXG4gIH0pO1xuXG4gIHJldHVybiByZXNwb25zZS5qc29uKCk7XG59XG5cbm1vZHVsZS5leHBvcnRzID0ge1xuICBkZWZhdWx0OiB7XG4gICAgc2VjcmV0OiB7XG4gICAgICBnZXQ6IGdldERlZmF1bHRXZWJob29rU2VjcmV0LFxuICAgIH0sXG4gIH0sXG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/replicate/lib/webhooks.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/replicate/vendor/eventsource-parser/stream.js":
/*!********************************************************************!*\
  !*** ./node_modules/replicate/vendor/eventsource-parser/stream.js ***!
  \********************************************************************/
/***/ ((module) => {

eval("// Source: https://github.com/rexxars/eventsource-parser/tree/v1.1.2\n//\n// MIT License\n//\n// Copyright (c) 2024 Espen Hovlandsdal <<EMAIL>>\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in all\n// copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n// SOFTWARE.\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if ((from && typeof from === \"object\") || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, {\n          get: () => from[key],\n          enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable,\n        });\n  }\n  return to;\n};\nvar __toCommonJS = (mod) =>\n  __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\n\n// /input.ts\nvar input_exports = {};\n__export(input_exports, {\n  EventSourceParserStream: () => EventSourceParserStream,\n});\nmodule.exports = __toCommonJS(input_exports);\n\n// http-url:https://unpkg.com/eventsource-parser@1.1.2/dist/index.js\nfunction createParser(onParse) {\n  let isFirstChunk;\n  let buffer;\n  let startingPosition;\n  let startingFieldLength;\n  let eventId;\n  let eventName;\n  let data;\n  reset();\n  return {\n    feed,\n    reset,\n  };\n  function reset() {\n    isFirstChunk = true;\n    buffer = \"\";\n    startingPosition = 0;\n    startingFieldLength = -1;\n    eventId = void 0;\n    eventName = void 0;\n    data = \"\";\n  }\n  function feed(chunk) {\n    buffer = buffer ? buffer + chunk : chunk;\n    if (isFirstChunk && hasBom(buffer)) {\n      buffer = buffer.slice(BOM.length);\n    }\n    isFirstChunk = false;\n    const length = buffer.length;\n    let position = 0;\n    let discardTrailingNewline = false;\n    while (position < length) {\n      if (discardTrailingNewline) {\n        if (buffer[position] === \"\\n\") {\n          ++position;\n        }\n        discardTrailingNewline = false;\n      }\n      let lineLength = -1;\n      let fieldLength = startingFieldLength;\n      let character;\n      for (\n        let index = startingPosition;\n        lineLength < 0 && index < length;\n        ++index\n      ) {\n        character = buffer[index];\n        if (character === \":\" && fieldLength < 0) {\n          fieldLength = index - position;\n        } else if (character === \"\\r\") {\n          discardTrailingNewline = true;\n          lineLength = index - position;\n        } else if (character === \"\\n\") {\n          lineLength = index - position;\n        }\n      }\n      if (lineLength < 0) {\n        startingPosition = length - position;\n        startingFieldLength = fieldLength;\n        break;\n      } else {\n        startingPosition = 0;\n        startingFieldLength = -1;\n      }\n      parseEventStreamLine(buffer, position, fieldLength, lineLength);\n      position += lineLength + 1;\n    }\n    if (position === length) {\n      buffer = \"\";\n    } else if (position > 0) {\n      buffer = buffer.slice(position);\n    }\n  }\n  function parseEventStreamLine(lineBuffer, index, fieldLength, lineLength) {\n    if (lineLength === 0) {\n      if (data.length > 0) {\n        onParse({\n          type: \"event\",\n          id: eventId,\n          event: eventName || void 0,\n          data: data.slice(0, -1),\n          // remove trailing newline\n        });\n        data = \"\";\n        eventId = void 0;\n      }\n      eventName = void 0;\n      return;\n    }\n    const noValue = fieldLength < 0;\n    const field = lineBuffer.slice(\n      index,\n      index + (noValue ? lineLength : fieldLength)\n    );\n    let step = 0;\n    if (noValue) {\n      step = lineLength;\n    } else if (lineBuffer[index + fieldLength + 1] === \" \") {\n      step = fieldLength + 2;\n    } else {\n      step = fieldLength + 1;\n    }\n    const position = index + step;\n    const valueLength = lineLength - step;\n    const value = lineBuffer.slice(position, position + valueLength).toString();\n    if (field === \"data\") {\n      data += value ? \"\".concat(value, \"\\n\") : \"\\n\";\n    } else if (field === \"event\") {\n      eventName = value;\n    } else if (field === \"id\" && !value.includes(\"\\0\")) {\n      eventId = value;\n    } else if (field === \"retry\") {\n      const retry = parseInt(value, 10);\n      if (!Number.isNaN(retry)) {\n        onParse({\n          type: \"reconnect-interval\",\n          value: retry,\n        });\n      }\n    }\n  }\n}\nvar BOM = [239, 187, 191];\nfunction hasBom(buffer) {\n  return BOM.every((charCode, index) => buffer.charCodeAt(index) === charCode);\n}\n\n// http-url:https://unpkg.com/eventsource-parser@1.1.2/dist/stream.js\nvar EventSourceParserStream = class extends TransformStream {\n  constructor() {\n    let parser;\n    super({\n      start(controller) {\n        parser = createParser((event) => {\n          if (event.type === \"event\") {\n            controller.enqueue(event);\n          }\n        });\n      },\n      transform(chunk) {\n        parser.feed(chunk);\n      },\n    });\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/replicate/vendor/eventsource-parser/stream.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/replicate/vendor/streams-text-encoding/text-decoder-stream.js":
/*!************************************************************************************!*\
  !*** ./node_modules/replicate/vendor/streams-text-encoding/text-decoder-stream.js ***!
  \************************************************************************************/
/***/ ((module) => {

eval("// Adapted from https://github.com/stardazed/sd-streams\n//\n// MIT License\n//\n// Copyright (c) 2018-Present @zenmumbler\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in all\n// copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n// SOFTWARE.\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\n\n// /input.ts\nvar input_exports = {};\n__export(input_exports, {\n  TextDecoderStream: () => TextDecoderStream\n});\nmodule.exports = __toCommonJS(input_exports);\n\n// http-url:https://unpkg.com/@stardazed/streams-text-encoding@1.0.2/dist/sd-streams-text-encoding.esm.js\nvar decDecoder = Symbol(\"decDecoder\");\nvar decTransform = Symbol(\"decTransform\");\nvar TextDecodeTransformer = class {\n  constructor(decoder) {\n    this.decoder_ = decoder;\n  }\n  transform(chunk, controller) {\n    if (!(chunk instanceof ArrayBuffer || ArrayBuffer.isView(chunk))) {\n      throw new TypeError(\"Input data must be a BufferSource\");\n    }\n    const text = this.decoder_.decode(chunk, { stream: true });\n    if (text.length !== 0) {\n      controller.enqueue(text);\n    }\n  }\n  flush(controller) {\n    const text = this.decoder_.decode();\n    if (text.length !== 0) {\n      controller.enqueue(text);\n    }\n  }\n};\nvar TextDecoderStream = class {\n  constructor(label, options) {\n    const decoder = new TextDecoder(label || \"utf-8\", options || {});\n    this[decDecoder] = decoder;\n    this[decTransform] = new TransformStream(new TextDecodeTransformer(decoder));\n  }\n  get encoding() {\n    return this[decDecoder].encoding;\n  }\n  get fatal() {\n    return this[decDecoder].fatal;\n  }\n  get ignoreBOM() {\n    return this[decDecoder].ignoreBOM;\n  }\n  get readable() {\n    return this[decTransform].readable;\n  }\n  get writable() {\n    return this[decTransform].writable;\n  }\n};\nvar encEncoder = Symbol(\"encEncoder\");\nvar encTransform = Symbol(\"encTransform\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/replicate/vendor/streams-text-encoding/text-decoder-stream.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/replicate/package.json":
/*!*********************************************!*\
  !*** ./node_modules/replicate/package.json ***!
  \*********************************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"name":"replicate","version":"0.31.1","description":"JavaScript client for Replicate","repository":"github:replicate/replicate-javascript","homepage":"https://github.com/replicate/replicate-javascript#readme","bugs":"https://github.com/replicate/replicate-javascript/issues","license":"Apache-2.0","main":"index.js","type":"commonjs","types":"index.d.ts","files":["CONTRIBUTING.md","LICENSE","README.md","index.d.ts","index.js","lib/**/*.js","vendor/**/*","package.json"],"engines":{"node":">=18.0.0","npm":">=7.19.0","git":">=2.11.0","yarn":">=1.7.0"},"scripts":{"check":"tsc","format":"biome format . --write","lint-biome":"biome lint .","lint-publint":"publint","lint":"npm run lint-biome && npm run lint-publint","test":"jest"},"optionalDependencies":{"readable-stream":">=4.0.0"},"devDependencies":{"@biomejs/biome":"^1.4.1","@types/jest":"^29.5.3","@typescript-eslint/eslint-plugin":"^5.56.0","cross-fetch":"^3.1.5","jest":"^29.7.0","nock":"^14.0.0-beta.6","publint":"^0.2.7","ts-jest":"^29.1.0","typescript":"^5.0.2"}}');

/***/ })

};
;