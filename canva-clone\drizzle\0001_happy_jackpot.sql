CREATE TABLE IF NOT EXISTS "uploaded_image" (
	"id" text PRIMARY KEY NOT NULL,
	"userId" text NOT NULL,
	"filename" text NOT NULL,
	"originalName" text NOT NULL,
	"url" text NOT NULL,
	"size" integer NOT NULL,
	"mimeType" text NOT NULL,
	"createdAt" timestamp NOT NULL
);
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "uploaded_image" ADD CONSTRAINT "uploaded_image_userId_user_id_fk" FOREIGN KEY ("userId") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
