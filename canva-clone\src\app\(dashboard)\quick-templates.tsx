"use client";

import { useRouter } from "next/navigation";
import { useState } from "react";
import { Instagram, Monitor, Smartphone, Loader2 } from "lucide-react";

import { useCreateProject } from "@/features/projects/api/use-create-project";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface QuickTemplate {
  id: string;
  name: string;
  description: string;
  width: number;
  height: number;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
}

const quickTemplates: QuickTemplate[] = [
  {
    id: "instagram-post",
    name: "Instagram Post",
    description: "1080 x 1080 px",
    width: 1080,
    height: 1080,
    icon: Instagram,
    color: "bg-gradient-to-br from-purple-500 to-pink-500",
  },
  {
    id: "instagram-story",
    name: "Instagram Story",
    description: "1080 x 1920 px",
    width: 1080,
    height: 1920,
    icon: Smartphone,
    color: "bg-gradient-to-br from-blue-500 to-purple-500",
  },
  {
    id: "presentation",
    name: "Presentation",
    description: "1920 x 1080 px",
    width: 1920,
    height: 1080,
    icon: Monitor,
    color: "bg-gradient-to-br from-green-500 to-blue-500",
  },
];

export const QuickTemplates = () => {
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const mutation = useCreateProject();

  const handleTemplateSelect = (template: QuickTemplate) => {
    setSelectedTemplate(template.id);
    setLoading(true);
    
    mutation.mutate(
      {
        name: `${template.name} project`,
        json: "",
        width: template.width,
        height: template.height,
      },
      {
        onSuccess: ({ data }) => {
          router.push(`/editor/${data.id}`);
        },
        onError: () => {
          setLoading(false);
          setSelectedTemplate(null);
        },
      }
    );
  };

  return (
    <div className="space-y-4">
      <h3 className="font-semibold text-lg">Quick Start</h3>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {quickTemplates.map((template) => (
          <QuickTemplateCard
            key={template.id}
            template={template}
            isSelected={selectedTemplate === template.id}
            isLoading={loading && selectedTemplate === template.id}
            onClick={() => handleTemplateSelect(template)}
            disabled={loading}
          />
        ))}
      </div>
    </div>
  );
};

interface QuickTemplateCardProps {
  template: QuickTemplate;
  isSelected: boolean;
  isLoading: boolean;
  onClick: () => void;
  disabled: boolean;
}

const QuickTemplateCard = ({
  template,
  isSelected,
  isLoading,
  onClick,
  disabled,
}: QuickTemplateCardProps) => {
  const Icon = template.icon;

  return (
    <Button
      variant="outline"
      onClick={onClick}
      disabled={disabled}
      className={cn(
        "relative h-24 p-4 flex items-center space-x-4 transition-all duration-200 hover:shadow-md",
        isSelected && "ring-2 ring-blue-500 bg-blue-50",
        disabled && "opacity-50 cursor-not-allowed"
      )}
    >
      {isLoading && (
        <div className="absolute inset-0 bg-white/80 flex items-center justify-center rounded-md">
          <Loader2 className="size-6 animate-spin text-blue-500" />
        </div>
      )}
      
      <div className={cn(
        "rounded-lg flex items-center justify-center size-12 text-white",
        template.color
      )}>
        <Icon className="size-6" />
      </div>
      
      <div className="text-left space-y-1 flex-1">
        <h4 className="font-medium text-gray-900 text-sm">
          {template.name}
        </h4>
        <p className="text-gray-500 text-xs">
          {template.description}
        </p>
      </div>
    </Button>
  );
};
