"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@neondatabase";
exports.ids = ["vendor-chunks/@neondatabase"];
exports.modules = {

/***/ "(rsc)/./node_modules/@neondatabase/serverless/index.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/@neondatabase/serverless/index.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Client: () => (/* binding */ xn),\n/* harmony export */   ClientBase: () => (/* binding */ export_ClientBase),\n/* harmony export */   Connection: () => (/* binding */ export_Connection),\n/* harmony export */   DatabaseError: () => (/* binding */ export_DatabaseError),\n/* harmony export */   NeonDbError: () => (/* binding */ Ce),\n/* harmony export */   Pool: () => (/* binding */ Ys),\n/* harmony export */   Query: () => (/* binding */ export_Query),\n/* harmony export */   defaults: () => (/* binding */ export_defaults),\n/* harmony export */   neon: () => (/* binding */ zs),\n/* harmony export */   neonConfig: () => (/* binding */ Ae),\n/* harmony export */   types: () => (/* binding */ export_types)\n/* harmony export */ });\nvar eo=Object.create;var Ie=Object.defineProperty;var to=Object.getOwnPropertyDescriptor;var ro=Object.getOwnPropertyNames;var no=Object.getPrototypeOf,io=Object.prototype.hasOwnProperty;var so=(r,e,t)=>e in r?Ie(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):\nr[e]=t;var a=(r,e)=>Ie(r,\"name\",{value:e,configurable:!0});var z=(r,e)=>()=>(r&&(e=r(r=0)),e);var T=(r,e)=>()=>(e||r((e={exports:{}}).exports,e),e.exports),ie=(r,e)=>{for(var t in e)\nIe(r,t,{get:e[t],enumerable:!0})},An=(r,e,t,n)=>{if(e&&typeof e==\"object\"||typeof e==\n\"function\")for(let i of ro(e))!io.call(r,i)&&i!==t&&Ie(r,i,{get:()=>e[i],enumerable:!(n=\nto(e,i))||n.enumerable});return r};var Qe=(r,e,t)=>(t=r!=null?eo(no(r)):{},An(e||!r||!r.__esModule?Ie(t,\"default\",{\nvalue:r,enumerable:!0}):t,r)),N=r=>An(Ie({},\"__esModule\",{value:!0}),r);var _=(r,e,t)=>so(r,typeof e!=\"symbol\"?e+\"\":e,t);var Tn=T(nt=>{\"use strict\";p();nt.byteLength=ao;nt.toByteArray=co;nt.fromByteArray=\nfo;var ae=[],te=[],oo=typeof Uint8Array<\"u\"?Uint8Array:Array,It=\"ABCDEFGHIJKLMNO\\\nPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\";for(Ee=0,Cn=It.length;Ee<Cn;++Ee)\nae[Ee]=It[Ee],te[It.charCodeAt(Ee)]=Ee;var Ee,Cn;te[45]=62;te[95]=63;function In(r){\nvar e=r.length;if(e%4>0)throw new Error(\"Invalid string. Length must be a multip\\\nle of 4\");var t=r.indexOf(\"=\");t===-1&&(t=e);var n=t===e?0:4-t%4;return[t,n]}a(In,\n\"getLens\");function ao(r){var e=In(r),t=e[0],n=e[1];return(t+n)*3/4-n}a(ao,\"byte\\\nLength\");function uo(r,e,t){return(e+t)*3/4-t}a(uo,\"_byteLength\");function co(r){\nvar e,t=In(r),n=t[0],i=t[1],s=new oo(uo(r,n,i)),o=0,u=i>0?n-4:n,c;for(c=0;c<u;c+=\n4)e=te[r.charCodeAt(c)]<<18|te[r.charCodeAt(c+1)]<<12|te[r.charCodeAt(c+2)]<<6|te[r.\ncharCodeAt(c+3)],s[o++]=e>>16&255,s[o++]=e>>8&255,s[o++]=e&255;return i===2&&(e=\nte[r.charCodeAt(c)]<<2|te[r.charCodeAt(c+1)]>>4,s[o++]=e&255),i===1&&(e=te[r.charCodeAt(\nc)]<<10|te[r.charCodeAt(c+1)]<<4|te[r.charCodeAt(c+2)]>>2,s[o++]=e>>8&255,s[o++]=\ne&255),s}a(co,\"toByteArray\");function ho(r){return ae[r>>18&63]+ae[r>>12&63]+ae[r>>\n6&63]+ae[r&63]}a(ho,\"tripletToBase64\");function lo(r,e,t){for(var n,i=[],s=e;s<t;s+=\n3)n=(r[s]<<16&16711680)+(r[s+1]<<8&65280)+(r[s+2]&255),i.push(ho(n));return i.join(\n\"\")}a(lo,\"encodeChunk\");function fo(r){for(var e,t=r.length,n=t%3,i=[],s=16383,o=0,\nu=t-n;o<u;o+=s)i.push(lo(r,o,o+s>u?u:o+s));return n===1?(e=r[t-1],i.push(ae[e>>2]+\nae[e<<4&63]+\"==\")):n===2&&(e=(r[t-2]<<8)+r[t-1],i.push(ae[e>>10]+ae[e>>4&63]+ae[e<<\n2&63]+\"=\")),i.join(\"\")}a(fo,\"fromByteArray\")});var Pn=T(Tt=>{p();Tt.read=function(r,e,t,n,i){var s,o,u=i*8-n-1,c=(1<<u)-1,h=c>>\n1,l=-7,y=t?i-1:0,x=t?-1:1,C=r[e+y];for(y+=x,s=C&(1<<-l)-1,C>>=-l,l+=u;l>0;s=s*256+\nr[e+y],y+=x,l-=8);for(o=s&(1<<-l)-1,s>>=-l,l+=n;l>0;o=o*256+r[e+y],y+=x,l-=8);if(s===\n0)s=1-h;else{if(s===c)return o?NaN:(C?-1:1)*(1/0);o=o+Math.pow(2,n),s=s-h}return(C?\n-1:1)*o*Math.pow(2,s-n)};Tt.write=function(r,e,t,n,i,s){var o,u,c,h=s*8-i-1,l=(1<<\nh)-1,y=l>>1,x=i===23?Math.pow(2,-24)-Math.pow(2,-77):0,C=n?0:s-1,B=n?1:-1,W=e<0||\ne===0&&1/e<0?1:0;for(e=Math.abs(e),isNaN(e)||e===1/0?(u=isNaN(e)?1:0,o=l):(o=Math.\nfloor(Math.log(e)/Math.LN2),e*(c=Math.pow(2,-o))<1&&(o--,c*=2),o+y>=1?e+=x/c:e+=\nx*Math.pow(2,1-y),e*c>=2&&(o++,c/=2),o+y>=l?(u=0,o=l):o+y>=1?(u=(e*c-1)*Math.pow(\n2,i),o=o+y):(u=e*Math.pow(2,y-1)*Math.pow(2,i),o=0));i>=8;r[t+C]=u&255,C+=B,u/=256,\ni-=8);for(o=o<<i|u,h+=i;h>0;r[t+C]=o&255,C+=B,o/=256,h-=8);r[t+C-B]|=W*128}});var $n=T(Le=>{\"use strict\";p();var Pt=Tn(),Pe=Pn(),Bn=typeof Symbol==\"function\"&&\ntypeof Symbol.for==\"function\"?Symbol.for(\"nodejs.util.inspect.custom\"):null;Le.Buffer=\nf;Le.SlowBuffer=bo;Le.INSPECT_MAX_BYTES=50;var it=2147483647;Le.kMaxLength=it;f.\nTYPED_ARRAY_SUPPORT=po();!f.TYPED_ARRAY_SUPPORT&&typeof console<\"u\"&&typeof console.\nerror==\"function\"&&console.error(\"This browser lacks typed array (Uint8Array) su\\\npport which is required by `buffer` v5.x. Use `buffer` v4.x if you require old b\\\nrowser support.\");function po(){try{let r=new Uint8Array(1),e={foo:a(function(){\nreturn 42},\"foo\")};return Object.setPrototypeOf(e,Uint8Array.prototype),Object.setPrototypeOf(\nr,e),r.foo()===42}catch{return!1}}a(po,\"typedArraySupport\");Object.defineProperty(\nf.prototype,\"parent\",{enumerable:!0,get:a(function(){if(f.isBuffer(this))return this.\nbuffer},\"get\")});Object.defineProperty(f.prototype,\"offset\",{enumerable:!0,get:a(\nfunction(){if(f.isBuffer(this))return this.byteOffset},\"get\")});function fe(r){if(r>\nit)throw new RangeError('The value \"'+r+'\" is invalid for option \"size\"');let e=new Uint8Array(\nr);return Object.setPrototypeOf(e,f.prototype),e}a(fe,\"createBuffer\");function f(r,e,t){\nif(typeof r==\"number\"){if(typeof e==\"string\")throw new TypeError('The \"string\" a\\\nrgument must be of type string. Received type number');return Ft(r)}return Mn(r,\ne,t)}a(f,\"Buffer\");f.poolSize=8192;function Mn(r,e,t){if(typeof r==\"string\")return mo(\nr,e);if(ArrayBuffer.isView(r))return go(r);if(r==null)throw new TypeError(\"The f\\\nirst argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-l\\\nike Object. Received type \"+typeof r);if(ue(r,ArrayBuffer)||r&&ue(r.buffer,ArrayBuffer)||\ntypeof SharedArrayBuffer<\"u\"&&(ue(r,SharedArrayBuffer)||r&&ue(r.buffer,SharedArrayBuffer)))\nreturn Lt(r,e,t);if(typeof r==\"number\")throw new TypeError('The \"value\" argument\\\n must not be of type number. Received type number');let n=r.valueOf&&r.valueOf();\nif(n!=null&&n!==r)return f.from(n,e,t);let i=wo(r);if(i)return i;if(typeof Symbol<\n\"u\"&&Symbol.toPrimitive!=null&&typeof r[Symbol.toPrimitive]==\"function\")return f.\nfrom(r[Symbol.toPrimitive](\"string\"),e,t);throw new TypeError(\"The first argumen\\\nt must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. \\\nReceived type \"+typeof r)}a(Mn,\"from\");f.from=function(r,e,t){return Mn(r,e,t)};\nObject.setPrototypeOf(f.prototype,Uint8Array.prototype);Object.setPrototypeOf(f,\nUint8Array);function Dn(r){if(typeof r!=\"number\")throw new TypeError('\"size\" arg\\\nument must be of type number');if(r<0)throw new RangeError('The value \"'+r+'\" is\\\n invalid for option \"size\"')}a(Dn,\"assertSize\");function yo(r,e,t){return Dn(r),\nr<=0?fe(r):e!==void 0?typeof t==\"string\"?fe(r).fill(e,t):fe(r).fill(e):fe(r)}a(yo,\n\"alloc\");f.alloc=function(r,e,t){return yo(r,e,t)};function Ft(r){return Dn(r),fe(\nr<0?0:Mt(r)|0)}a(Ft,\"allocUnsafe\");f.allocUnsafe=function(r){return Ft(r)};f.allocUnsafeSlow=\nfunction(r){return Ft(r)};function mo(r,e){if((typeof e!=\"string\"||e===\"\")&&(e=\"\\\nutf8\"),!f.isEncoding(e))throw new TypeError(\"Unknown encoding: \"+e);let t=kn(r,e)|\n0,n=fe(t),i=n.write(r,e);return i!==t&&(n=n.slice(0,i)),n}a(mo,\"fromString\");function Bt(r){\nlet e=r.length<0?0:Mt(r.length)|0,t=fe(e);for(let n=0;n<e;n+=1)t[n]=r[n]&255;return t}\na(Bt,\"fromArrayLike\");function go(r){if(ue(r,Uint8Array)){let e=new Uint8Array(r);\nreturn Lt(e.buffer,e.byteOffset,e.byteLength)}return Bt(r)}a(go,\"fromArrayView\");\nfunction Lt(r,e,t){if(e<0||r.byteLength<e)throw new RangeError('\"offset\" is outs\\\nide of buffer bounds');if(r.byteLength<e+(t||0))throw new RangeError('\"length\" i\\\ns outside of buffer bounds');let n;return e===void 0&&t===void 0?n=new Uint8Array(\nr):t===void 0?n=new Uint8Array(r,e):n=new Uint8Array(r,e,t),Object.setPrototypeOf(\nn,f.prototype),n}a(Lt,\"fromArrayBuffer\");function wo(r){if(f.isBuffer(r)){let e=Mt(\nr.length)|0,t=fe(e);return t.length===0||r.copy(t,0,0,e),t}if(r.length!==void 0)\nreturn typeof r.length!=\"number\"||kt(r.length)?fe(0):Bt(r);if(r.type===\"Buffer\"&&\nArray.isArray(r.data))return Bt(r.data)}a(wo,\"fromObject\");function Mt(r){if(r>=\nit)throw new RangeError(\"Attempt to allocate Buffer larger than maximum size: 0x\"+\nit.toString(16)+\" bytes\");return r|0}a(Mt,\"checked\");function bo(r){return+r!=r&&\n(r=0),f.alloc(+r)}a(bo,\"SlowBuffer\");f.isBuffer=a(function(e){return e!=null&&e.\n_isBuffer===!0&&e!==f.prototype},\"isBuffer\");f.compare=a(function(e,t){if(ue(e,Uint8Array)&&\n(e=f.from(e,e.offset,e.byteLength)),ue(t,Uint8Array)&&(t=f.from(t,t.offset,t.byteLength)),\n!f.isBuffer(e)||!f.isBuffer(t))throw new TypeError('The \"buf1\", \"buf2\" arguments\\\n must be one of type Buffer or Uint8Array');if(e===t)return 0;let n=e.length,i=t.\nlength;for(let s=0,o=Math.min(n,i);s<o;++s)if(e[s]!==t[s]){n=e[s],i=t[s];break}return n<\ni?-1:i<n?1:0},\"compare\");f.isEncoding=a(function(e){switch(String(e).toLowerCase()){case\"\\\nhex\":case\"utf8\":case\"utf-8\":case\"ascii\":case\"latin1\":case\"binary\":case\"base64\":case\"\\\nucs2\":case\"ucs-2\":case\"utf16le\":case\"utf-16le\":return!0;default:return!1}},\"isEn\\\ncoding\");f.concat=a(function(e,t){if(!Array.isArray(e))throw new TypeError('\"lis\\\nt\" argument must be an Array of Buffers');if(e.length===0)return f.alloc(0);let n;\nif(t===void 0)for(t=0,n=0;n<e.length;++n)t+=e[n].length;let i=f.allocUnsafe(t),s=0;\nfor(n=0;n<e.length;++n){let o=e[n];if(ue(o,Uint8Array))s+o.length>i.length?(f.isBuffer(\no)||(o=f.from(o)),o.copy(i,s)):Uint8Array.prototype.set.call(i,o,s);else if(f.isBuffer(\no))o.copy(i,s);else throw new TypeError('\"list\" argument must be an Array of Buf\\\nfers');s+=o.length}return i},\"concat\");function kn(r,e){if(f.isBuffer(r))return r.\nlength;if(ArrayBuffer.isView(r)||ue(r,ArrayBuffer))return r.byteLength;if(typeof r!=\n\"string\")throw new TypeError('The \"string\" argument must be one of type string, \\\nBuffer, or ArrayBuffer. Received type '+typeof r);let t=r.length,n=arguments.length>\n2&&arguments[2]===!0;if(!n&&t===0)return 0;let i=!1;for(;;)switch(e){case\"ascii\":case\"\\\nlatin1\":case\"binary\":return t;case\"utf8\":case\"utf-8\":return Rt(r).length;case\"uc\\\ns2\":case\"ucs-2\":case\"utf16le\":case\"utf-16le\":return t*2;case\"hex\":return t>>>1;case\"\\\nbase64\":return Gn(r).length;default:if(i)return n?-1:Rt(r).length;e=(\"\"+e).toLowerCase(),\ni=!0}}a(kn,\"byteLength\");f.byteLength=kn;function So(r,e,t){let n=!1;if((e===void 0||\ne<0)&&(e=0),e>this.length||((t===void 0||t>this.length)&&(t=this.length),t<=0)||\n(t>>>=0,e>>>=0,t<=e))return\"\";for(r||(r=\"utf8\");;)switch(r){case\"hex\":return Bo(\nthis,e,t);case\"utf8\":case\"utf-8\":return On(this,e,t);case\"ascii\":return To(this,\ne,t);case\"latin1\":case\"binary\":return Po(this,e,t);case\"base64\":return Co(this,e,\nt);case\"ucs2\":case\"ucs-2\":case\"utf16le\":case\"utf-16le\":return Lo(this,e,t);default:\nif(n)throw new TypeError(\"Unknown encoding: \"+r);r=(r+\"\").toLowerCase(),n=!0}}a(\nSo,\"slowToString\");f.prototype._isBuffer=!0;function _e(r,e,t){let n=r[e];r[e]=r[t],\nr[t]=n}a(_e,\"swap\");f.prototype.swap16=a(function(){let e=this.length;if(e%2!==0)\nthrow new RangeError(\"Buffer size must be a multiple of 16-bits\");for(let t=0;t<\ne;t+=2)_e(this,t,t+1);return this},\"swap16\");f.prototype.swap32=a(function(){let e=this.\nlength;if(e%4!==0)throw new RangeError(\"Buffer size must be a multiple of 32-bit\\\ns\");for(let t=0;t<e;t+=4)_e(this,t,t+3),_e(this,t+1,t+2);return this},\"swap32\");\nf.prototype.swap64=a(function(){let e=this.length;if(e%8!==0)throw new RangeError(\n\"Buffer size must be a multiple of 64-bits\");for(let t=0;t<e;t+=8)_e(this,t,t+7),\n_e(this,t+1,t+6),_e(this,t+2,t+5),_e(this,t+3,t+4);return this},\"swap64\");f.prototype.\ntoString=a(function(){let e=this.length;return e===0?\"\":arguments.length===0?On(\nthis,0,e):So.apply(this,arguments)},\"toString\");f.prototype.toLocaleString=f.prototype.\ntoString;f.prototype.equals=a(function(e){if(!f.isBuffer(e))throw new TypeError(\n\"Argument must be a Buffer\");return this===e?!0:f.compare(this,e)===0},\"equals\");\nf.prototype.inspect=a(function(){let e=\"\",t=Le.INSPECT_MAX_BYTES;return e=this.toString(\n\"hex\",0,t).replace(/(.{2})/g,\"$1 \").trim(),this.length>t&&(e+=\" ... \"),\"<Buffer \"+\ne+\">\"},\"inspect\");Bn&&(f.prototype[Bn]=f.prototype.inspect);f.prototype.compare=\na(function(e,t,n,i,s){if(ue(e,Uint8Array)&&(e=f.from(e,e.offset,e.byteLength)),!f.\nisBuffer(e))throw new TypeError('The \"target\" argument must be one of type Buffe\\\nr or Uint8Array. Received type '+typeof e);if(t===void 0&&(t=0),n===void 0&&(n=e?\ne.length:0),i===void 0&&(i=0),s===void 0&&(s=this.length),t<0||n>e.length||i<0||\ns>this.length)throw new RangeError(\"out of range index\");if(i>=s&&t>=n)return 0;\nif(i>=s)return-1;if(t>=n)return 1;if(t>>>=0,n>>>=0,i>>>=0,s>>>=0,this===e)return 0;\nlet o=s-i,u=n-t,c=Math.min(o,u),h=this.slice(i,s),l=e.slice(t,n);for(let y=0;y<c;++y)\nif(h[y]!==l[y]){o=h[y],u=l[y];break}return o<u?-1:u<o?1:0},\"compare\");function Un(r,e,t,n,i){\nif(r.length===0)return-1;if(typeof t==\"string\"?(n=t,t=0):t>2147483647?t=2147483647:\nt<-2147483648&&(t=-2147483648),t=+t,kt(t)&&(t=i?0:r.length-1),t<0&&(t=r.length+t),\nt>=r.length){if(i)return-1;t=r.length-1}else if(t<0)if(i)t=0;else return-1;if(typeof e==\n\"string\"&&(e=f.from(e,n)),f.isBuffer(e))return e.length===0?-1:Ln(r,e,t,n,i);if(typeof e==\n\"number\")return e=e&255,typeof Uint8Array.prototype.indexOf==\"function\"?i?Uint8Array.\nprototype.indexOf.call(r,e,t):Uint8Array.prototype.lastIndexOf.call(r,e,t):Ln(r,\n[e],t,n,i);throw new TypeError(\"val must be string, number or Buffer\")}a(Un,\"bid\\\nirectionalIndexOf\");function Ln(r,e,t,n,i){let s=1,o=r.length,u=e.length;if(n!==\nvoid 0&&(n=String(n).toLowerCase(),n===\"ucs2\"||n===\"ucs-2\"||n===\"utf16le\"||n===\"\\\nutf-16le\")){if(r.length<2||e.length<2)return-1;s=2,o/=2,u/=2,t/=2}function c(l,y){\nreturn s===1?l[y]:l.readUInt16BE(y*s)}a(c,\"read\");let h;if(i){let l=-1;for(h=t;h<\no;h++)if(c(r,h)===c(e,l===-1?0:h-l)){if(l===-1&&(l=h),h-l+1===u)return l*s}else l!==\n-1&&(h-=h-l),l=-1}else for(t+u>o&&(t=o-u),h=t;h>=0;h--){let l=!0;for(let y=0;y<u;y++)\nif(c(r,h+y)!==c(e,y)){l=!1;break}if(l)return h}return-1}a(Ln,\"arrayIndexOf\");f.prototype.\nincludes=a(function(e,t,n){return this.indexOf(e,t,n)!==-1},\"includes\");f.prototype.\nindexOf=a(function(e,t,n){return Un(this,e,t,n,!0)},\"indexOf\");f.prototype.lastIndexOf=\na(function(e,t,n){return Un(this,e,t,n,!1)},\"lastIndexOf\");function xo(r,e,t,n){\nt=Number(t)||0;let i=r.length-t;n?(n=Number(n),n>i&&(n=i)):n=i;let s=e.length;n>\ns/2&&(n=s/2);let o;for(o=0;o<n;++o){let u=parseInt(e.substr(o*2,2),16);if(kt(u))\nreturn o;r[t+o]=u}return o}a(xo,\"hexWrite\");function vo(r,e,t,n){return st(Rt(e,\nr.length-t),r,t,n)}a(vo,\"utf8Write\");function Eo(r,e,t,n){return st(Do(e),r,t,n)}\na(Eo,\"asciiWrite\");function _o(r,e,t,n){return st(Gn(e),r,t,n)}a(_o,\"base64Write\");\nfunction Ao(r,e,t,n){return st(ko(e,r.length-t),r,t,n)}a(Ao,\"ucs2Write\");f.prototype.\nwrite=a(function(e,t,n,i){if(t===void 0)i=\"utf8\",n=this.length,t=0;else if(n===void 0&&\ntypeof t==\"string\")i=t,n=this.length,t=0;else if(isFinite(t))t=t>>>0,isFinite(n)?\n(n=n>>>0,i===void 0&&(i=\"utf8\")):(i=n,n=void 0);else throw new Error(\"Buffer.wri\\\nte(string, encoding, offset[, length]) is no longer supported\");let s=this.length-\nt;if((n===void 0||n>s)&&(n=s),e.length>0&&(n<0||t<0)||t>this.length)throw new RangeError(\n\"Attempt to write outside buffer bounds\");i||(i=\"utf8\");let o=!1;for(;;)switch(i){case\"\\\nhex\":return xo(this,e,t,n);case\"utf8\":case\"utf-8\":return vo(this,e,t,n);case\"asc\\\nii\":case\"latin1\":case\"binary\":return Eo(this,e,t,n);case\"base64\":return _o(this,\ne,t,n);case\"ucs2\":case\"ucs-2\":case\"utf16le\":case\"utf-16le\":return Ao(this,e,t,n);default:\nif(o)throw new TypeError(\"Unknown encoding: \"+i);i=(\"\"+i).toLowerCase(),o=!0}},\"\\\nwrite\");f.prototype.toJSON=a(function(){return{type:\"Buffer\",data:Array.prototype.\nslice.call(this._arr||this,0)}},\"toJSON\");function Co(r,e,t){return e===0&&t===r.\nlength?Pt.fromByteArray(r):Pt.fromByteArray(r.slice(e,t))}a(Co,\"base64Slice\");function On(r,e,t){\nt=Math.min(r.length,t);let n=[],i=e;for(;i<t;){let s=r[i],o=null,u=s>239?4:s>223?\n3:s>191?2:1;if(i+u<=t){let c,h,l,y;switch(u){case 1:s<128&&(o=s);break;case 2:c=\nr[i+1],(c&192)===128&&(y=(s&31)<<6|c&63,y>127&&(o=y));break;case 3:c=r[i+1],h=r[i+\n2],(c&192)===128&&(h&192)===128&&(y=(s&15)<<12|(c&63)<<6|h&63,y>2047&&(y<55296||\ny>57343)&&(o=y));break;case 4:c=r[i+1],h=r[i+2],l=r[i+3],(c&192)===128&&(h&192)===\n128&&(l&192)===128&&(y=(s&15)<<18|(c&63)<<12|(h&63)<<6|l&63,y>65535&&y<1114112&&\n(o=y))}}o===null?(o=65533,u=1):o>65535&&(o-=65536,n.push(o>>>10&1023|55296),o=56320|\no&1023),n.push(o),i+=u}return Io(n)}a(On,\"utf8Slice\");var Rn=4096;function Io(r){\nlet e=r.length;if(e<=Rn)return String.fromCharCode.apply(String,r);let t=\"\",n=0;\nfor(;n<e;)t+=String.fromCharCode.apply(String,r.slice(n,n+=Rn));return t}a(Io,\"d\\\necodeCodePointsArray\");function To(r,e,t){let n=\"\";t=Math.min(r.length,t);for(let i=e;i<\nt;++i)n+=String.fromCharCode(r[i]&127);return n}a(To,\"asciiSlice\");function Po(r,e,t){\nlet n=\"\";t=Math.min(r.length,t);for(let i=e;i<t;++i)n+=String.fromCharCode(r[i]);\nreturn n}a(Po,\"latin1Slice\");function Bo(r,e,t){let n=r.length;(!e||e<0)&&(e=0),\n(!t||t<0||t>n)&&(t=n);let i=\"\";for(let s=e;s<t;++s)i+=Uo[r[s]];return i}a(Bo,\"he\\\nxSlice\");function Lo(r,e,t){let n=r.slice(e,t),i=\"\";for(let s=0;s<n.length-1;s+=\n2)i+=String.fromCharCode(n[s]+n[s+1]*256);return i}a(Lo,\"utf16leSlice\");f.prototype.\nslice=a(function(e,t){let n=this.length;e=~~e,t=t===void 0?n:~~t,e<0?(e+=n,e<0&&\n(e=0)):e>n&&(e=n),t<0?(t+=n,t<0&&(t=0)):t>n&&(t=n),t<e&&(t=e);let i=this.subarray(\ne,t);return Object.setPrototypeOf(i,f.prototype),i},\"slice\");function q(r,e,t){if(r%\n1!==0||r<0)throw new RangeError(\"offset is not uint\");if(r+e>t)throw new RangeError(\n\"Trying to access beyond buffer length\")}a(q,\"checkOffset\");f.prototype.readUintLE=\nf.prototype.readUIntLE=a(function(e,t,n){e=e>>>0,t=t>>>0,n||q(e,t,this.length);let i=this[e],\ns=1,o=0;for(;++o<t&&(s*=256);)i+=this[e+o]*s;return i},\"readUIntLE\");f.prototype.\nreadUintBE=f.prototype.readUIntBE=a(function(e,t,n){e=e>>>0,t=t>>>0,n||q(e,t,this.\nlength);let i=this[e+--t],s=1;for(;t>0&&(s*=256);)i+=this[e+--t]*s;return i},\"re\\\nadUIntBE\");f.prototype.readUint8=f.prototype.readUInt8=a(function(e,t){return e=\ne>>>0,t||q(e,1,this.length),this[e]},\"readUInt8\");f.prototype.readUint16LE=f.prototype.\nreadUInt16LE=a(function(e,t){return e=e>>>0,t||q(e,2,this.length),this[e]|this[e+\n1]<<8},\"readUInt16LE\");f.prototype.readUint16BE=f.prototype.readUInt16BE=a(function(e,t){\nreturn e=e>>>0,t||q(e,2,this.length),this[e]<<8|this[e+1]},\"readUInt16BE\");f.prototype.\nreadUint32LE=f.prototype.readUInt32LE=a(function(e,t){return e=e>>>0,t||q(e,4,this.\nlength),(this[e]|this[e+1]<<8|this[e+2]<<16)+this[e+3]*16777216},\"readUInt32LE\");\nf.prototype.readUint32BE=f.prototype.readUInt32BE=a(function(e,t){return e=e>>>0,\nt||q(e,4,this.length),this[e]*16777216+(this[e+1]<<16|this[e+2]<<8|this[e+3])},\"\\\nreadUInt32BE\");f.prototype.readBigUInt64LE=ge(a(function(e){e=e>>>0,Be(e,\"offset\");\nlet t=this[e],n=this[e+7];(t===void 0||n===void 0)&&We(e,this.length-8);let i=t+\nthis[++e]*2**8+this[++e]*2**16+this[++e]*2**24,s=this[++e]+this[++e]*2**8+this[++e]*\n2**16+n*2**24;return BigInt(i)+(BigInt(s)<<BigInt(32))},\"readBigUInt64LE\"));f.prototype.\nreadBigUInt64BE=ge(a(function(e){e=e>>>0,Be(e,\"offset\");let t=this[e],n=this[e+7];\n(t===void 0||n===void 0)&&We(e,this.length-8);let i=t*2**24+this[++e]*2**16+this[++e]*\n2**8+this[++e],s=this[++e]*2**24+this[++e]*2**16+this[++e]*2**8+n;return(BigInt(\ni)<<BigInt(32))+BigInt(s)},\"readBigUInt64BE\"));f.prototype.readIntLE=a(function(e,t,n){\ne=e>>>0,t=t>>>0,n||q(e,t,this.length);let i=this[e],s=1,o=0;for(;++o<t&&(s*=256);)\ni+=this[e+o]*s;return s*=128,i>=s&&(i-=Math.pow(2,8*t)),i},\"readIntLE\");f.prototype.\nreadIntBE=a(function(e,t,n){e=e>>>0,t=t>>>0,n||q(e,t,this.length);let i=t,s=1,o=this[e+\n--i];for(;i>0&&(s*=256);)o+=this[e+--i]*s;return s*=128,o>=s&&(o-=Math.pow(2,8*t)),\no},\"readIntBE\");f.prototype.readInt8=a(function(e,t){return e=e>>>0,t||q(e,1,this.\nlength),this[e]&128?(255-this[e]+1)*-1:this[e]},\"readInt8\");f.prototype.readInt16LE=\na(function(e,t){e=e>>>0,t||q(e,2,this.length);let n=this[e]|this[e+1]<<8;return n&\n32768?n|4294901760:n},\"readInt16LE\");f.prototype.readInt16BE=a(function(e,t){e=e>>>\n0,t||q(e,2,this.length);let n=this[e+1]|this[e]<<8;return n&32768?n|4294901760:n},\n\"readInt16BE\");f.prototype.readInt32LE=a(function(e,t){return e=e>>>0,t||q(e,4,this.\nlength),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},\"readInt32LE\");f.prototype.\nreadInt32BE=a(function(e,t){return e=e>>>0,t||q(e,4,this.length),this[e]<<24|this[e+\n1]<<16|this[e+2]<<8|this[e+3]},\"readInt32BE\");f.prototype.readBigInt64LE=ge(a(function(e){\ne=e>>>0,Be(e,\"offset\");let t=this[e],n=this[e+7];(t===void 0||n===void 0)&&We(e,\nthis.length-8);let i=this[e+4]+this[e+5]*2**8+this[e+6]*2**16+(n<<24);return(BigInt(\ni)<<BigInt(32))+BigInt(t+this[++e]*2**8+this[++e]*2**16+this[++e]*2**24)},\"readB\\\nigInt64LE\"));f.prototype.readBigInt64BE=ge(a(function(e){e=e>>>0,Be(e,\"offset\");\nlet t=this[e],n=this[e+7];(t===void 0||n===void 0)&&We(e,this.length-8);let i=(t<<\n24)+this[++e]*2**16+this[++e]*2**8+this[++e];return(BigInt(i)<<BigInt(32))+BigInt(\nthis[++e]*2**24+this[++e]*2**16+this[++e]*2**8+n)},\"readBigInt64BE\"));f.prototype.\nreadFloatLE=a(function(e,t){return e=e>>>0,t||q(e,4,this.length),Pe.read(this,e,\n!0,23,4)},\"readFloatLE\");f.prototype.readFloatBE=a(function(e,t){return e=e>>>0,\nt||q(e,4,this.length),Pe.read(this,e,!1,23,4)},\"readFloatBE\");f.prototype.readDoubleLE=\na(function(e,t){return e=e>>>0,t||q(e,8,this.length),Pe.read(this,e,!0,52,8)},\"r\\\neadDoubleLE\");f.prototype.readDoubleBE=a(function(e,t){return e=e>>>0,t||q(e,8,this.\nlength),Pe.read(this,e,!1,52,8)},\"readDoubleBE\");function Y(r,e,t,n,i,s){if(!f.isBuffer(\nr))throw new TypeError('\"buffer\" argument must be a Buffer instance');if(e>i||e<\ns)throw new RangeError('\"value\" argument is out of bounds');if(t+n>r.length)throw new RangeError(\n\"Index out of range\")}a(Y,\"checkInt\");f.prototype.writeUintLE=f.prototype.writeUIntLE=\na(function(e,t,n,i){if(e=+e,t=t>>>0,n=n>>>0,!i){let u=Math.pow(2,8*n)-1;Y(this,e,\nt,n,u,0)}let s=1,o=0;for(this[t]=e&255;++o<n&&(s*=256);)this[t+o]=e/s&255;return t+\nn},\"writeUIntLE\");f.prototype.writeUintBE=f.prototype.writeUIntBE=a(function(e,t,n,i){\nif(e=+e,t=t>>>0,n=n>>>0,!i){let u=Math.pow(2,8*n)-1;Y(this,e,t,n,u,0)}let s=n-1,\no=1;for(this[t+s]=e&255;--s>=0&&(o*=256);)this[t+s]=e/o&255;return t+n},\"writeUI\\\nntBE\");f.prototype.writeUint8=f.prototype.writeUInt8=a(function(e,t,n){return e=\n+e,t=t>>>0,n||Y(this,e,t,1,255,0),this[t]=e&255,t+1},\"writeUInt8\");f.prototype.writeUint16LE=\nf.prototype.writeUInt16LE=a(function(e,t,n){return e=+e,t=t>>>0,n||Y(this,e,t,2,\n65535,0),this[t]=e&255,this[t+1]=e>>>8,t+2},\"writeUInt16LE\");f.prototype.writeUint16BE=\nf.prototype.writeUInt16BE=a(function(e,t,n){return e=+e,t=t>>>0,n||Y(this,e,t,2,\n65535,0),this[t]=e>>>8,this[t+1]=e&255,t+2},\"writeUInt16BE\");f.prototype.writeUint32LE=\nf.prototype.writeUInt32LE=a(function(e,t,n){return e=+e,t=t>>>0,n||Y(this,e,t,4,\n4294967295,0),this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=e&255,t+\n4},\"writeUInt32LE\");f.prototype.writeUint32BE=f.prototype.writeUInt32BE=a(function(e,t,n){\nreturn e=+e,t=t>>>0,n||Y(this,e,t,4,4294967295,0),this[t]=e>>>24,this[t+1]=e>>>16,\nthis[t+2]=e>>>8,this[t+3]=e&255,t+4},\"writeUInt32BE\");function Nn(r,e,t,n,i){Hn(\ne,n,i,r,t,7);let s=Number(e&BigInt(4294967295));r[t++]=s,s=s>>8,r[t++]=s,s=s>>8,\nr[t++]=s,s=s>>8,r[t++]=s;let o=Number(e>>BigInt(32)&BigInt(4294967295));return r[t++]=\no,o=o>>8,r[t++]=o,o=o>>8,r[t++]=o,o=o>>8,r[t++]=o,t}a(Nn,\"wrtBigUInt64LE\");function qn(r,e,t,n,i){\nHn(e,n,i,r,t,7);let s=Number(e&BigInt(4294967295));r[t+7]=s,s=s>>8,r[t+6]=s,s=s>>\n8,r[t+5]=s,s=s>>8,r[t+4]=s;let o=Number(e>>BigInt(32)&BigInt(4294967295));return r[t+\n3]=o,o=o>>8,r[t+2]=o,o=o>>8,r[t+1]=o,o=o>>8,r[t]=o,t+8}a(qn,\"wrtBigUInt64BE\");f.\nprototype.writeBigUInt64LE=ge(a(function(e,t=0){return Nn(this,e,t,BigInt(0),BigInt(\n\"0xffffffffffffffff\"))},\"writeBigUInt64LE\"));f.prototype.writeBigUInt64BE=ge(a(function(e,t=0){\nreturn qn(this,e,t,BigInt(0),BigInt(\"0xffffffffffffffff\"))},\"writeBigUInt64BE\"));\nf.prototype.writeIntLE=a(function(e,t,n,i){if(e=+e,t=t>>>0,!i){let c=Math.pow(2,\n8*n-1);Y(this,e,t,n,c-1,-c)}let s=0,o=1,u=0;for(this[t]=e&255;++s<n&&(o*=256);)e<\n0&&u===0&&this[t+s-1]!==0&&(u=1),this[t+s]=(e/o>>0)-u&255;return t+n},\"writeIntL\\\nE\");f.prototype.writeIntBE=a(function(e,t,n,i){if(e=+e,t=t>>>0,!i){let c=Math.pow(\n2,8*n-1);Y(this,e,t,n,c-1,-c)}let s=n-1,o=1,u=0;for(this[t+s]=e&255;--s>=0&&(o*=\n256);)e<0&&u===0&&this[t+s+1]!==0&&(u=1),this[t+s]=(e/o>>0)-u&255;return t+n},\"w\\\nriteIntBE\");f.prototype.writeInt8=a(function(e,t,n){return e=+e,t=t>>>0,n||Y(this,\ne,t,1,127,-128),e<0&&(e=255+e+1),this[t]=e&255,t+1},\"writeInt8\");f.prototype.writeInt16LE=\na(function(e,t,n){return e=+e,t=t>>>0,n||Y(this,e,t,2,32767,-32768),this[t]=e&255,\nthis[t+1]=e>>>8,t+2},\"writeInt16LE\");f.prototype.writeInt16BE=a(function(e,t,n){\nreturn e=+e,t=t>>>0,n||Y(this,e,t,2,32767,-32768),this[t]=e>>>8,this[t+1]=e&255,\nt+2},\"writeInt16BE\");f.prototype.writeInt32LE=a(function(e,t,n){return e=+e,t=t>>>\n0,n||Y(this,e,t,4,2147483647,-2147483648),this[t]=e&255,this[t+1]=e>>>8,this[t+2]=\ne>>>16,this[t+3]=e>>>24,t+4},\"writeInt32LE\");f.prototype.writeInt32BE=a(function(e,t,n){\nreturn e=+e,t=t>>>0,n||Y(this,e,t,4,2147483647,-2147483648),e<0&&(e=4294967295+e+\n1),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=e&255,t+4},\"writeIn\\\nt32BE\");f.prototype.writeBigInt64LE=ge(a(function(e,t=0){return Nn(this,e,t,-BigInt(\n\"0x8000000000000000\"),BigInt(\"0x7fffffffffffffff\"))},\"writeBigInt64LE\"));f.prototype.\nwriteBigInt64BE=ge(a(function(e,t=0){return qn(this,e,t,-BigInt(\"0x8000000000000\\\n000\"),BigInt(\"0x7fffffffffffffff\"))},\"writeBigInt64BE\"));function Qn(r,e,t,n,i,s){\nif(t+n>r.length)throw new RangeError(\"Index out of range\");if(t<0)throw new RangeError(\n\"Index out of range\")}a(Qn,\"checkIEEE754\");function Wn(r,e,t,n,i){return e=+e,t=\nt>>>0,i||Qn(r,e,t,4,34028234663852886e22,-34028234663852886e22),Pe.write(r,e,t,n,\n23,4),t+4}a(Wn,\"writeFloat\");f.prototype.writeFloatLE=a(function(e,t,n){return Wn(\nthis,e,t,!0,n)},\"writeFloatLE\");f.prototype.writeFloatBE=a(function(e,t,n){return Wn(\nthis,e,t,!1,n)},\"writeFloatBE\");function jn(r,e,t,n,i){return e=+e,t=t>>>0,i||Qn(\nr,e,t,8,17976931348623157e292,-17976931348623157e292),Pe.write(r,e,t,n,52,8),t+8}\na(jn,\"writeDouble\");f.prototype.writeDoubleLE=a(function(e,t,n){return jn(this,e,\nt,!0,n)},\"writeDoubleLE\");f.prototype.writeDoubleBE=a(function(e,t,n){return jn(\nthis,e,t,!1,n)},\"writeDoubleBE\");f.prototype.copy=a(function(e,t,n,i){if(!f.isBuffer(\ne))throw new TypeError(\"argument should be a Buffer\");if(n||(n=0),!i&&i!==0&&(i=\nthis.length),t>=e.length&&(t=e.length),t||(t=0),i>0&&i<n&&(i=n),i===n||e.length===\n0||this.length===0)return 0;if(t<0)throw new RangeError(\"targetStart out of boun\\\nds\");if(n<0||n>=this.length)throw new RangeError(\"Index out of range\");if(i<0)throw new RangeError(\n\"sourceEnd out of bounds\");i>this.length&&(i=this.length),e.length-t<i-n&&(i=e.length-\nt+n);let s=i-n;return this===e&&typeof Uint8Array.prototype.copyWithin==\"functio\\\nn\"?this.copyWithin(t,n,i):Uint8Array.prototype.set.call(e,this.subarray(n,i),t),\ns},\"copy\");f.prototype.fill=a(function(e,t,n,i){if(typeof e==\"string\"){if(typeof t==\n\"string\"?(i=t,t=0,n=this.length):typeof n==\"string\"&&(i=n,n=this.length),i!==void 0&&\ntypeof i!=\"string\")throw new TypeError(\"encoding must be a string\");if(typeof i==\n\"string\"&&!f.isEncoding(i))throw new TypeError(\"Unknown encoding: \"+i);if(e.length===\n1){let o=e.charCodeAt(0);(i===\"utf8\"&&o<128||i===\"latin1\")&&(e=o)}}else typeof e==\n\"number\"?e=e&255:typeof e==\"boolean\"&&(e=Number(e));if(t<0||this.length<t||this.\nlength<n)throw new RangeError(\"Out of range index\");if(n<=t)return this;t=t>>>0,\nn=n===void 0?this.length:n>>>0,e||(e=0);let s;if(typeof e==\"number\")for(s=t;s<n;++s)\nthis[s]=e;else{let o=f.isBuffer(e)?e:f.from(e,i),u=o.length;if(u===0)throw new TypeError(\n'The value \"'+e+'\" is invalid for argument \"value\"');for(s=0;s<n-t;++s)this[s+t]=\no[s%u]}return this},\"fill\");var Te={};function Dt(r,e,t){var n;Te[r]=(n=class extends t{constructor(){\nsuper(),Object.defineProperty(this,\"message\",{value:e.apply(this,arguments),writable:!0,\nconfigurable:!0}),this.name=`${this.name} [${r}]`,this.stack,delete this.name}get code(){\nreturn r}set code(s){Object.defineProperty(this,\"code\",{configurable:!0,enumerable:!0,\nvalue:s,writable:!0})}toString(){return`${this.name} [${r}]: ${this.message}`}},\na(n,\"NodeError\"),n)}a(Dt,\"E\");Dt(\"ERR_BUFFER_OUT_OF_BOUNDS\",function(r){return r?\n`${r} is outside of buffer bounds`:\"Attempt to access memory outside buffer boun\\\nds\"},RangeError);Dt(\"ERR_INVALID_ARG_TYPE\",function(r,e){return`The \"${r}\" argum\\\nent must be of type number. Received type ${typeof e}`},TypeError);Dt(\"ERR_OUT_O\\\nF_RANGE\",function(r,e,t){let n=`The value of \"${r}\" is out of range.`,i=t;return Number.\nisInteger(t)&&Math.abs(t)>2**32?i=Fn(String(t)):typeof t==\"bigint\"&&(i=String(t),\n(t>BigInt(2)**BigInt(32)||t<-(BigInt(2)**BigInt(32)))&&(i=Fn(i)),i+=\"n\"),n+=` It\\\n must be ${e}. Received ${i}`,n},RangeError);function Fn(r){let e=\"\",t=r.length,\nn=r[0]===\"-\"?1:0;for(;t>=n+4;t-=3)e=`_${r.slice(t-3,t)}${e}`;return`${r.slice(0,\nt)}${e}`}a(Fn,\"addNumericalSeparator\");function Ro(r,e,t){Be(e,\"offset\"),(r[e]===\nvoid 0||r[e+t]===void 0)&&We(e,r.length-(t+1))}a(Ro,\"checkBounds\");function Hn(r,e,t,n,i,s){\nif(r>t||r<e){let o=typeof e==\"bigint\"?\"n\":\"\",u;throw s>3?e===0||e===BigInt(0)?u=\n`>= 0${o} and < 2${o} ** ${(s+1)*8}${o}`:u=`>= -(2${o} ** ${(s+1)*8-1}${o}) and \\\n< 2 ** ${(s+1)*8-1}${o}`:u=`>= ${e}${o} and <= ${t}${o}`,new Te.ERR_OUT_OF_RANGE(\n\"value\",u,r)}Ro(n,i,s)}a(Hn,\"checkIntBI\");function Be(r,e){if(typeof r!=\"number\")\nthrow new Te.ERR_INVALID_ARG_TYPE(e,\"number\",r)}a(Be,\"validateNumber\");function We(r,e,t){\nthrow Math.floor(r)!==r?(Be(r,t),new Te.ERR_OUT_OF_RANGE(t||\"offset\",\"an integer\",\nr)):e<0?new Te.ERR_BUFFER_OUT_OF_BOUNDS:new Te.ERR_OUT_OF_RANGE(t||\"offset\",`>= ${t?\n1:0} and <= ${e}`,r)}a(We,\"boundsError\");var Fo=/[^+/0-9A-Za-z-_]/g;function Mo(r){\nif(r=r.split(\"=\")[0],r=r.trim().replace(Fo,\"\"),r.length<2)return\"\";for(;r.length%\n4!==0;)r=r+\"=\";return r}a(Mo,\"base64clean\");function Rt(r,e){e=e||1/0;let t,n=r.\nlength,i=null,s=[];for(let o=0;o<n;++o){if(t=r.charCodeAt(o),t>55295&&t<57344){if(!i){\nif(t>56319){(e-=3)>-1&&s.push(239,191,189);continue}else if(o+1===n){(e-=3)>-1&&\ns.push(239,191,189);continue}i=t;continue}if(t<56320){(e-=3)>-1&&s.push(239,191,\n189),i=t;continue}t=(i-55296<<10|t-56320)+65536}else i&&(e-=3)>-1&&s.push(239,191,\n189);if(i=null,t<128){if((e-=1)<0)break;s.push(t)}else if(t<2048){if((e-=2)<0)break;\ns.push(t>>6|192,t&63|128)}else if(t<65536){if((e-=3)<0)break;s.push(t>>12|224,t>>\n6&63|128,t&63|128)}else if(t<1114112){if((e-=4)<0)break;s.push(t>>18|240,t>>12&63|\n128,t>>6&63|128,t&63|128)}else throw new Error(\"Invalid code point\")}return s}a(\nRt,\"utf8ToBytes\");function Do(r){let e=[];for(let t=0;t<r.length;++t)e.push(r.charCodeAt(\nt)&255);return e}a(Do,\"asciiToBytes\");function ko(r,e){let t,n,i,s=[];for(let o=0;o<\nr.length&&!((e-=2)<0);++o)t=r.charCodeAt(o),n=t>>8,i=t%256,s.push(i),s.push(n);return s}\na(ko,\"utf16leToBytes\");function Gn(r){return Pt.toByteArray(Mo(r))}a(Gn,\"base64T\\\noBytes\");function st(r,e,t,n){let i;for(i=0;i<n&&!(i+t>=e.length||i>=r.length);++i)\ne[i+t]=r[i];return i}a(st,\"blitBuffer\");function ue(r,e){return r instanceof e||\nr!=null&&r.constructor!=null&&r.constructor.name!=null&&r.constructor.name===e.name}\na(ue,\"isInstance\");function kt(r){return r!==r}a(kt,\"numberIsNaN\");var Uo=function(){\nlet r=\"0123456789abcdef\",e=new Array(256);for(let t=0;t<16;++t){let n=t*16;for(let i=0;i<\n16;++i)e[n+i]=r[t]+r[i]}return e}();function ge(r){return typeof BigInt>\"u\"?Oo:r}\na(ge,\"defineBigIntMethod\");function Oo(){throw new Error(\"BigInt not supported\")}\na(Oo,\"BufferBigIntNotDefined\")});var b,S,v,g,d,m,p=z(()=>{\"use strict\";b=globalThis,S=globalThis.setImmediate??(r=>setTimeout(\nr,0)),v=globalThis.clearImmediate??(r=>clearTimeout(r)),g=globalThis.crypto??{};\ng.subtle??(g.subtle={});d=typeof globalThis.Buffer==\"function\"&&typeof globalThis.\nBuffer.allocUnsafe==\"function\"?globalThis.Buffer:$n().Buffer,m=globalThis.process??\n{};m.env??(m.env={});try{m.nextTick(()=>{})}catch{let e=Promise.resolve();m.nextTick=\ne.then.bind(e)}});var we=T((Jc,Ut)=>{\"use strict\";p();var Re=typeof Reflect==\"object\"?Reflect:null,\nVn=Re&&typeof Re.apply==\"function\"?Re.apply:a(function(e,t,n){return Function.prototype.\napply.call(e,t,n)},\"ReflectApply\"),ot;Re&&typeof Re.ownKeys==\"function\"?ot=Re.ownKeys:\nObject.getOwnPropertySymbols?ot=a(function(e){return Object.getOwnPropertyNames(\ne).concat(Object.getOwnPropertySymbols(e))},\"ReflectOwnKeys\"):ot=a(function(e){return Object.\ngetOwnPropertyNames(e)},\"ReflectOwnKeys\");function No(r){console&&console.warn&&\nconsole.warn(r)}a(No,\"ProcessEmitWarning\");var zn=Number.isNaN||a(function(e){return e!==\ne},\"NumberIsNaN\");function L(){L.init.call(this)}a(L,\"EventEmitter\");Ut.exports=\nL;Ut.exports.once=jo;L.EventEmitter=L;L.prototype._events=void 0;L.prototype._eventsCount=\n0;L.prototype._maxListeners=void 0;var Kn=10;function at(r){if(typeof r!=\"functi\\\non\")throw new TypeError('The \"listener\" argument must be of type Function. Recei\\\nved type '+typeof r)}a(at,\"checkListener\");Object.defineProperty(L,\"defaultMaxLi\\\nsteners\",{enumerable:!0,get:a(function(){return Kn},\"get\"),set:a(function(r){if(typeof r!=\n\"number\"||r<0||zn(r))throw new RangeError('The value of \"defaultMaxListeners\" is\\\n out of range. It must be a non-negative number. Received '+r+\".\");Kn=r},\"set\")});\nL.init=function(){(this._events===void 0||this._events===Object.getPrototypeOf(this).\n_events)&&(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=\nthis._maxListeners||void 0};L.prototype.setMaxListeners=a(function(e){if(typeof e!=\n\"number\"||e<0||zn(e))throw new RangeError('The value of \"n\" is out of range. It \\\nmust be a non-negative number. Received '+e+\".\");return this._maxListeners=e,this},\n\"setMaxListeners\");function Yn(r){return r._maxListeners===void 0?L.defaultMaxListeners:\nr._maxListeners}a(Yn,\"_getMaxListeners\");L.prototype.getMaxListeners=a(function(){\nreturn Yn(this)},\"getMaxListeners\");L.prototype.emit=a(function(e){for(var t=[],\nn=1;n<arguments.length;n++)t.push(arguments[n]);var i=e===\"error\",s=this._events;\nif(s!==void 0)i=i&&s.error===void 0;else if(!i)return!1;if(i){var o;if(t.length>\n0&&(o=t[0]),o instanceof Error)throw o;var u=new Error(\"Unhandled error.\"+(o?\" (\"+\no.message+\")\":\"\"));throw u.context=o,u}var c=s[e];if(c===void 0)return!1;if(typeof c==\n\"function\")Vn(c,this,t);else for(var h=c.length,l=ti(c,h),n=0;n<h;++n)Vn(l[n],this,\nt);return!0},\"emit\");function Zn(r,e,t,n){var i,s,o;if(at(t),s=r._events,s===void 0?\n(s=r._events=Object.create(null),r._eventsCount=0):(s.newListener!==void 0&&(r.emit(\n\"newListener\",e,t.listener?t.listener:t),s=r._events),o=s[e]),o===void 0)o=s[e]=\nt,++r._eventsCount;else if(typeof o==\"function\"?o=s[e]=n?[t,o]:[o,t]:n?o.unshift(\nt):o.push(t),i=Yn(r),i>0&&o.length>i&&!o.warned){o.warned=!0;var u=new Error(\"Po\\\nssible EventEmitter memory leak detected. \"+o.length+\" \"+String(e)+\" listeners a\\\ndded. Use emitter.setMaxListeners() to increase limit\");u.name=\"MaxListenersExce\\\nededWarning\",u.emitter=r,u.type=e,u.count=o.length,No(u)}return r}a(Zn,\"_addList\\\nener\");L.prototype.addListener=a(function(e,t){return Zn(this,e,t,!1)},\"addListe\\\nner\");L.prototype.on=L.prototype.addListener;L.prototype.prependListener=a(function(e,t){\nreturn Zn(this,e,t,!0)},\"prependListener\");function qo(){if(!this.fired)return this.\ntarget.removeListener(this.type,this.wrapFn),this.fired=!0,arguments.length===0?\nthis.listener.call(this.target):this.listener.apply(this.target,arguments)}a(qo,\n\"onceWrapper\");function Jn(r,e,t){var n={fired:!1,wrapFn:void 0,target:r,type:e,\nlistener:t},i=qo.bind(n);return i.listener=t,n.wrapFn=i,i}a(Jn,\"_onceWrap\");L.prototype.\nonce=a(function(e,t){return at(t),this.on(e,Jn(this,e,t)),this},\"once\");L.prototype.\nprependOnceListener=a(function(e,t){return at(t),this.prependListener(e,Jn(this,\ne,t)),this},\"prependOnceListener\");L.prototype.removeListener=a(function(e,t){var n,\ni,s,o,u;if(at(t),i=this._events,i===void 0)return this;if(n=i[e],n===void 0)return this;\nif(n===t||n.listener===t)--this._eventsCount===0?this._events=Object.create(null):\n(delete i[e],i.removeListener&&this.emit(\"removeListener\",e,n.listener||t));else if(typeof n!=\n\"function\"){for(s=-1,o=n.length-1;o>=0;o--)if(n[o]===t||n[o].listener===t){u=n[o].\nlistener,s=o;break}if(s<0)return this;s===0?n.shift():Qo(n,s),n.length===1&&(i[e]=\nn[0]),i.removeListener!==void 0&&this.emit(\"removeListener\",e,u||t)}return this},\n\"removeListener\");L.prototype.off=L.prototype.removeListener;L.prototype.removeAllListeners=\na(function(e){var t,n,i;if(n=this._events,n===void 0)return this;if(n.removeListener===\nvoid 0)return arguments.length===0?(this._events=Object.create(null),this._eventsCount=\n0):n[e]!==void 0&&(--this._eventsCount===0?this._events=Object.create(null):delete n[e]),\nthis;if(arguments.length===0){var s=Object.keys(n),o;for(i=0;i<s.length;++i)o=s[i],\no!==\"removeListener\"&&this.removeAllListeners(o);return this.removeAllListeners(\n\"removeListener\"),this._events=Object.create(null),this._eventsCount=0,this}if(t=\nn[e],typeof t==\"function\")this.removeListener(e,t);else if(t!==void 0)for(i=t.length-\n1;i>=0;i--)this.removeListener(e,t[i]);return this},\"removeAllListeners\");function Xn(r,e,t){\nvar n=r._events;if(n===void 0)return[];var i=n[e];return i===void 0?[]:typeof i==\n\"function\"?t?[i.listener||i]:[i]:t?Wo(i):ti(i,i.length)}a(Xn,\"_listeners\");L.prototype.\nlisteners=a(function(e){return Xn(this,e,!0)},\"listeners\");L.prototype.rawListeners=\na(function(e){return Xn(this,e,!1)},\"rawListeners\");L.listenerCount=function(r,e){\nreturn typeof r.listenerCount==\"function\"?r.listenerCount(e):ei.call(r,e)};L.prototype.\nlistenerCount=ei;function ei(r){var e=this._events;if(e!==void 0){var t=e[r];if(typeof t==\n\"function\")return 1;if(t!==void 0)return t.length}return 0}a(ei,\"listenerCount\");\nL.prototype.eventNames=a(function(){return this._eventsCount>0?ot(this._events):\n[]},\"eventNames\");function ti(r,e){for(var t=new Array(e),n=0;n<e;++n)t[n]=r[n];\nreturn t}a(ti,\"arrayClone\");function Qo(r,e){for(;e+1<r.length;e++)r[e]=r[e+1];r.\npop()}a(Qo,\"spliceOne\");function Wo(r){for(var e=new Array(r.length),t=0;t<e.length;++t)\ne[t]=r[t].listener||r[t];return e}a(Wo,\"unwrapListeners\");function jo(r,e){return new Promise(\nfunction(t,n){function i(o){r.removeListener(e,s),n(o)}a(i,\"errorListener\");function s(){\ntypeof r.removeListener==\"function\"&&r.removeListener(\"error\",i),t([].slice.call(\narguments))}a(s,\"resolver\"),ri(r,e,s,{once:!0}),e!==\"error\"&&Ho(r,i,{once:!0})})}\na(jo,\"once\");function Ho(r,e,t){typeof r.on==\"function\"&&ri(r,\"error\",e,t)}a(Ho,\n\"addErrorHandlerIfEventEmitter\");function ri(r,e,t,n){if(typeof r.on==\"function\")\nn.once?r.once(e,t):r.on(e,t);else if(typeof r.addEventListener==\"function\")r.addEventListener(\ne,a(function i(s){n.once&&r.removeEventListener(e,i),t(s)},\"wrapListener\"));else\nthrow new TypeError('The \"emitter\" argument must be of type EventEmitter. Receiv\\\ned type '+typeof r)}a(ri,\"eventTargetAgnosticAddListener\")});var je={};ie(je,{default:()=>Go});var Go,He=z(()=>{\"use strict\";p();Go={}});function Ge(r){let e=1779033703,t=3144134277,n=1013904242,i=2773480762,s=1359893119,\no=2600822924,u=528734635,c=1541459225,h=0,l=0,y=[1116352408,1899447441,3049323471,\n3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,\n1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,\n604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,\n3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,\n1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,\n3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,\n883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,\n2361852424,2428436474,2756734187,3204031479,3329325298],x=a((A,w)=>A>>>w|A<<32-w,\n\"rrot\"),C=new Uint32Array(64),B=new Uint8Array(64),W=a(()=>{for(let R=0,G=0;R<16;R++,\nG+=4)C[R]=B[G]<<24|B[G+1]<<16|B[G+2]<<8|B[G+3];for(let R=16;R<64;R++){let G=x(C[R-\n15],7)^x(C[R-15],18)^C[R-15]>>>3,he=x(C[R-2],17)^x(C[R-2],19)^C[R-2]>>>10;C[R]=C[R-\n16]+G+C[R-7]+he|0}let A=e,w=t,P=n,V=i,k=s,j=o,ce=u,ee=c;for(let R=0;R<64;R++){let G=x(\nk,6)^x(k,11)^x(k,25),he=k&j^~k&ce,ye=ee+G+he+y[R]+C[R]|0,ve=x(A,2)^x(A,13)^x(A,22),\nme=A&w^A&P^w&P,se=ve+me|0;ee=ce,ce=j,j=k,k=V+ye|0,V=P,P=w,w=A,A=ye+se|0}e=e+A|0,\nt=t+w|0,n=n+P|0,i=i+V|0,s=s+k|0,o=o+j|0,u=u+ce|0,c=c+ee|0,l=0},\"process\"),X=a(A=>{\ntypeof A==\"string\"&&(A=new TextEncoder().encode(A));for(let w=0;w<A.length;w++)B[l++]=\nA[w],l===64&&W();h+=A.length},\"add\"),de=a(()=>{if(B[l++]=128,l==64&&W(),l+8>64){\nfor(;l<64;)B[l++]=0;W()}for(;l<58;)B[l++]=0;let A=h*8;B[l++]=A/1099511627776&255,\nB[l++]=A/4294967296&255,B[l++]=A>>>24,B[l++]=A>>>16&255,B[l++]=A>>>8&255,B[l++]=\nA&255,W();let w=new Uint8Array(32);return w[0]=e>>>24,w[1]=e>>>16&255,w[2]=e>>>8&\n255,w[3]=e&255,w[4]=t>>>24,w[5]=t>>>16&255,w[6]=t>>>8&255,w[7]=t&255,w[8]=n>>>24,\nw[9]=n>>>16&255,w[10]=n>>>8&255,w[11]=n&255,w[12]=i>>>24,w[13]=i>>>16&255,w[14]=\ni>>>8&255,w[15]=i&255,w[16]=s>>>24,w[17]=s>>>16&255,w[18]=s>>>8&255,w[19]=s&255,\nw[20]=o>>>24,w[21]=o>>>16&255,w[22]=o>>>8&255,w[23]=o&255,w[24]=u>>>24,w[25]=u>>>\n16&255,w[26]=u>>>8&255,w[27]=u&255,w[28]=c>>>24,w[29]=c>>>16&255,w[30]=c>>>8&255,\nw[31]=c&255,w},\"digest\");return r===void 0?{add:X,digest:de}:(X(r),de())}var ni=z(\n()=>{\"use strict\";p();a(Ge,\"sha256\")});var U,$e,ii=z(()=>{\"use strict\";p();U=class U{constructor(){_(this,\"_dataLength\",\n0);_(this,\"_bufferLength\",0);_(this,\"_state\",new Int32Array(4));_(this,\"_buffer\",\nnew ArrayBuffer(68));_(this,\"_buffer8\");_(this,\"_buffer32\");this._buffer8=new Uint8Array(\nthis._buffer,0,68),this._buffer32=new Uint32Array(this._buffer,0,17),this.start()}static hashByteArray(e,t=!1){\nreturn this.onePassHasher.start().appendByteArray(e).end(t)}static hashStr(e,t=!1){\nreturn this.onePassHasher.start().appendStr(e).end(t)}static hashAsciiStr(e,t=!1){\nreturn this.onePassHasher.start().appendAsciiStr(e).end(t)}static _hex(e){let t=U.\nhexChars,n=U.hexOut,i,s,o,u;for(u=0;u<4;u+=1)for(s=u*8,i=e[u],o=0;o<8;o+=2)n[s+1+\no]=t.charAt(i&15),i>>>=4,n[s+0+o]=t.charAt(i&15),i>>>=4;return n.join(\"\")}static _md5cycle(e,t){\nlet n=e[0],i=e[1],s=e[2],o=e[3];n+=(i&s|~i&o)+t[0]-680876936|0,n=(n<<7|n>>>25)+i|\n0,o+=(n&i|~n&s)+t[1]-389564586|0,o=(o<<12|o>>>20)+n|0,s+=(o&n|~o&i)+t[2]+606105819|\n0,s=(s<<17|s>>>15)+o|0,i+=(s&o|~s&n)+t[3]-1044525330|0,i=(i<<22|i>>>10)+s|0,n+=(i&\ns|~i&o)+t[4]-176418897|0,n=(n<<7|n>>>25)+i|0,o+=(n&i|~n&s)+t[5]+1200080426|0,o=(o<<\n12|o>>>20)+n|0,s+=(o&n|~o&i)+t[6]-1473231341|0,s=(s<<17|s>>>15)+o|0,i+=(s&o|~s&n)+\nt[7]-45705983|0,i=(i<<22|i>>>10)+s|0,n+=(i&s|~i&o)+t[8]+1770035416|0,n=(n<<7|n>>>\n25)+i|0,o+=(n&i|~n&s)+t[9]-1958414417|0,o=(o<<12|o>>>20)+n|0,s+=(o&n|~o&i)+t[10]-\n42063|0,s=(s<<17|s>>>15)+o|0,i+=(s&o|~s&n)+t[11]-1990404162|0,i=(i<<22|i>>>10)+s|\n0,n+=(i&s|~i&o)+t[12]+1804603682|0,n=(n<<7|n>>>25)+i|0,o+=(n&i|~n&s)+t[13]-40341101|\n0,o=(o<<12|o>>>20)+n|0,s+=(o&n|~o&i)+t[14]-1502002290|0,s=(s<<17|s>>>15)+o|0,i+=\n(s&o|~s&n)+t[15]+1236535329|0,i=(i<<22|i>>>10)+s|0,n+=(i&o|s&~o)+t[1]-165796510|\n0,n=(n<<5|n>>>27)+i|0,o+=(n&s|i&~s)+t[6]-1069501632|0,o=(o<<9|o>>>23)+n|0,s+=(o&\ni|n&~i)+t[11]+643717713|0,s=(s<<14|s>>>18)+o|0,i+=(s&n|o&~n)+t[0]-373897302|0,i=\n(i<<20|i>>>12)+s|0,n+=(i&o|s&~o)+t[5]-701558691|0,n=(n<<5|n>>>27)+i|0,o+=(n&s|i&\n~s)+t[10]+38016083|0,o=(o<<9|o>>>23)+n|0,s+=(o&i|n&~i)+t[15]-660478335|0,s=(s<<14|\ns>>>18)+o|0,i+=(s&n|o&~n)+t[4]-405537848|0,i=(i<<20|i>>>12)+s|0,n+=(i&o|s&~o)+t[9]+\n568446438|0,n=(n<<5|n>>>27)+i|0,o+=(n&s|i&~s)+t[14]-1019803690|0,o=(o<<9|o>>>23)+\nn|0,s+=(o&i|n&~i)+t[3]-187363961|0,s=(s<<14|s>>>18)+o|0,i+=(s&n|o&~n)+t[8]+1163531501|\n0,i=(i<<20|i>>>12)+s|0,n+=(i&o|s&~o)+t[13]-1444681467|0,n=(n<<5|n>>>27)+i|0,o+=(n&\ns|i&~s)+t[2]-51403784|0,o=(o<<9|o>>>23)+n|0,s+=(o&i|n&~i)+t[7]+1735328473|0,s=(s<<\n14|s>>>18)+o|0,i+=(s&n|o&~n)+t[12]-1926607734|0,i=(i<<20|i>>>12)+s|0,n+=(i^s^o)+\nt[5]-378558|0,n=(n<<4|n>>>28)+i|0,o+=(n^i^s)+t[8]-2022574463|0,o=(o<<11|o>>>21)+\nn|0,s+=(o^n^i)+t[11]+1839030562|0,s=(s<<16|s>>>16)+o|0,i+=(s^o^n)+t[14]-35309556|\n0,i=(i<<23|i>>>9)+s|0,n+=(i^s^o)+t[1]-1530992060|0,n=(n<<4|n>>>28)+i|0,o+=(n^i^s)+\nt[4]+1272893353|0,o=(o<<11|o>>>21)+n|0,s+=(o^n^i)+t[7]-155497632|0,s=(s<<16|s>>>\n16)+o|0,i+=(s^o^n)+t[10]-1094730640|0,i=(i<<23|i>>>9)+s|0,n+=(i^s^o)+t[13]+681279174|\n0,n=(n<<4|n>>>28)+i|0,o+=(n^i^s)+t[0]-358537222|0,o=(o<<11|o>>>21)+n|0,s+=(o^n^i)+\nt[3]-722521979|0,s=(s<<16|s>>>16)+o|0,i+=(s^o^n)+t[6]+76029189|0,i=(i<<23|i>>>9)+\ns|0,n+=(i^s^o)+t[9]-640364487|0,n=(n<<4|n>>>28)+i|0,o+=(n^i^s)+t[12]-421815835|0,\no=(o<<11|o>>>21)+n|0,s+=(o^n^i)+t[15]+530742520|0,s=(s<<16|s>>>16)+o|0,i+=(s^o^n)+\nt[2]-995338651|0,i=(i<<23|i>>>9)+s|0,n+=(s^(i|~o))+t[0]-198630844|0,n=(n<<6|n>>>\n26)+i|0,o+=(i^(n|~s))+t[7]+1126891415|0,o=(o<<10|o>>>22)+n|0,s+=(n^(o|~i))+t[14]-\n1416354905|0,s=(s<<15|s>>>17)+o|0,i+=(o^(s|~n))+t[5]-57434055|0,i=(i<<21|i>>>11)+\ns|0,n+=(s^(i|~o))+t[12]+1700485571|0,n=(n<<6|n>>>26)+i|0,o+=(i^(n|~s))+t[3]-1894986606|\n0,o=(o<<10|o>>>22)+n|0,s+=(n^(o|~i))+t[10]-1051523|0,s=(s<<15|s>>>17)+o|0,i+=(o^\n(s|~n))+t[1]-2054922799|0,i=(i<<21|i>>>11)+s|0,n+=(s^(i|~o))+t[8]+1873313359|0,n=\n(n<<6|n>>>26)+i|0,o+=(i^(n|~s))+t[15]-30611744|0,o=(o<<10|o>>>22)+n|0,s+=(n^(o|~i))+\nt[6]-1560198380|0,s=(s<<15|s>>>17)+o|0,i+=(o^(s|~n))+t[13]+1309151649|0,i=(i<<21|\ni>>>11)+s|0,n+=(s^(i|~o))+t[4]-145523070|0,n=(n<<6|n>>>26)+i|0,o+=(i^(n|~s))+t[11]-\n1120210379|0,o=(o<<10|o>>>22)+n|0,s+=(n^(o|~i))+t[2]+718787259|0,s=(s<<15|s>>>17)+\no|0,i+=(o^(s|~n))+t[9]-343485551|0,i=(i<<21|i>>>11)+s|0,e[0]=n+e[0]|0,e[1]=i+e[1]|\n0,e[2]=s+e[2]|0,e[3]=o+e[3]|0}start(){return this._dataLength=0,this._bufferLength=\n0,this._state.set(U.stateIdentity),this}appendStr(e){let t=this._buffer8,n=this.\n_buffer32,i=this._bufferLength,s,o;for(o=0;o<e.length;o+=1){if(s=e.charCodeAt(o),\ns<128)t[i++]=s;else if(s<2048)t[i++]=(s>>>6)+192,t[i++]=s&63|128;else if(s<55296||\ns>56319)t[i++]=(s>>>12)+224,t[i++]=s>>>6&63|128,t[i++]=s&63|128;else{if(s=(s-55296)*\n1024+(e.charCodeAt(++o)-56320)+65536,s>1114111)throw new Error(\"Unicode standard\\\n supports code points up to U+10FFFF\");t[i++]=(s>>>18)+240,t[i++]=s>>>12&63|128,\nt[i++]=s>>>6&63|128,t[i++]=s&63|128}i>=64&&(this._dataLength+=64,U._md5cycle(this.\n_state,n),i-=64,n[0]=n[16])}return this._bufferLength=i,this}appendAsciiStr(e){let t=this.\n_buffer8,n=this._buffer32,i=this._bufferLength,s,o=0;for(;;){for(s=Math.min(e.length-\no,64-i);s--;)t[i++]=e.charCodeAt(o++);if(i<64)break;this._dataLength+=64,U._md5cycle(\nthis._state,n),i=0}return this._bufferLength=i,this}appendByteArray(e){let t=this.\n_buffer8,n=this._buffer32,i=this._bufferLength,s,o=0;for(;;){for(s=Math.min(e.length-\no,64-i);s--;)t[i++]=e[o++];if(i<64)break;this._dataLength+=64,U._md5cycle(this._state,\nn),i=0}return this._bufferLength=i,this}getState(){let e=this._state;return{buffer:String.\nfromCharCode.apply(null,Array.from(this._buffer8)),buflen:this._bufferLength,length:this.\n_dataLength,state:[e[0],e[1],e[2],e[3]]}}setState(e){let t=e.buffer,n=e.state,i=this.\n_state,s;for(this._dataLength=e.length,this._bufferLength=e.buflen,i[0]=n[0],i[1]=\nn[1],i[2]=n[2],i[3]=n[3],s=0;s<t.length;s+=1)this._buffer8[s]=t.charCodeAt(s)}end(e=!1){\nlet t=this._bufferLength,n=this._buffer8,i=this._buffer32,s=(t>>2)+1;this._dataLength+=\nt;let o=this._dataLength*8;if(n[t]=128,n[t+1]=n[t+2]=n[t+3]=0,i.set(U.buffer32Identity.\nsubarray(s),s),t>55&&(U._md5cycle(this._state,i),i.set(U.buffer32Identity)),o<=4294967295)\ni[14]=o;else{let u=o.toString(16).match(/(.*?)(.{0,8})$/);if(u===null)return;let c=parseInt(\nu[2],16),h=parseInt(u[1],16)||0;i[14]=c,i[15]=h}return U._md5cycle(this._state,i),\ne?this._state:U._hex(this._state)}};a(U,\"Md5\"),_(U,\"stateIdentity\",new Int32Array(\n[1732584193,-271733879,-1732584194,271733878])),_(U,\"buffer32Identity\",new Int32Array(\n[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0])),_(U,\"hexChars\",\"0123456789abcdef\"),_(U,\"hexO\\\nut\",[]),_(U,\"onePassHasher\",new U);$e=U});var Ot={};ie(Ot,{createHash:()=>Vo,createHmac:()=>Ko,randomBytes:()=>$o});function $o(r){\nreturn g.getRandomValues(d.alloc(r))}function Vo(r){if(r===\"sha256\")return{update:a(\nfunction(e){return{digest:a(function(){return d.from(Ge(e))},\"digest\")}},\"update\")};\nif(r===\"md5\")return{update:a(function(e){return{digest:a(function(){return typeof e==\n\"string\"?$e.hashStr(e):$e.hashByteArray(e)},\"digest\")}},\"update\")};throw new Error(\n`Hash type '${r}' not supported`)}function Ko(r,e){if(r!==\"sha256\")throw new Error(\n`Only sha256 is supported (requested: '${r}')`);return{update:a(function(t){return{\ndigest:a(function(){typeof e==\"string\"&&(e=new TextEncoder().encode(e)),typeof t==\n\"string\"&&(t=new TextEncoder().encode(t));let n=e.length;if(n>64)e=Ge(e);else if(n<\n64){let c=new Uint8Array(64);c.set(e),e=c}let i=new Uint8Array(64),s=new Uint8Array(\n64);for(let c=0;c<64;c++)i[c]=54^e[c],s[c]=92^e[c];let o=new Uint8Array(t.length+\n64);o.set(i,0),o.set(t,64);let u=new Uint8Array(96);return u.set(s,0),u.set(Ge(o),\n64),d.from(Ge(u))},\"digest\")}},\"update\")}}var Nt=z(()=>{\"use strict\";p();ni();ii();\na($o,\"randomBytes\");a(Vo,\"createHash\");a(Ko,\"createHmac\")});var Qt=T(si=>{\"use strict\";p();si.parse=function(r,e){return new qt(r,e).parse()};\nvar ut=class ut{constructor(e,t){this.source=e,this.transform=t||zo,this.position=\n0,this.entries=[],this.recorded=[],this.dimension=0}isEof(){return this.position>=\nthis.source.length}nextCharacter(){var e=this.source[this.position++];return e===\n\"\\\\\"?{value:this.source[this.position++],escaped:!0}:{value:e,escaped:!1}}record(e){\nthis.recorded.push(e)}newEntry(e){var t;(this.recorded.length>0||e)&&(t=this.recorded.\njoin(\"\"),t===\"NULL\"&&!e&&(t=null),t!==null&&(t=this.transform(t)),this.entries.push(\nt),this.recorded=[])}consumeDimensions(){if(this.source[0]===\"[\")for(;!this.isEof();){\nvar e=this.nextCharacter();if(e.value===\"=\")break}}parse(e){var t,n,i;for(this.consumeDimensions();!this.\nisEof();)if(t=this.nextCharacter(),t.value===\"{\"&&!i)this.dimension++,this.dimension>\n1&&(n=new ut(this.source.substr(this.position-1),this.transform),this.entries.push(\nn.parse(!0)),this.position+=n.position-2);else if(t.value===\"}\"&&!i){if(this.dimension--,\n!this.dimension&&(this.newEntry(),e))return this.entries}else t.value==='\"'&&!t.\nescaped?(i&&this.newEntry(!0),i=!i):t.value===\",\"&&!i?this.newEntry():this.record(\nt.value);if(this.dimension!==0)throw new Error(\"array dimension not balanced\");return this.\nentries}};a(ut,\"ArrayParser\");var qt=ut;function zo(r){return r}a(zo,\"identity\")});var Wt=T((yh,oi)=>{p();var Yo=Qt();oi.exports={create:a(function(r,e){return{parse:a(\nfunction(){return Yo.parse(r,e)},\"parse\")}},\"create\")}});var ci=T((wh,ui)=>{\"use strict\";p();var Zo=/(\\d{1,})-(\\d{2})-(\\d{2}) (\\d{2}):(\\d{2}):(\\d{2})(\\.\\d{1,})?.*?( BC)?$/,\nJo=/^(\\d{1,})-(\\d{2})-(\\d{2})( BC)?$/,Xo=/([Z+-])(\\d{2})?:?(\\d{2})?:?(\\d{2})?/,ea=/^-?infinity$/;\nui.exports=a(function(e){if(ea.test(e))return Number(e.replace(\"i\",\"I\"));var t=Zo.\nexec(e);if(!t)return ta(e)||null;var n=!!t[8],i=parseInt(t[1],10);n&&(i=ai(i));var s=parseInt(\nt[2],10)-1,o=t[3],u=parseInt(t[4],10),c=parseInt(t[5],10),h=parseInt(t[6],10),l=t[7];\nl=l?1e3*parseFloat(l):0;var y,x=ra(e);return x!=null?(y=new Date(Date.UTC(i,s,o,\nu,c,h,l)),jt(i)&&y.setUTCFullYear(i),x!==0&&y.setTime(y.getTime()-x)):(y=new Date(\ni,s,o,u,c,h,l),jt(i)&&y.setFullYear(i)),y},\"parseDate\");function ta(r){var e=Jo.\nexec(r);if(e){var t=parseInt(e[1],10),n=!!e[4];n&&(t=ai(t));var i=parseInt(e[2],\n10)-1,s=e[3],o=new Date(t,i,s);return jt(t)&&o.setFullYear(t),o}}a(ta,\"getDate\");\nfunction ra(r){if(r.endsWith(\"+00\"))return 0;var e=Xo.exec(r.split(\" \")[1]);if(e){\nvar t=e[1];if(t===\"Z\")return 0;var n=t===\"-\"?-1:1,i=parseInt(e[2],10)*3600+parseInt(\ne[3]||0,10)*60+parseInt(e[4]||0,10);return i*n*1e3}}a(ra,\"timeZoneOffset\");function ai(r){\nreturn-(r-1)}a(ai,\"bcYearToNegativeYear\");function jt(r){return r>=0&&r<100}a(jt,\n\"is0To99\")});var li=T((xh,hi)=>{p();hi.exports=ia;var na=Object.prototype.hasOwnProperty;function ia(r){\nfor(var e=1;e<arguments.length;e++){var t=arguments[e];for(var n in t)na.call(t,\nn)&&(r[n]=t[n])}return r}a(ia,\"extend\")});var di=T((_h,pi)=>{\"use strict\";p();var sa=li();pi.exports=Fe;function Fe(r){if(!(this instanceof\nFe))return new Fe(r);sa(this,ga(r))}a(Fe,\"PostgresInterval\");var oa=[\"seconds\",\"\\\nminutes\",\"hours\",\"days\",\"months\",\"years\"];Fe.prototype.toPostgres=function(){var r=oa.\nfilter(this.hasOwnProperty,this);return this.milliseconds&&r.indexOf(\"seconds\")<\n0&&r.push(\"seconds\"),r.length===0?\"0\":r.map(function(e){var t=this[e]||0;return e===\n\"seconds\"&&this.milliseconds&&(t=(t+this.milliseconds/1e3).toFixed(6).replace(/\\.?0+$/,\n\"\")),t+\" \"+e},this).join(\" \")};var aa={years:\"Y\",months:\"M\",days:\"D\",hours:\"H\",minutes:\"\\\nM\",seconds:\"S\"},ua=[\"years\",\"months\",\"days\"],ca=[\"hours\",\"minutes\",\"seconds\"];Fe.\nprototype.toISOString=Fe.prototype.toISO=function(){var r=ua.map(t,this).join(\"\"),\ne=ca.map(t,this).join(\"\");return\"P\"+r+\"T\"+e;function t(n){var i=this[n]||0;return n===\n\"seconds\"&&this.milliseconds&&(i=(i+this.milliseconds/1e3).toFixed(6).replace(/0+$/,\n\"\")),i+aa[n]}};var Ht=\"([+-]?\\\\d+)\",ha=Ht+\"\\\\s+years?\",la=Ht+\"\\\\s+mons?\",fa=Ht+\"\\\n\\\\s+days?\",pa=\"([+-])?([\\\\d]*):(\\\\d\\\\d):(\\\\d\\\\d)\\\\.?(\\\\d{1,6})?\",da=new RegExp([\nha,la,fa,pa].map(function(r){return\"(\"+r+\")?\"}).join(\"\\\\s*\")),fi={years:2,months:4,\ndays:6,hours:9,minutes:10,seconds:11,milliseconds:12},ya=[\"hours\",\"minutes\",\"sec\\\nonds\",\"milliseconds\"];function ma(r){var e=r+\"000000\".slice(r.length);return parseInt(\ne,10)/1e3}a(ma,\"parseMilliseconds\");function ga(r){if(!r)return{};var e=da.exec(\nr),t=e[8]===\"-\";return Object.keys(fi).reduce(function(n,i){var s=fi[i],o=e[s];return!o||\n(o=i===\"milliseconds\"?ma(o):parseInt(o,10),!o)||(t&&~ya.indexOf(i)&&(o*=-1),n[i]=\no),n},{})}a(ga,\"parse\")});var mi=T((Ih,yi)=>{\"use strict\";p();yi.exports=a(function(e){if(/^\\\\x/.test(e))return new d(\ne.substr(2),\"hex\");for(var t=\"\",n=0;n<e.length;)if(e[n]!==\"\\\\\")t+=e[n],++n;else if(/[0-7]{3}/.\ntest(e.substr(n+1,3)))t+=String.fromCharCode(parseInt(e.substr(n+1,3),8)),n+=4;else{\nfor(var i=1;n+i<e.length&&e[n+i]===\"\\\\\";)i++;for(var s=0;s<Math.floor(i/2);++s)t+=\n\"\\\\\";n+=Math.floor(i/2)*2}return new d(t,\"binary\")},\"parseBytea\")});var Ei=T((Bh,vi)=>{p();var Ve=Qt(),Ke=Wt(),ct=ci(),wi=di(),bi=mi();function ht(r){\nreturn a(function(t){return t===null?t:r(t)},\"nullAllowed\")}a(ht,\"allowNull\");function Si(r){\nreturn r===null?r:r===\"TRUE\"||r===\"t\"||r===\"true\"||r===\"y\"||r===\"yes\"||r===\"on\"||\nr===\"1\"}a(Si,\"parseBool\");function wa(r){return r?Ve.parse(r,Si):null}a(wa,\"pars\\\neBoolArray\");function ba(r){return parseInt(r,10)}a(ba,\"parseBaseTenInt\");function Gt(r){\nreturn r?Ve.parse(r,ht(ba)):null}a(Gt,\"parseIntegerArray\");function Sa(r){return r?\nVe.parse(r,ht(function(e){return xi(e).trim()})):null}a(Sa,\"parseBigIntegerArray\");\nvar xa=a(function(r){if(!r)return null;var e=Ke.create(r,function(t){return t!==\nnull&&(t=zt(t)),t});return e.parse()},\"parsePointArray\"),$t=a(function(r){if(!r)\nreturn null;var e=Ke.create(r,function(t){return t!==null&&(t=parseFloat(t)),t});\nreturn e.parse()},\"parseFloatArray\"),re=a(function(r){if(!r)return null;var e=Ke.\ncreate(r);return e.parse()},\"parseStringArray\"),Vt=a(function(r){if(!r)return null;\nvar e=Ke.create(r,function(t){return t!==null&&(t=ct(t)),t});return e.parse()},\"\\\nparseDateArray\"),va=a(function(r){if(!r)return null;var e=Ke.create(r,function(t){\nreturn t!==null&&(t=wi(t)),t});return e.parse()},\"parseIntervalArray\"),Ea=a(function(r){\nreturn r?Ve.parse(r,ht(bi)):null},\"parseByteAArray\"),Kt=a(function(r){return parseInt(\nr,10)},\"parseInteger\"),xi=a(function(r){var e=String(r);return/^\\d+$/.test(e)?e:\nr},\"parseBigInteger\"),gi=a(function(r){return r?Ve.parse(r,ht(JSON.parse)):null},\n\"parseJsonArray\"),zt=a(function(r){return r[0]!==\"(\"?null:(r=r.substring(1,r.length-\n1).split(\",\"),{x:parseFloat(r[0]),y:parseFloat(r[1])})},\"parsePoint\"),_a=a(function(r){\nif(r[0]!==\"<\"&&r[1]!==\"(\")return null;for(var e=\"(\",t=\"\",n=!1,i=2;i<r.length-1;i++){\nif(n||(e+=r[i]),r[i]===\")\"){n=!0;continue}else if(!n)continue;r[i]!==\",\"&&(t+=r[i])}\nvar s=zt(e);return s.radius=parseFloat(t),s},\"parseCircle\"),Aa=a(function(r){r(20,\nxi),r(21,Kt),r(23,Kt),r(26,Kt),r(700,parseFloat),r(701,parseFloat),r(16,Si),r(1082,\nct),r(1114,ct),r(1184,ct),r(600,zt),r(651,re),r(718,_a),r(1e3,wa),r(1001,Ea),r(1005,\nGt),r(1007,Gt),r(1028,Gt),r(1016,Sa),r(1017,xa),r(1021,$t),r(1022,$t),r(1231,$t),\nr(1014,re),r(1015,re),r(1008,re),r(1009,re),r(1040,re),r(1041,re),r(1115,Vt),r(1182,\nVt),r(1185,Vt),r(1186,wi),r(1187,va),r(17,bi),r(114,JSON.parse.bind(JSON)),r(3802,\nJSON.parse.bind(JSON)),r(199,gi),r(3807,gi),r(3907,re),r(2951,re),r(791,re),r(1183,\nre),r(1270,re)},\"init\");vi.exports={init:Aa}});var Ai=T((Fh,_i)=>{\"use strict\";p();var Z=1e6;function Ca(r){var e=r.readInt32BE(\n0),t=r.readUInt32BE(4),n=\"\";e<0&&(e=~e+(t===0),t=~t+1>>>0,n=\"-\");var i=\"\",s,o,u,\nc,h,l;{if(s=e%Z,e=e/Z>>>0,o=4294967296*s+t,t=o/Z>>>0,u=\"\"+(o-Z*t),t===0&&e===0)return n+\nu+i;for(c=\"\",h=6-u.length,l=0;l<h;l++)c+=\"0\";i=c+u+i}{if(s=e%Z,e=e/Z>>>0,o=4294967296*\ns+t,t=o/Z>>>0,u=\"\"+(o-Z*t),t===0&&e===0)return n+u+i;for(c=\"\",h=6-u.length,l=0;l<\nh;l++)c+=\"0\";i=c+u+i}{if(s=e%Z,e=e/Z>>>0,o=4294967296*s+t,t=o/Z>>>0,u=\"\"+(o-Z*t),\nt===0&&e===0)return n+u+i;for(c=\"\",h=6-u.length,l=0;l<h;l++)c+=\"0\";i=c+u+i}return s=\ne%Z,o=4294967296*s+t,u=\"\"+o%Z,n+u+i}a(Ca,\"readInt8\");_i.exports=Ca});var Bi=T((kh,Pi)=>{p();var Ia=Ai(),F=a(function(r,e,t,n,i){t=t||0,n=n||!1,i=i||function(C,B,W){\nreturn C*Math.pow(2,W)+B};var s=t>>3,o=a(function(C){return n?~C&255:C},\"inv\"),u=255,\nc=8-t%8;e<c&&(u=255<<8-e&255,c=e),t&&(u=u>>t%8);var h=0;t%8+e>=8&&(h=i(0,o(r[s])&\nu,c));for(var l=e+t>>3,y=s+1;y<l;y++)h=i(h,o(r[y]),8);var x=(e+t)%8;return x>0&&\n(h=i(h,o(r[l])>>8-x,x)),h},\"parseBits\"),Ti=a(function(r,e,t){var n=Math.pow(2,t-\n1)-1,i=F(r,1),s=F(r,t,1);if(s===0)return 0;var o=1,u=a(function(h,l,y){h===0&&(h=\n1);for(var x=1;x<=y;x++)o/=2,(l&1<<y-x)>0&&(h+=o);return h},\"parsePrecisionBits\"),\nc=F(r,e,t+1,!1,u);return s==Math.pow(2,t+1)-1?c===0?i===0?1/0:-1/0:NaN:(i===0?1:\n-1)*Math.pow(2,s-n)*c},\"parseFloatFromBits\"),Ta=a(function(r){return F(r,1)==1?-1*\n(F(r,15,1,!0)+1):F(r,15,1)},\"parseInt16\"),Ci=a(function(r){return F(r,1)==1?-1*(F(\nr,31,1,!0)+1):F(r,31,1)},\"parseInt32\"),Pa=a(function(r){return Ti(r,23,8)},\"pars\\\neFloat32\"),Ba=a(function(r){return Ti(r,52,11)},\"parseFloat64\"),La=a(function(r){\nvar e=F(r,16,32);if(e==49152)return NaN;for(var t=Math.pow(1e4,F(r,16,16)),n=0,i=[],\ns=F(r,16),o=0;o<s;o++)n+=F(r,16,64+16*o)*t,t/=1e4;var u=Math.pow(10,F(r,16,48));\nreturn(e===0?1:-1)*Math.round(n*u)/u},\"parseNumeric\"),Ii=a(function(r,e){var t=F(\ne,1),n=F(e,63,1),i=new Date((t===0?1:-1)*n/1e3+9466848e5);return r||i.setTime(i.\ngetTime()+i.getTimezoneOffset()*6e4),i.usec=n%1e3,i.getMicroSeconds=function(){return this.\nusec},i.setMicroSeconds=function(s){this.usec=s},i.getUTCMicroSeconds=function(){\nreturn this.usec},i},\"parseDate\"),ze=a(function(r){for(var e=F(r,32),t=F(r,32,32),\nn=F(r,32,64),i=96,s=[],o=0;o<e;o++)s[o]=F(r,32,i),i+=32,i+=32;var u=a(function(h){\nvar l=F(r,32,i);if(i+=32,l==4294967295)return null;var y;if(h==23||h==20)return y=\nF(r,l*8,i),i+=l*8,y;if(h==25)return y=r.toString(this.encoding,i>>3,(i+=l<<3)>>3),\ny;console.log(\"ERROR: ElementType not implemented: \"+h)},\"parseElement\"),c=a(function(h,l){\nvar y=[],x;if(h.length>1){var C=h.shift();for(x=0;x<C;x++)y[x]=c(h,l);h.unshift(\nC)}else for(x=0;x<h[0];x++)y[x]=u(l);return y},\"parse\");return c(s,n)},\"parseArr\\\nay\"),Ra=a(function(r){return r.toString(\"utf8\")},\"parseText\"),Fa=a(function(r){return r===\nnull?null:F(r,8)>0},\"parseBool\"),Ma=a(function(r){r(20,Ia),r(21,Ta),r(23,Ci),r(26,\nCi),r(1700,La),r(700,Pa),r(701,Ba),r(16,Fa),r(1114,Ii.bind(null,!1)),r(1184,Ii.bind(\nnull,!0)),r(1e3,ze),r(1007,ze),r(1016,ze),r(1008,ze),r(1009,ze),r(25,Ra)},\"init\");\nPi.exports={init:Ma}});var Ri=T((Nh,Li)=>{p();Li.exports={BOOL:16,BYTEA:17,CHAR:18,INT8:20,INT2:21,INT4:23,\nREGPROC:24,TEXT:25,OID:26,TID:27,XID:28,CID:29,JSON:114,XML:142,PG_NODE_TREE:194,\nSMGR:210,PATH:602,POLYGON:604,CIDR:650,FLOAT4:700,FLOAT8:701,ABSTIME:702,RELTIME:703,\nTINTERVAL:704,CIRCLE:718,MACADDR8:774,MONEY:790,MACADDR:829,INET:869,ACLITEM:1033,\nBPCHAR:1042,VARCHAR:1043,DATE:1082,TIME:1083,TIMESTAMP:1114,TIMESTAMPTZ:1184,INTERVAL:1186,\nTIMETZ:1266,BIT:1560,VARBIT:1562,NUMERIC:1700,REFCURSOR:1790,REGPROCEDURE:2202,REGOPER:2203,\nREGOPERATOR:2204,REGCLASS:2205,REGTYPE:2206,UUID:2950,TXID_SNAPSHOT:2970,PG_LSN:3220,\nPG_NDISTINCT:3361,PG_DEPENDENCIES:3402,TSVECTOR:3614,TSQUERY:3615,GTSVECTOR:3642,\nREGCONFIG:3734,REGDICTIONARY:3769,JSONB:3802,REGNAMESPACE:4089,REGROLE:4096}});var Je=T(Ze=>{p();var Da=Ei(),ka=Bi(),Ua=Wt(),Oa=Ri();Ze.getTypeParser=Na;Ze.setTypeParser=\nqa;Ze.arrayParser=Ua;Ze.builtins=Oa;var Ye={text:{},binary:{}};function Fi(r){return String(\nr)}a(Fi,\"noParse\");function Na(r,e){return e=e||\"text\",Ye[e]&&Ye[e][r]||Fi}a(Na,\n\"getTypeParser\");function qa(r,e,t){typeof e==\"function\"&&(t=e,e=\"text\"),Ye[e][r]=\nt}a(qa,\"setTypeParser\");Da.init(function(r,e){Ye.text[r]=e});ka.init(function(r,e){\nYe.binary[r]=e})});var Xe=T((Hh,Yt)=>{\"use strict\";p();Yt.exports={host:\"localhost\",user:m.platform===\n\"win32\"?m.env.USERNAME:m.env.USER,database:void 0,password:null,connectionString:void 0,\nport:5432,rows:0,binary:!1,max:10,idleTimeoutMillis:3e4,client_encoding:\"\",ssl:!1,\napplication_name:void 0,fallback_application_name:void 0,options:void 0,parseInputDatesAsUTC:!1,\nstatement_timeout:!1,lock_timeout:!1,idle_in_transaction_session_timeout:!1,query_timeout:!1,\nconnect_timeout:0,keepalives:1,keepalives_idle:0};var Me=Je(),Qa=Me.getTypeParser(\n20,\"text\"),Wa=Me.getTypeParser(1016,\"text\");Yt.exports.__defineSetter__(\"parseIn\\\nt8\",function(r){Me.setTypeParser(20,\"text\",r?Me.getTypeParser(23,\"text\"):Qa),Me.\nsetTypeParser(1016,\"text\",r?Me.getTypeParser(1007,\"text\"):Wa)})});var et=T(($h,Di)=>{\"use strict\";p();var ja=(Nt(),N(Ot)),Ha=Xe();function Ga(r){var e=r.\nreplace(/\\\\/g,\"\\\\\\\\\").replace(/\"/g,'\\\\\"');return'\"'+e+'\"'}a(Ga,\"escapeElement\");\nfunction Mi(r){for(var e=\"{\",t=0;t<r.length;t++)t>0&&(e=e+\",\"),r[t]===null||typeof r[t]>\n\"u\"?e=e+\"NULL\":Array.isArray(r[t])?e=e+Mi(r[t]):r[t]instanceof d?e+=\"\\\\\\\\x\"+r[t].\ntoString(\"hex\"):e+=Ga(lt(r[t]));return e=e+\"}\",e}a(Mi,\"arrayString\");var lt=a(function(r,e){\nif(r==null)return null;if(r instanceof d)return r;if(ArrayBuffer.isView(r)){var t=d.\nfrom(r.buffer,r.byteOffset,r.byteLength);return t.length===r.byteLength?t:t.slice(\nr.byteOffset,r.byteOffset+r.byteLength)}return r instanceof Date?Ha.parseInputDatesAsUTC?\nKa(r):Va(r):Array.isArray(r)?Mi(r):typeof r==\"object\"?$a(r,e):r.toString()},\"pre\\\npareValue\");function $a(r,e){if(r&&typeof r.toPostgres==\"function\"){if(e=e||[],e.\nindexOf(r)!==-1)throw new Error('circular reference detected while preparing \"'+\nr+'\" for query');return e.push(r),lt(r.toPostgres(lt),e)}return JSON.stringify(r)}\na($a,\"prepareObject\");function H(r,e){for(r=\"\"+r;r.length<e;)r=\"0\"+r;return r}a(\nH,\"pad\");function Va(r){var e=-r.getTimezoneOffset(),t=r.getFullYear(),n=t<1;n&&\n(t=Math.abs(t)+1);var i=H(t,4)+\"-\"+H(r.getMonth()+1,2)+\"-\"+H(r.getDate(),2)+\"T\"+\nH(r.getHours(),2)+\":\"+H(r.getMinutes(),2)+\":\"+H(r.getSeconds(),2)+\".\"+H(r.getMilliseconds(),\n3);return e<0?(i+=\"-\",e*=-1):i+=\"+\",i+=H(Math.floor(e/60),2)+\":\"+H(e%60,2),n&&(i+=\n\" BC\"),i}a(Va,\"dateToString\");function Ka(r){var e=r.getUTCFullYear(),t=e<1;t&&(e=\nMath.abs(e)+1);var n=H(e,4)+\"-\"+H(r.getUTCMonth()+1,2)+\"-\"+H(r.getUTCDate(),2)+\"\\\nT\"+H(r.getUTCHours(),2)+\":\"+H(r.getUTCMinutes(),2)+\":\"+H(r.getUTCSeconds(),2)+\".\"+\nH(r.getUTCMilliseconds(),3);return n+=\"+00:00\",t&&(n+=\" BC\"),n}a(Ka,\"dateToStrin\\\ngUTC\");function za(r,e,t){return r=typeof r==\"string\"?{text:r}:r,e&&(typeof e==\"\\\nfunction\"?r.callback=e:r.values=e),t&&(r.callback=t),r}a(za,\"normalizeQueryConfi\\\ng\");var Zt=a(function(r){return ja.createHash(\"md5\").update(r,\"utf-8\").digest(\"h\\\nex\")},\"md5\"),Ya=a(function(r,e,t){var n=Zt(e+r),i=Zt(d.concat([d.from(n),t]));return\"\\\nmd5\"+i},\"postgresMd5PasswordHash\");Di.exports={prepareValue:a(function(e){return lt(\ne)},\"prepareValueWrapper\"),normalizeQueryConfig:za,postgresMd5PasswordHash:Ya,md5:Zt}});var qi=T((zh,Ni)=>{\"use strict\";p();var Jt=(Nt(),N(Ot));function Za(r){if(r.indexOf(\n\"SCRAM-SHA-256\")===-1)throw new Error(\"SASL: Only mechanism SCRAM-SHA-256 is cur\\\nrently supported\");let e=Jt.randomBytes(18).toString(\"base64\");return{mechanism:\"\\\nSCRAM-SHA-256\",clientNonce:e,response:\"n,,n=*,r=\"+e,message:\"SASLInitialResponse\"}}\na(Za,\"startSession\");function Ja(r,e,t){if(r.message!==\"SASLInitialResponse\")throw new Error(\n\"SASL: Last message was not SASLInitialResponse\");if(typeof e!=\"string\")throw new Error(\n\"SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\");if(typeof t!=\n\"string\")throw new Error(\"SASL: SCRAM-SERVER-FIRST-MESSAGE: serverData must be a\\\n string\");let n=tu(t);if(n.nonce.startsWith(r.clientNonce)){if(n.nonce.length===\nr.clientNonce.length)throw new Error(\"SASL: SCRAM-SERVER-FIRST-MESSAGE: server n\\\nonce is too short\")}else throw new Error(\"SASL: SCRAM-SERVER-FIRST-MESSAGE: serv\\\ner nonce does not start with client nonce\");var i=d.from(n.salt,\"base64\"),s=iu(e,\ni,n.iteration),o=De(s,\"Client Key\"),u=nu(o),c=\"n=*,r=\"+r.clientNonce,h=\"r=\"+n.nonce+\n\",s=\"+n.salt+\",i=\"+n.iteration,l=\"c=biws,r=\"+n.nonce,y=c+\",\"+h+\",\"+l,x=De(u,y),C=Oi(\no,x),B=C.toString(\"base64\"),W=De(s,\"Server Key\"),X=De(W,y);r.message=\"SASLRespon\\\nse\",r.serverSignature=X.toString(\"base64\"),r.response=l+\",p=\"+B}a(Ja,\"continueSe\\\nssion\");function Xa(r,e){if(r.message!==\"SASLResponse\")throw new Error(\"SASL: La\\\nst message was not SASLResponse\");if(typeof e!=\"string\")throw new Error(\"SASL: S\\\nCRAM-SERVER-FINAL-MESSAGE: serverData must be a string\");let{serverSignature:t}=ru(\ne);if(t!==r.serverSignature)throw new Error(\"SASL: SCRAM-SERVER-FINAL-MESSAGE: s\\\nerver signature does not match\")}a(Xa,\"finalizeSession\");function eu(r){if(typeof r!=\n\"string\")throw new TypeError(\"SASL: text must be a string\");return r.split(\"\").map(\n(e,t)=>r.charCodeAt(t)).every(e=>e>=33&&e<=43||e>=45&&e<=126)}a(eu,\"isPrintableC\\\nhars\");function ki(r){return/^(?:[a-zA-Z0-9+/]{4})*(?:[a-zA-Z0-9+/]{2}==|[a-zA-Z0-9+/]{3}=)?$/.\ntest(r)}a(ki,\"isBase64\");function Ui(r){if(typeof r!=\"string\")throw new TypeError(\n\"SASL: attribute pairs text must be a string\");return new Map(r.split(\",\").map(e=>{\nif(!/^.=/.test(e))throw new Error(\"SASL: Invalid attribute pair entry\");let t=e[0],\nn=e.substring(2);return[t,n]}))}a(Ui,\"parseAttributePairs\");function tu(r){let e=Ui(\nr),t=e.get(\"r\");if(t){if(!eu(t))throw new Error(\"SASL: SCRAM-SERVER-FIRST-MESSAG\\\nE: nonce must only contain printable characters\")}else throw new Error(\"SASL: SC\\\nRAM-SERVER-FIRST-MESSAGE: nonce missing\");let n=e.get(\"s\");if(n){if(!ki(n))throw new Error(\n\"SASL: SCRAM-SERVER-FIRST-MESSAGE: salt must be base64\")}else throw new Error(\"S\\\nASL: SCRAM-SERVER-FIRST-MESSAGE: salt missing\");let i=e.get(\"i\");if(i){if(!/^[1-9][0-9]*$/.\ntest(i))throw new Error(\"SASL: SCRAM-SERVER-FIRST-MESSAGE: invalid iteration cou\\\nnt\")}else throw new Error(\"SASL: SCRAM-SERVER-FIRST-MESSAGE: iteration missing\");\nlet s=parseInt(i,10);return{nonce:t,salt:n,iteration:s}}a(tu,\"parseServerFirstMe\\\nssage\");function ru(r){let t=Ui(r).get(\"v\");if(t){if(!ki(t))throw new Error(\"SAS\\\nL: SCRAM-SERVER-FINAL-MESSAGE: server signature must be base64\")}else throw new Error(\n\"SASL: SCRAM-SERVER-FINAL-MESSAGE: server signature is missing\");return{serverSignature:t}}\na(ru,\"parseServerFinalMessage\");function Oi(r,e){if(!d.isBuffer(r))throw new TypeError(\n\"first argument must be a Buffer\");if(!d.isBuffer(e))throw new TypeError(\"second\\\n argument must be a Buffer\");if(r.length!==e.length)throw new Error(\"Buffer leng\\\nths must match\");if(r.length===0)throw new Error(\"Buffers cannot be empty\");return d.\nfrom(r.map((t,n)=>r[n]^e[n]))}a(Oi,\"xorBuffers\");function nu(r){return Jt.createHash(\n\"sha256\").update(r).digest()}a(nu,\"sha256\");function De(r,e){return Jt.createHmac(\n\"sha256\",r).update(e).digest()}a(De,\"hmacSha256\");function iu(r,e,t){for(var n=De(\nr,d.concat([e,d.from([0,0,0,1])])),i=n,s=0;s<t-1;s++)n=De(r,n),i=Oi(i,n);return i}\na(iu,\"Hi\");Ni.exports={startSession:Za,continueSession:Ja,finalizeSession:Xa}});var Xt={};ie(Xt,{join:()=>su});function su(...r){return r.join(\"/\")}var er=z(()=>{\n\"use strict\";p();a(su,\"join\")});var tr={};ie(tr,{stat:()=>ou});function ou(r,e){e(new Error(\"No filesystem\"))}var rr=z(\n()=>{\"use strict\";p();a(ou,\"stat\")});var nr={};ie(nr,{default:()=>au});var au,ir=z(()=>{\"use strict\";p();au={}});var Qi={};ie(Qi,{StringDecoder:()=>sr});var or,sr,Wi=z(()=>{\"use strict\";p();or=\nclass or{constructor(e){_(this,\"td\");this.td=new TextDecoder(e)}write(e){return this.\ntd.decode(e,{stream:!0})}end(e){return this.td.decode(e)}};a(or,\"StringDecoder\");\nsr=or});var $i=T((sl,Gi)=>{\"use strict\";p();var{Transform:uu}=(ir(),N(nr)),{StringDecoder:cu}=(Wi(),N(Qi)),\nbe=Symbol(\"last\"),ft=Symbol(\"decoder\");function hu(r,e,t){let n;if(this.overflow){\nif(n=this[ft].write(r).split(this.matcher),n.length===1)return t();n.shift(),this.\noverflow=!1}else this[be]+=this[ft].write(r),n=this[be].split(this.matcher);this[be]=\nn.pop();for(let i=0;i<n.length;i++)try{Hi(this,this.mapper(n[i]))}catch(s){return t(\ns)}if(this.overflow=this[be].length>this.maxLength,this.overflow&&!this.skipOverflow){\nt(new Error(\"maximum buffer reached\"));return}t()}a(hu,\"transform\");function lu(r){\nif(this[be]+=this[ft].end(),this[be])try{Hi(this,this.mapper(this[be]))}catch(e){\nreturn r(e)}r()}a(lu,\"flush\");function Hi(r,e){e!==void 0&&r.push(e)}a(Hi,\"push\");\nfunction ji(r){return r}a(ji,\"noop\");function fu(r,e,t){switch(r=r||/\\r?\\n/,e=e||\nji,t=t||{},arguments.length){case 1:typeof r==\"function\"?(e=r,r=/\\r?\\n/):typeof r==\n\"object\"&&!(r instanceof RegExp)&&!r[Symbol.split]&&(t=r,r=/\\r?\\n/);break;case 2:\ntypeof r==\"function\"?(t=e,e=r,r=/\\r?\\n/):typeof e==\"object\"&&(t=e,e=ji)}t=Object.\nassign({},t),t.autoDestroy=!0,t.transform=hu,t.flush=lu,t.readableObjectMode=!0;\nlet n=new uu(t);return n[be]=\"\",n[ft]=new cu(\"utf8\"),n.matcher=r,n.mapper=e,n.maxLength=\nt.maxLength,n.skipOverflow=t.skipOverflow||!1,n.overflow=!1,n._destroy=function(i,s){\nthis._writableState.errorEmitted=!1,s(i)},n}a(fu,\"split\");Gi.exports=fu});var zi=T((ul,pe)=>{\"use strict\";p();var Vi=(er(),N(Xt)),pu=(ir(),N(nr)).Stream,du=$i(),\nKi=(He(),N(je)),yu=5432,pt=m.platform===\"win32\",tt=m.stderr,mu=56,gu=7,wu=61440,\nbu=32768;function Su(r){return(r&wu)==bu}a(Su,\"isRegFile\");var ke=[\"host\",\"port\",\n\"database\",\"user\",\"password\"],ar=ke.length,xu=ke[ar-1];function ur(){var r=tt instanceof\npu&&tt.writable===!0;if(r){var e=Array.prototype.slice.call(arguments).concat(`\n`);tt.write(Ki.format.apply(Ki,e))}}a(ur,\"warn\");Object.defineProperty(pe.exports,\n\"isWin\",{get:a(function(){return pt},\"get\"),set:a(function(r){pt=r},\"set\")});pe.\nexports.warnTo=function(r){var e=tt;return tt=r,e};pe.exports.getFileName=function(r){\nvar e=r||m.env,t=e.PGPASSFILE||(pt?Vi.join(e.APPDATA||\"./\",\"postgresql\",\"pgpass.\\\nconf\"):Vi.join(e.HOME||\"./\",\".pgpass\"));return t};pe.exports.usePgPass=function(r,e){\nreturn Object.prototype.hasOwnProperty.call(m.env,\"PGPASSWORD\")?!1:pt?!0:(e=e||\"\\\n<unkn>\",Su(r.mode)?r.mode&(mu|gu)?(ur('WARNING: password file \"%s\" has group or \\\nworld access; permissions should be u=rw (0600) or less',e),!1):!0:(ur('WARNING:\\\n password file \"%s\" is not a plain file',e),!1))};var vu=pe.exports.match=function(r,e){\nreturn ke.slice(0,-1).reduce(function(t,n,i){return i==1&&Number(r[n]||yu)===Number(\ne[n])?t&&!0:t&&(e[n]===\"*\"||e[n]===r[n])},!0)};pe.exports.getPassword=function(r,e,t){\nvar n,i=e.pipe(du());function s(c){var h=Eu(c);h&&_u(h)&&vu(r,h)&&(n=h[xu],i.end())}\na(s,\"onLine\");var o=a(function(){e.destroy(),t(n)},\"onEnd\"),u=a(function(c){e.destroy(),\nur(\"WARNING: error on reading file: %s\",c),t(void 0)},\"onErr\");e.on(\"error\",u),i.\non(\"data\",s).on(\"end\",o).on(\"error\",u)};var Eu=pe.exports.parseLine=function(r){\nif(r.length<11||r.match(/^\\s+#/))return null;for(var e=\"\",t=\"\",n=0,i=0,s=0,o={},\nu=!1,c=a(function(l,y,x){var C=r.substring(y,x);Object.hasOwnProperty.call(m.env,\n\"PGPASS_NO_DEESCAPE\")||(C=C.replace(/\\\\([:\\\\])/g,\"$1\")),o[ke[l]]=C},\"addToObj\"),\nh=0;h<r.length-1;h+=1){if(e=r.charAt(h+1),t=r.charAt(h),u=n==ar-1,u){c(n,i);break}\nh>=0&&e==\":\"&&t!==\"\\\\\"&&(c(n,i,h+1),i=h+2,n+=1)}return o=Object.keys(o).length===\nar?o:null,o},_u=pe.exports.isValidEntry=function(r){for(var e={0:function(o){return o.\nlength>0},1:function(o){return o===\"*\"?!0:(o=Number(o),isFinite(o)&&o>0&&o<9007199254740992&&\nMath.floor(o)===o)},2:function(o){return o.length>0},3:function(o){return o.length>\n0},4:function(o){return o.length>0}},t=0;t<ke.length;t+=1){var n=e[t],i=r[ke[t]]||\n\"\",s=n(i);if(!s)return!1}return!0}});var Zi=T((fl,cr)=>{\"use strict\";p();var ll=(er(),N(Xt)),Yi=(rr(),N(tr)),dt=zi();\ncr.exports=function(r,e){var t=dt.getFileName();Yi.stat(t,function(n,i){if(n||!dt.\nusePgPass(i,t))return e(void 0);var s=Yi.createReadStream(t);dt.getPassword(r,s,\ne)})};cr.exports.warnTo=dt.warnTo});var hr=T((dl,Ji)=>{\"use strict\";p();var Au=Je();function yt(r){this._types=r||Au,\nthis.text={},this.binary={}}a(yt,\"TypeOverrides\");yt.prototype.getOverrides=function(r){\nswitch(r){case\"text\":return this.text;case\"binary\":return this.binary;default:return{}}};\nyt.prototype.setTypeParser=function(r,e,t){typeof e==\"function\"&&(t=e,e=\"text\"),\nthis.getOverrides(e)[r]=t};yt.prototype.getTypeParser=function(r,e){return e=e||\n\"text\",this.getOverrides(e)[r]||this._types.getTypeParser(r,e)};Ji.exports=yt});var Xi={};ie(Xi,{default:()=>Cu});var Cu,es=z(()=>{\"use strict\";p();Cu={}});var ts={};ie(ts,{parse:()=>lr});function lr(r,e=!1){let{protocol:t}=new URL(r),n=\"\\\nhttp:\"+r.substring(t.length),{username:i,password:s,host:o,hostname:u,port:c,pathname:h,\nsearch:l,searchParams:y,hash:x}=new URL(n);s=decodeURIComponent(s),i=decodeURIComponent(\ni),h=decodeURIComponent(h);let C=i+\":\"+s,B=e?Object.fromEntries(y.entries()):l;return{\nhref:r,protocol:t,auth:C,username:i,password:s,host:o,hostname:u,port:c,pathname:h,\nsearch:l,query:B,hash:x}}var fr=z(()=>{\"use strict\";p();a(lr,\"parse\")});var ns=T((Sl,rs)=>{\"use strict\";p();var Iu=(fr(),N(ts)),pr=(rr(),N(tr));function dr(r){\nif(r.charAt(0)===\"/\"){var t=r.split(\" \");return{host:t[0],database:t[1]}}var e=Iu.\nparse(/ |%[^a-f0-9]|%[a-f0-9][^a-f0-9]/i.test(r)?encodeURI(r).replace(/\\%25(\\d\\d)/g,\n\"%$1\"):r,!0),t=e.query;for(var n in t)Array.isArray(t[n])&&(t[n]=t[n][t[n].length-\n1]);var i=(e.auth||\":\").split(\":\");if(t.user=i[0],t.password=i.splice(1).join(\":\"),\nt.port=e.port,e.protocol==\"socket:\")return t.host=decodeURI(e.pathname),t.database=\ne.query.db,t.client_encoding=e.query.encoding,t;t.host||(t.host=e.hostname);var s=e.\npathname;if(!t.host&&s&&/^%2f/i.test(s)){var o=s.split(\"/\");t.host=decodeURIComponent(\no[0]),s=o.splice(1).join(\"/\")}switch(s&&s.charAt(0)===\"/\"&&(s=s.slice(1)||null),\nt.database=s&&decodeURI(s),(t.ssl===\"true\"||t.ssl===\"1\")&&(t.ssl=!0),t.ssl===\"0\"&&\n(t.ssl=!1),(t.sslcert||t.sslkey||t.sslrootcert||t.sslmode)&&(t.ssl={}),t.sslcert&&\n(t.ssl.cert=pr.readFileSync(t.sslcert).toString()),t.sslkey&&(t.ssl.key=pr.readFileSync(\nt.sslkey).toString()),t.sslrootcert&&(t.ssl.ca=pr.readFileSync(t.sslrootcert).toString()),\nt.sslmode){case\"disable\":{t.ssl=!1;break}case\"prefer\":case\"require\":case\"verify-\\\nca\":case\"verify-full\":break;case\"no-verify\":{t.ssl.rejectUnauthorized=!1;break}}\nreturn t}a(dr,\"parse\");rs.exports=dr;dr.parse=dr});var mt=T((El,os)=>{\"use strict\";p();var Tu=(es(),N(Xi)),ss=Xe(),is=ns().parse,$=a(\nfunction(r,e,t){return t===void 0?t=m.env[\"PG\"+r.toUpperCase()]:t===!1||(t=m.env[t]),\ne[r]||t||ss[r]},\"val\"),Pu=a(function(){switch(m.env.PGSSLMODE){case\"disable\":return!1;case\"\\\nprefer\":case\"require\":case\"verify-ca\":case\"verify-full\":return!0;case\"no-verify\":\nreturn{rejectUnauthorized:!1}}return ss.ssl},\"readSSLConfigFromEnvironment\"),Ue=a(\nfunction(r){return\"'\"+(\"\"+r).replace(/\\\\/g,\"\\\\\\\\\").replace(/'/g,\"\\\\'\")+\"'\"},\"quo\\\nteParamValue\"),ne=a(function(r,e,t){var n=e[t];n!=null&&r.push(t+\"=\"+Ue(n))},\"ad\\\nd\"),mr=class mr{constructor(e){e=typeof e==\"string\"?is(e):e||{},e.connectionString&&\n(e=Object.assign({},e,is(e.connectionString))),this.user=$(\"user\",e),this.database=\n$(\"database\",e),this.database===void 0&&(this.database=this.user),this.port=parseInt(\n$(\"port\",e),10),this.host=$(\"host\",e),Object.defineProperty(this,\"password\",{configurable:!0,\nenumerable:!1,writable:!0,value:$(\"password\",e)}),this.binary=$(\"binary\",e),this.\noptions=$(\"options\",e),this.ssl=typeof e.ssl>\"u\"?Pu():e.ssl,typeof this.ssl==\"st\\\nring\"&&this.ssl===\"true\"&&(this.ssl=!0),this.ssl===\"no-verify\"&&(this.ssl={rejectUnauthorized:!1}),\nthis.ssl&&this.ssl.key&&Object.defineProperty(this.ssl,\"key\",{enumerable:!1}),this.\nclient_encoding=$(\"client_encoding\",e),this.replication=$(\"replication\",e),this.\nisDomainSocket=!(this.host||\"\").indexOf(\"/\"),this.application_name=$(\"applicatio\\\nn_name\",e,\"PGAPPNAME\"),this.fallback_application_name=$(\"fallback_application_na\\\nme\",e,!1),this.statement_timeout=$(\"statement_timeout\",e,!1),this.lock_timeout=$(\n\"lock_timeout\",e,!1),this.idle_in_transaction_session_timeout=$(\"idle_in_transac\\\ntion_session_timeout\",e,!1),this.query_timeout=$(\"query_timeout\",e,!1),e.connectionTimeoutMillis===\nvoid 0?this.connect_timeout=m.env.PGCONNECT_TIMEOUT||0:this.connect_timeout=Math.\nfloor(e.connectionTimeoutMillis/1e3),e.keepAlive===!1?this.keepalives=0:e.keepAlive===\n!0&&(this.keepalives=1),typeof e.keepAliveInitialDelayMillis==\"number\"&&(this.keepalives_idle=\nMath.floor(e.keepAliveInitialDelayMillis/1e3))}getLibpqConnectionString(e){var t=[];\nne(t,this,\"user\"),ne(t,this,\"password\"),ne(t,this,\"port\"),ne(t,this,\"application\\\n_name\"),ne(t,this,\"fallback_application_name\"),ne(t,this,\"connect_timeout\"),ne(t,\nthis,\"options\");var n=typeof this.ssl==\"object\"?this.ssl:this.ssl?{sslmode:this.\nssl}:{};if(ne(t,n,\"sslmode\"),ne(t,n,\"sslca\"),ne(t,n,\"sslkey\"),ne(t,n,\"sslcert\"),\nne(t,n,\"sslrootcert\"),this.database&&t.push(\"dbname=\"+Ue(this.database)),this.replication&&\nt.push(\"replication=\"+Ue(this.replication)),this.host&&t.push(\"host=\"+Ue(this.host)),\nthis.isDomainSocket)return e(null,t.join(\" \"));this.client_encoding&&t.push(\"cli\\\nent_encoding=\"+Ue(this.client_encoding)),Tu.lookup(this.host,function(i,s){return i?\ne(i,null):(t.push(\"hostaddr=\"+Ue(s)),e(null,t.join(\" \")))})}};a(mr,\"ConnectionPa\\\nrameters\");var yr=mr;os.exports=yr});var cs=T((Cl,us)=>{\"use strict\";p();var Bu=Je(),as=/^([A-Za-z]+)(?: (\\d+))?(?: (\\d+))?/,\nwr=class wr{constructor(e,t){this.command=null,this.rowCount=null,this.oid=null,\nthis.rows=[],this.fields=[],this._parsers=void 0,this._types=t,this.RowCtor=null,\nthis.rowAsArray=e===\"array\",this.rowAsArray&&(this.parseRow=this._parseRowAsArray)}addCommandComplete(e){\nvar t;e.text?t=as.exec(e.text):t=as.exec(e.command),t&&(this.command=t[1],t[3]?(this.\noid=parseInt(t[2],10),this.rowCount=parseInt(t[3],10)):t[2]&&(this.rowCount=parseInt(\nt[2],10)))}_parseRowAsArray(e){for(var t=new Array(e.length),n=0,i=e.length;n<i;n++){\nvar s=e[n];s!==null?t[n]=this._parsers[n](s):t[n]=null}return t}parseRow(e){for(var t={},\nn=0,i=e.length;n<i;n++){var s=e[n],o=this.fields[n].name;s!==null?t[o]=this._parsers[n](\ns):t[o]=null}return t}addRow(e){this.rows.push(e)}addFields(e){this.fields=e,this.\nfields.length&&(this._parsers=new Array(e.length));for(var t=0;t<e.length;t++){var n=e[t];\nthis._types?this._parsers[t]=this._types.getTypeParser(n.dataTypeID,n.format||\"t\\\next\"):this._parsers[t]=Bu.getTypeParser(n.dataTypeID,n.format||\"text\")}}};a(wr,\"\\\nResult\");var gr=wr;us.exports=gr});var ps=T((Pl,fs)=>{\"use strict\";p();var{EventEmitter:Lu}=we(),hs=cs(),ls=et(),Sr=class Sr extends Lu{constructor(e,t,n){\nsuper(),e=ls.normalizeQueryConfig(e,t,n),this.text=e.text,this.values=e.values,this.\nrows=e.rows,this.types=e.types,this.name=e.name,this.binary=e.binary,this.portal=\ne.portal||\"\",this.callback=e.callback,this._rowMode=e.rowMode,m.domain&&e.callback&&\n(this.callback=m.domain.bind(e.callback)),this._result=new hs(this._rowMode,this.\ntypes),this._results=this._result,this.isPreparedStatement=!1,this._canceledDueToError=\n!1,this._promise=null}requiresPreparation(){return this.name||this.rows?!0:!this.\ntext||!this.values?!1:this.values.length>0}_checkForMultirow(){this._result.command&&\n(Array.isArray(this._results)||(this._results=[this._result]),this._result=new hs(\nthis._rowMode,this.types),this._results.push(this._result))}handleRowDescription(e){\nthis._checkForMultirow(),this._result.addFields(e.fields),this._accumulateRows=this.\ncallback||!this.listeners(\"row\").length}handleDataRow(e){let t;if(!this._canceledDueToError){\ntry{t=this._result.parseRow(e.fields)}catch(n){this._canceledDueToError=n;return}\nthis.emit(\"row\",t,this._result),this._accumulateRows&&this._result.addRow(t)}}handleCommandComplete(e,t){\nthis._checkForMultirow(),this._result.addCommandComplete(e),this.rows&&t.sync()}handleEmptyQuery(e){\nthis.rows&&e.sync()}handleError(e,t){if(this._canceledDueToError&&(e=this._canceledDueToError,\nthis._canceledDueToError=!1),this.callback)return this.callback(e);this.emit(\"er\\\nror\",e)}handleReadyForQuery(e){if(this._canceledDueToError)return this.handleError(\nthis._canceledDueToError,e);if(this.callback)try{this.callback(null,this._results)}catch(t){\nm.nextTick(()=>{throw t})}this.emit(\"end\",this._results)}submit(e){if(typeof this.\ntext!=\"string\"&&typeof this.name!=\"string\")return new Error(\"A query must have e\\\nither text or a name. Supplying neither is unsupported.\");let t=e.parsedStatements[this.\nname];return this.text&&t&&this.text!==t?new Error(`Prepared statements must be \\\nunique - '${this.name}' was used for a different statement`):this.values&&!Array.\nisArray(this.values)?new Error(\"Query values must be an array\"):(this.requiresPreparation()?\nthis.prepare(e):e.query(this.text),null)}hasBeenParsed(e){return this.name&&e.parsedStatements[this.\nname]}handlePortalSuspended(e){this._getRows(e,this.rows)}_getRows(e,t){e.execute(\n{portal:this.portal,rows:t}),t?e.flush():e.sync()}prepare(e){this.isPreparedStatement=\n!0,this.hasBeenParsed(e)||e.parse({text:this.text,name:this.name,types:this.types});\ntry{e.bind({portal:this.portal,statement:this.name,values:this.values,binary:this.\nbinary,valueMapper:ls.prepareValue})}catch(t){this.handleError(t,e);return}e.describe(\n{type:\"P\",name:this.portal||\"\"}),this._getRows(e,this.rows)}handleCopyInResponse(e){\ne.sendCopyFail(\"No source stream defined\")}handleCopyData(e,t){}};a(Sr,\"Query\");\nvar br=Sr;fs.exports=br});var ys={};ie(ys,{Socket:()=>Ae,isIP:()=>Ru});function Ru(r){return 0}var ds,Fu,E,\nAe,gt=z(()=>{\"use strict\";p();ds=Qe(we(),1);a(Ru,\"isIP\");Fu=a(r=>r.replace(/^[^.]+\\./,\n\"api.\"),\"transformHost\"),E=class E extends ds.EventEmitter{constructor(){super(...arguments);\n_(this,\"opts\",{});_(this,\"connecting\",!1);_(this,\"pending\",!0);_(this,\"writable\",\n!0);_(this,\"encrypted\",!1);_(this,\"authorized\",!1);_(this,\"destroyed\",!1);_(this,\n\"ws\",null);_(this,\"writeBuffer\");_(this,\"tlsState\",0);_(this,\"tlsRead\");_(this,\"\\\ntlsWrite\")}static get poolQueryViaFetch(){return E.opts.poolQueryViaFetch??E.defaults.\npoolQueryViaFetch}static set poolQueryViaFetch(t){E.opts.poolQueryViaFetch=t}static get fetchEndpoint(){\nreturn E.opts.fetchEndpoint??E.defaults.fetchEndpoint}static set fetchEndpoint(t){\nE.opts.fetchEndpoint=t}static get fetchConnectionCache(){return!0}static set fetchConnectionCache(t){\nconsole.warn(\"The `fetchConnectionCache` option is deprecated (now always `true`\\\n)\")}static get fetchFunction(){return E.opts.fetchFunction??E.defaults.fetchFunction}static set fetchFunction(t){\nE.opts.fetchFunction=t}static get webSocketConstructor(){return E.opts.webSocketConstructor??\nE.defaults.webSocketConstructor}static set webSocketConstructor(t){E.opts.webSocketConstructor=\nt}get webSocketConstructor(){return this.opts.webSocketConstructor??E.webSocketConstructor}set webSocketConstructor(t){\nthis.opts.webSocketConstructor=t}static get wsProxy(){return E.opts.wsProxy??E.defaults.\nwsProxy}static set wsProxy(t){E.opts.wsProxy=t}get wsProxy(){return this.opts.wsProxy??\nE.wsProxy}set wsProxy(t){this.opts.wsProxy=t}static get coalesceWrites(){return E.\nopts.coalesceWrites??E.defaults.coalesceWrites}static set coalesceWrites(t){E.opts.\ncoalesceWrites=t}get coalesceWrites(){return this.opts.coalesceWrites??E.coalesceWrites}set coalesceWrites(t){\nthis.opts.coalesceWrites=t}static get useSecureWebSocket(){return E.opts.useSecureWebSocket??\nE.defaults.useSecureWebSocket}static set useSecureWebSocket(t){E.opts.useSecureWebSocket=\nt}get useSecureWebSocket(){return this.opts.useSecureWebSocket??E.useSecureWebSocket}set useSecureWebSocket(t){\nthis.opts.useSecureWebSocket=t}static get forceDisablePgSSL(){return E.opts.forceDisablePgSSL??\nE.defaults.forceDisablePgSSL}static set forceDisablePgSSL(t){E.opts.forceDisablePgSSL=\nt}get forceDisablePgSSL(){return this.opts.forceDisablePgSSL??E.forceDisablePgSSL}set forceDisablePgSSL(t){\nthis.opts.forceDisablePgSSL=t}static get disableSNI(){return E.opts.disableSNI??\nE.defaults.disableSNI}static set disableSNI(t){E.opts.disableSNI=t}get disableSNI(){\nreturn this.opts.disableSNI??E.disableSNI}set disableSNI(t){this.opts.disableSNI=\nt}static get pipelineConnect(){return E.opts.pipelineConnect??E.defaults.pipelineConnect}static set pipelineConnect(t){\nE.opts.pipelineConnect=t}get pipelineConnect(){return this.opts.pipelineConnect??\nE.pipelineConnect}set pipelineConnect(t){this.opts.pipelineConnect=t}static get subtls(){\nreturn E.opts.subtls??E.defaults.subtls}static set subtls(t){E.opts.subtls=t}get subtls(){\nreturn this.opts.subtls??E.subtls}set subtls(t){this.opts.subtls=t}static get pipelineTLS(){\nreturn E.opts.pipelineTLS??E.defaults.pipelineTLS}static set pipelineTLS(t){E.opts.\npipelineTLS=t}get pipelineTLS(){return this.opts.pipelineTLS??E.pipelineTLS}set pipelineTLS(t){\nthis.opts.pipelineTLS=t}static get rootCerts(){return E.opts.rootCerts??E.defaults.\nrootCerts}static set rootCerts(t){E.opts.rootCerts=t}get rootCerts(){return this.\nopts.rootCerts??E.rootCerts}set rootCerts(t){this.opts.rootCerts=t}wsProxyAddrForHost(t,n){\nlet i=this.wsProxy;if(i===void 0)throw new Error(\"No WebSocket proxy is configur\\\ned. Please see https://github.com/neondatabase/serverless/blob/main/CONFIG.md#ws\\\nproxy-string--host-string-port-number--string--string\");return typeof i==\"functi\\\non\"?i(t,n):`${i}?address=${t}:${n}`}setNoDelay(){return this}setKeepAlive(){return this}ref(){\nreturn this}unref(){return this}connect(t,n,i){this.connecting=!0,i&&this.once(\"\\\nconnect\",i);let s=a(()=>{this.connecting=!1,this.pending=!1,this.emit(\"connect\"),\nthis.emit(\"ready\")},\"handleWebSocketOpen\"),o=a((c,h=!1)=>{c.binaryType=\"arraybuf\\\nfer\",c.addEventListener(\"error\",l=>{this.emit(\"error\",l),this.emit(\"close\")}),c.\naddEventListener(\"message\",l=>{if(this.tlsState===0){let y=d.from(l.data);this.emit(\n\"data\",y)}}),c.addEventListener(\"close\",()=>{this.emit(\"close\")}),h?s():c.addEventListener(\n\"open\",s)},\"configureWebSocket\"),u;try{u=this.wsProxyAddrForHost(n,typeof t==\"st\\\nring\"?parseInt(t,10):t)}catch(c){this.emit(\"error\",c),this.emit(\"close\");return}\ntry{let h=(this.useSecureWebSocket?\"wss:\":\"ws:\")+\"//\"+u;if(this.webSocketConstructor!==\nvoid 0)this.ws=new this.webSocketConstructor(h),o(this.ws);else try{this.ws=new WebSocket(\nh),o(this.ws)}catch{this.ws=new __unstable_WebSocket(h),o(this.ws)}}catch(c){let l=(this.\nuseSecureWebSocket?\"https:\":\"http:\")+\"//\"+u;fetch(l,{headers:{Upgrade:\"websocket\"}}).\nthen(y=>{if(this.ws=y.webSocket,this.ws==null)throw c;this.ws.accept(),o(this.ws,\n!0)}).catch(y=>{this.emit(\"error\",new Error(`All attempts to open a WebSocket to\\\n connect to the database failed. Please refer to https://github.com/neondatabase\\\n/serverless/blob/main/CONFIG.md#websocketconstructor-typeof-websocket--undefined\\\n. Details: ${y.message}`)),this.emit(\"close\")})}}async startTls(t){if(this.subtls===\nvoid 0)throw new Error(\"For Postgres SSL connections, you must set `neonConfig.s\\\nubtls` to the subtls library. See https://github.com/neondatabase/serverless/blo\\\nb/main/CONFIG.md for more information.\");this.tlsState=1;let n=this.subtls.TrustedCert.\nfromPEM(this.rootCerts),i=new this.subtls.WebSocketReadQueue(this.ws),s=i.read.bind(\ni),o=this.rawWrite.bind(this),[u,c]=await this.subtls.startTls(t,n,s,o,{useSNI:!this.\ndisableSNI,expectPreData:this.pipelineTLS?new Uint8Array([83]):void 0});this.tlsRead=\nu,this.tlsWrite=c,this.tlsState=2,this.encrypted=!0,this.authorized=!0,this.emit(\n\"secureConnection\",this),this.tlsReadLoop()}async tlsReadLoop(){for(;;){let t=await this.\ntlsRead();if(t===void 0)break;{let n=d.from(t);this.emit(\"data\",n)}}}rawWrite(t){\nif(!this.coalesceWrites){this.ws.send(t);return}if(this.writeBuffer===void 0)this.\nwriteBuffer=t,setTimeout(()=>{this.ws.send(this.writeBuffer),this.writeBuffer=void 0},\n0);else{let n=new Uint8Array(this.writeBuffer.length+t.length);n.set(this.writeBuffer),\nn.set(t,this.writeBuffer.length),this.writeBuffer=n}}write(t,n=\"utf8\",i=s=>{}){return t.\nlength===0?(i(),!0):(typeof t==\"string\"&&(t=d.from(t,n)),this.tlsState===0?(this.\nrawWrite(t),i()):this.tlsState===1?this.once(\"secureConnection\",()=>{this.write(\nt,n,i)}):(this.tlsWrite(t),i()),!0)}end(t=d.alloc(0),n=\"utf8\",i=()=>{}){return this.\nwrite(t,n,()=>{this.ws.close(),i()}),this}destroy(){return this.destroyed=!0,this.\nend()}};a(E,\"Socket\"),_(E,\"defaults\",{poolQueryViaFetch:!1,fetchEndpoint:a(t=>\"h\\\nttps://\"+Fu(t)+\"/sql\",\"fetchEndpoint\"),fetchConnectionCache:!0,fetchFunction:void 0,\nwebSocketConstructor:void 0,wsProxy:a(t=>t+\"/v2\",\"wsProxy\"),useSecureWebSocket:!0,\nforceDisablePgSSL:!0,coalesceWrites:!0,pipelineConnect:\"password\",subtls:void 0,\nrootCerts:\"\",pipelineTLS:!1,disableSNI:!1}),_(E,\"opts\",{});Ae=E});var zr=T(I=>{\"use strict\";p();Object.defineProperty(I,\"__esModule\",{value:!0});I.\nNoticeMessage=I.DataRowMessage=I.CommandCompleteMessage=I.ReadyForQueryMessage=I.\nNotificationResponseMessage=I.BackendKeyDataMessage=I.AuthenticationMD5Password=\nI.ParameterStatusMessage=I.ParameterDescriptionMessage=I.RowDescriptionMessage=I.\nField=I.CopyResponse=I.CopyDataMessage=I.DatabaseError=I.copyDone=I.emptyQuery=I.\nreplicationStart=I.portalSuspended=I.noData=I.closeComplete=I.bindComplete=I.parseComplete=\nvoid 0;I.parseComplete={name:\"parseComplete\",length:5};I.bindComplete={name:\"bin\\\ndComplete\",length:5};I.closeComplete={name:\"closeComplete\",length:5};I.noData={name:\"\\\nnoData\",length:5};I.portalSuspended={name:\"portalSuspended\",length:5};I.replicationStart=\n{name:\"replicationStart\",length:4};I.emptyQuery={name:\"emptyQuery\",length:4};I.copyDone=\n{name:\"copyDone\",length:4};var Dr=class Dr extends Error{constructor(e,t,n){super(\ne),this.length=t,this.name=n}};a(Dr,\"DatabaseError\");var xr=Dr;I.DatabaseError=xr;\nvar kr=class kr{constructor(e,t){this.length=e,this.chunk=t,this.name=\"copyData\"}};\na(kr,\"CopyDataMessage\");var vr=kr;I.CopyDataMessage=vr;var Ur=class Ur{constructor(e,t,n,i){\nthis.length=e,this.name=t,this.binary=n,this.columnTypes=new Array(i)}};a(Ur,\"Co\\\npyResponse\");var Er=Ur;I.CopyResponse=Er;var Or=class Or{constructor(e,t,n,i,s,o,u){\nthis.name=e,this.tableID=t,this.columnID=n,this.dataTypeID=i,this.dataTypeSize=s,\nthis.dataTypeModifier=o,this.format=u}};a(Or,\"Field\");var _r=Or;I.Field=_r;var Nr=class Nr{constructor(e,t){\nthis.length=e,this.fieldCount=t,this.name=\"rowDescription\",this.fields=new Array(\nthis.fieldCount)}};a(Nr,\"RowDescriptionMessage\");var Ar=Nr;I.RowDescriptionMessage=\nAr;var qr=class qr{constructor(e,t){this.length=e,this.parameterCount=t,this.name=\n\"parameterDescription\",this.dataTypeIDs=new Array(this.parameterCount)}};a(qr,\"P\\\narameterDescriptionMessage\");var Cr=qr;I.ParameterDescriptionMessage=Cr;var Qr=class Qr{constructor(e,t,n){\nthis.length=e,this.parameterName=t,this.parameterValue=n,this.name=\"parameterSta\\\ntus\"}};a(Qr,\"ParameterStatusMessage\");var Ir=Qr;I.ParameterStatusMessage=Ir;var Wr=class Wr{constructor(e,t){\nthis.length=e,this.salt=t,this.name=\"authenticationMD5Password\"}};a(Wr,\"Authenti\\\ncationMD5Password\");var Tr=Wr;I.AuthenticationMD5Password=Tr;var jr=class jr{constructor(e,t,n){\nthis.length=e,this.processID=t,this.secretKey=n,this.name=\"backendKeyData\"}};a(jr,\n\"BackendKeyDataMessage\");var Pr=jr;I.BackendKeyDataMessage=Pr;var Hr=class Hr{constructor(e,t,n,i){\nthis.length=e,this.processId=t,this.channel=n,this.payload=i,this.name=\"notifica\\\ntion\"}};a(Hr,\"NotificationResponseMessage\");var Br=Hr;I.NotificationResponseMessage=\nBr;var Gr=class Gr{constructor(e,t){this.length=e,this.status=t,this.name=\"ready\\\nForQuery\"}};a(Gr,\"ReadyForQueryMessage\");var Lr=Gr;I.ReadyForQueryMessage=Lr;var $r=class $r{constructor(e,t){\nthis.length=e,this.text=t,this.name=\"commandComplete\"}};a($r,\"CommandCompleteMes\\\nsage\");var Rr=$r;I.CommandCompleteMessage=Rr;var Vr=class Vr{constructor(e,t){this.\nlength=e,this.fields=t,this.name=\"dataRow\",this.fieldCount=t.length}};a(Vr,\"Data\\\nRowMessage\");var Fr=Vr;I.DataRowMessage=Fr;var Kr=class Kr{constructor(e,t){this.\nlength=e,this.message=t,this.name=\"notice\"}};a(Kr,\"NoticeMessage\");var Mr=Kr;I.NoticeMessage=\nMr});var ms=T(wt=>{\"use strict\";p();Object.defineProperty(wt,\"__esModule\",{value:!0});\nwt.Writer=void 0;var Zr=class Zr{constructor(e=256){this.size=e,this.offset=5,this.\nheaderPosition=0,this.buffer=d.allocUnsafe(e)}ensure(e){var t=this.buffer.length-\nthis.offset;if(t<e){var n=this.buffer,i=n.length+(n.length>>1)+e;this.buffer=d.allocUnsafe(\ni),n.copy(this.buffer)}}addInt32(e){return this.ensure(4),this.buffer[this.offset++]=\ne>>>24&255,this.buffer[this.offset++]=e>>>16&255,this.buffer[this.offset++]=e>>>\n8&255,this.buffer[this.offset++]=e>>>0&255,this}addInt16(e){return this.ensure(2),\nthis.buffer[this.offset++]=e>>>8&255,this.buffer[this.offset++]=e>>>0&255,this}addCString(e){\nif(!e)this.ensure(1);else{var t=d.byteLength(e);this.ensure(t+1),this.buffer.write(\ne,this.offset,\"utf-8\"),this.offset+=t}return this.buffer[this.offset++]=0,this}addString(e=\"\"){\nvar t=d.byteLength(e);return this.ensure(t),this.buffer.write(e,this.offset),this.\noffset+=t,this}add(e){return this.ensure(e.length),e.copy(this.buffer,this.offset),\nthis.offset+=e.length,this}join(e){if(e){this.buffer[this.headerPosition]=e;let t=this.\noffset-(this.headerPosition+1);this.buffer.writeInt32BE(t,this.headerPosition+1)}\nreturn this.buffer.slice(e?0:5,this.offset)}flush(e){var t=this.join(e);return this.\noffset=5,this.headerPosition=0,this.buffer=d.allocUnsafe(this.size),t}};a(Zr,\"Wr\\\niter\");var Yr=Zr;wt.Writer=Yr});var ws=T(St=>{\"use strict\";p();Object.defineProperty(St,\"__esModule\",{value:!0});\nSt.serialize=void 0;var Jr=ms(),M=new Jr.Writer,Mu=a(r=>{M.addInt16(3).addInt16(\n0);for(let n of Object.keys(r))M.addCString(n).addCString(r[n]);M.addCString(\"cl\\\nient_encoding\").addCString(\"UTF8\");var e=M.addCString(\"\").flush(),t=e.length+4;return new Jr.\nWriter().addInt32(t).add(e).flush()},\"startup\"),Du=a(()=>{let r=d.allocUnsafe(8);\nreturn r.writeInt32BE(8,0),r.writeInt32BE(80877103,4),r},\"requestSsl\"),ku=a(r=>M.\naddCString(r).flush(112),\"password\"),Uu=a(function(r,e){return M.addCString(r).addInt32(\nd.byteLength(e)).addString(e),M.flush(112)},\"sendSASLInitialResponseMessage\"),Ou=a(\nfunction(r){return M.addString(r).flush(112)},\"sendSCRAMClientFinalMessage\"),Nu=a(\nr=>M.addCString(r).flush(81),\"query\"),gs=[],qu=a(r=>{let e=r.name||\"\";e.length>63&&\n(console.error(\"Warning! Postgres only supports 63 characters for query names.\"),\nconsole.error(\"You supplied %s (%s)\",e,e.length),console.error(\"This can cause c\\\nonflicts and silent errors executing queries\"));let t=r.types||gs;for(var n=t.length,\ni=M.addCString(e).addCString(r.text).addInt16(n),s=0;s<n;s++)i.addInt32(t[s]);return M.\nflush(80)},\"parse\"),Oe=new Jr.Writer,Qu=a(function(r,e){for(let t=0;t<r.length;t++){\nlet n=e?e(r[t],t):r[t];n==null?(M.addInt16(0),Oe.addInt32(-1)):n instanceof d?(M.\naddInt16(1),Oe.addInt32(n.length),Oe.add(n)):(M.addInt16(0),Oe.addInt32(d.byteLength(\nn)),Oe.addString(n))}},\"writeValues\"),Wu=a((r={})=>{let e=r.portal||\"\",t=r.statement||\n\"\",n=r.binary||!1,i=r.values||gs,s=i.length;return M.addCString(e).addCString(t),\nM.addInt16(s),Qu(i,r.valueMapper),M.addInt16(s),M.add(Oe.flush()),M.addInt16(n?1:\n0),M.flush(66)},\"bind\"),ju=d.from([69,0,0,0,9,0,0,0,0,0]),Hu=a(r=>{if(!r||!r.portal&&\n!r.rows)return ju;let e=r.portal||\"\",t=r.rows||0,n=d.byteLength(e),i=4+n+1+4,s=d.\nallocUnsafe(1+i);return s[0]=69,s.writeInt32BE(i,1),s.write(e,5,\"utf-8\"),s[n+5]=\n0,s.writeUInt32BE(t,s.length-4),s},\"execute\"),Gu=a((r,e)=>{let t=d.allocUnsafe(16);\nreturn t.writeInt32BE(16,0),t.writeInt16BE(1234,4),t.writeInt16BE(5678,6),t.writeInt32BE(\nr,8),t.writeInt32BE(e,12),t},\"cancel\"),Xr=a((r,e)=>{let n=4+d.byteLength(e)+1,i=d.\nallocUnsafe(1+n);return i[0]=r,i.writeInt32BE(n,1),i.write(e,5,\"utf-8\"),i[n]=0,i},\n\"cstringMessage\"),$u=M.addCString(\"P\").flush(68),Vu=M.addCString(\"S\").flush(68),\nKu=a(r=>r.name?Xr(68,`${r.type}${r.name||\"\"}`):r.type===\"P\"?$u:Vu,\"describe\"),zu=a(\nr=>{let e=`${r.type}${r.name||\"\"}`;return Xr(67,e)},\"close\"),Yu=a(r=>M.add(r).flush(\n100),\"copyData\"),Zu=a(r=>Xr(102,r),\"copyFail\"),bt=a(r=>d.from([r,0,0,0,4]),\"code\\\nOnlyBuffer\"),Ju=bt(72),Xu=bt(83),ec=bt(88),tc=bt(99),rc={startup:Mu,password:ku,\nrequestSsl:Du,sendSASLInitialResponseMessage:Uu,sendSCRAMClientFinalMessage:Ou,query:Nu,\nparse:qu,bind:Wu,execute:Hu,describe:Ku,close:zu,flush:a(()=>Ju,\"flush\"),sync:a(\n()=>Xu,\"sync\"),end:a(()=>ec,\"end\"),copyData:Yu,copyDone:a(()=>tc,\"copyDone\"),copyFail:Zu,\ncancel:Gu};St.serialize=rc});var bs=T(xt=>{\"use strict\";p();Object.defineProperty(xt,\"__esModule\",{value:!0});\nxt.BufferReader=void 0;var nc=d.allocUnsafe(0),tn=class tn{constructor(e=0){this.\noffset=e,this.buffer=nc,this.encoding=\"utf-8\"}setBuffer(e,t){this.offset=e,this.\nbuffer=t}int16(){let e=this.buffer.readInt16BE(this.offset);return this.offset+=\n2,e}byte(){let e=this.buffer[this.offset];return this.offset++,e}int32(){let e=this.\nbuffer.readInt32BE(this.offset);return this.offset+=4,e}string(e){let t=this.buffer.\ntoString(this.encoding,this.offset,this.offset+e);return this.offset+=e,t}cstring(){\nlet e=this.offset,t=e;for(;this.buffer[t++]!==0;);return this.offset=t,this.buffer.\ntoString(this.encoding,e,t-1)}bytes(e){let t=this.buffer.slice(this.offset,this.\noffset+e);return this.offset+=e,t}};a(tn,\"BufferReader\");var en=tn;xt.BufferReader=\nen});var vs=T(vt=>{\"use strict\";p();Object.defineProperty(vt,\"__esModule\",{value:!0});\nvt.Parser=void 0;var D=zr(),ic=bs(),rn=1,sc=4,Ss=rn+sc,xs=d.allocUnsafe(0),sn=class sn{constructor(e){\nif(this.buffer=xs,this.bufferLength=0,this.bufferOffset=0,this.reader=new ic.BufferReader,\ne?.mode===\"binary\")throw new Error(\"Binary mode not supported yet\");this.mode=e?.\nmode||\"text\"}parse(e,t){this.mergeBuffer(e);let n=this.bufferOffset+this.bufferLength,\ni=this.bufferOffset;for(;i+Ss<=n;){let s=this.buffer[i],o=this.buffer.readUInt32BE(\ni+rn),u=rn+o;if(u+i<=n){let c=this.handlePacket(i+Ss,s,o,this.buffer);t(c),i+=u}else\nbreak}i===n?(this.buffer=xs,this.bufferLength=0,this.bufferOffset=0):(this.bufferLength=\nn-i,this.bufferOffset=i)}mergeBuffer(e){if(this.bufferLength>0){let t=this.bufferLength+\ne.byteLength;if(t+this.bufferOffset>this.buffer.byteLength){let i;if(t<=this.buffer.\nbyteLength&&this.bufferOffset>=this.bufferLength)i=this.buffer;else{let s=this.buffer.\nbyteLength*2;for(;t>=s;)s*=2;i=d.allocUnsafe(s)}this.buffer.copy(i,0,this.bufferOffset,\nthis.bufferOffset+this.bufferLength),this.buffer=i,this.bufferOffset=0}e.copy(this.\nbuffer,this.bufferOffset+this.bufferLength),this.bufferLength=t}else this.buffer=\ne,this.bufferOffset=0,this.bufferLength=e.byteLength}handlePacket(e,t,n,i){switch(t){case 50:\nreturn D.bindComplete;case 49:return D.parseComplete;case 51:return D.closeComplete;case 110:\nreturn D.noData;case 115:return D.portalSuspended;case 99:return D.copyDone;case 87:\nreturn D.replicationStart;case 73:return D.emptyQuery;case 68:return this.parseDataRowMessage(\ne,n,i);case 67:return this.parseCommandCompleteMessage(e,n,i);case 90:return this.\nparseReadyForQueryMessage(e,n,i);case 65:return this.parseNotificationMessage(e,\nn,i);case 82:return this.parseAuthenticationResponse(e,n,i);case 83:return this.\nparseParameterStatusMessage(e,n,i);case 75:return this.parseBackendKeyData(e,n,i);case 69:\nreturn this.parseErrorMessage(e,n,i,\"error\");case 78:return this.parseErrorMessage(\ne,n,i,\"notice\");case 84:return this.parseRowDescriptionMessage(e,n,i);case 116:return this.\nparseParameterDescriptionMessage(e,n,i);case 71:return this.parseCopyInMessage(e,\nn,i);case 72:return this.parseCopyOutMessage(e,n,i);case 100:return this.parseCopyData(\ne,n,i);default:return new D.DatabaseError(\"received invalid response: \"+t.toString(\n16),n,\"error\")}}parseReadyForQueryMessage(e,t,n){this.reader.setBuffer(e,n);let i=this.\nreader.string(1);return new D.ReadyForQueryMessage(t,i)}parseCommandCompleteMessage(e,t,n){\nthis.reader.setBuffer(e,n);let i=this.reader.cstring();return new D.CommandCompleteMessage(\nt,i)}parseCopyData(e,t,n){let i=n.slice(e,e+(t-4));return new D.CopyDataMessage(\nt,i)}parseCopyInMessage(e,t,n){return this.parseCopyMessage(e,t,n,\"copyInRespons\\\ne\")}parseCopyOutMessage(e,t,n){return this.parseCopyMessage(e,t,n,\"copyOutRespon\\\nse\")}parseCopyMessage(e,t,n,i){this.reader.setBuffer(e,n);let s=this.reader.byte()!==\n0,o=this.reader.int16(),u=new D.CopyResponse(t,i,s,o);for(let c=0;c<o;c++)u.columnTypes[c]=\nthis.reader.int16();return u}parseNotificationMessage(e,t,n){this.reader.setBuffer(\ne,n);let i=this.reader.int32(),s=this.reader.cstring(),o=this.reader.cstring();return new D.\nNotificationResponseMessage(t,i,s,o)}parseRowDescriptionMessage(e,t,n){this.reader.\nsetBuffer(e,n);let i=this.reader.int16(),s=new D.RowDescriptionMessage(t,i);for(let o=0;o<\ni;o++)s.fields[o]=this.parseField();return s}parseField(){let e=this.reader.cstring(),\nt=this.reader.int32(),n=this.reader.int16(),i=this.reader.int32(),s=this.reader.\nint16(),o=this.reader.int32(),u=this.reader.int16()===0?\"text\":\"binary\";return new D.\nField(e,t,n,i,s,o,u)}parseParameterDescriptionMessage(e,t,n){this.reader.setBuffer(\ne,n);let i=this.reader.int16(),s=new D.ParameterDescriptionMessage(t,i);for(let o=0;o<\ni;o++)s.dataTypeIDs[o]=this.reader.int32();return s}parseDataRowMessage(e,t,n){this.\nreader.setBuffer(e,n);let i=this.reader.int16(),s=new Array(i);for(let o=0;o<i;o++){\nlet u=this.reader.int32();s[o]=u===-1?null:this.reader.string(u)}return new D.DataRowMessage(\nt,s)}parseParameterStatusMessage(e,t,n){this.reader.setBuffer(e,n);let i=this.reader.\ncstring(),s=this.reader.cstring();return new D.ParameterStatusMessage(t,i,s)}parseBackendKeyData(e,t,n){\nthis.reader.setBuffer(e,n);let i=this.reader.int32(),s=this.reader.int32();return new D.\nBackendKeyDataMessage(t,i,s)}parseAuthenticationResponse(e,t,n){this.reader.setBuffer(\ne,n);let i=this.reader.int32(),s={name:\"authenticationOk\",length:t};switch(i){case 0:\nbreak;case 3:s.length===8&&(s.name=\"authenticationCleartextPassword\");break;case 5:\nif(s.length===12){s.name=\"authenticationMD5Password\";let u=this.reader.bytes(4);\nreturn new D.AuthenticationMD5Password(t,u)}break;case 10:s.name=\"authentication\\\nSASL\",s.mechanisms=[];let o;do o=this.reader.cstring(),o&&s.mechanisms.push(o);while(o);\nbreak;case 11:s.name=\"authenticationSASLContinue\",s.data=this.reader.string(t-8);\nbreak;case 12:s.name=\"authenticationSASLFinal\",s.data=this.reader.string(t-8);break;default:\nthrow new Error(\"Unknown authenticationOk message type \"+i)}return s}parseErrorMessage(e,t,n,i){\nthis.reader.setBuffer(e,n);let s={},o=this.reader.string(1);for(;o!==\"\\0\";)s[o]=\nthis.reader.cstring(),o=this.reader.string(1);let u=s.M,c=i===\"notice\"?new D.NoticeMessage(\nt,u):new D.DatabaseError(u,t,i);return c.severity=s.S,c.code=s.C,c.detail=s.D,c.\nhint=s.H,c.position=s.P,c.internalPosition=s.p,c.internalQuery=s.q,c.where=s.W,c.\nschema=s.s,c.table=s.t,c.column=s.c,c.dataType=s.d,c.constraint=s.n,c.file=s.F,c.\nline=s.L,c.routine=s.R,c}};a(sn,\"Parser\");var nn=sn;vt.Parser=nn});var on=T(Se=>{\"use strict\";p();Object.defineProperty(Se,\"__esModule\",{value:!0});\nSe.DatabaseError=Se.serialize=Se.parse=void 0;var oc=zr();Object.defineProperty(\nSe,\"DatabaseError\",{enumerable:!0,get:a(function(){return oc.DatabaseError},\"get\")});\nvar ac=ws();Object.defineProperty(Se,\"serialize\",{enumerable:!0,get:a(function(){\nreturn ac.serialize},\"get\")});var uc=vs();function cc(r,e){let t=new uc.Parser;return r.\non(\"data\",n=>t.parse(n,e)),new Promise(n=>r.on(\"end\",()=>n()))}a(cc,\"parse\");Se.\nparse=cc});var Es={};ie(Es,{connect:()=>hc});function hc({socket:r,servername:e}){return r.\nstartTls(e),r}var _s=z(()=>{\"use strict\";p();a(hc,\"connect\")});var cn=T((ef,Is)=>{\"use strict\";p();var As=(gt(),N(ys)),lc=we().EventEmitter,{parse:fc,\nserialize:Q}=on(),Cs=Q.flush(),pc=Q.sync(),dc=Q.end(),un=class un extends lc{constructor(e){\nsuper(),e=e||{},this.stream=e.stream||new As.Socket,this._keepAlive=e.keepAlive,\nthis._keepAliveInitialDelayMillis=e.keepAliveInitialDelayMillis,this.lastBuffer=\n!1,this.parsedStatements={},this.ssl=e.ssl||!1,this._ending=!1,this._emitMessage=\n!1;var t=this;this.on(\"newListener\",function(n){n===\"message\"&&(t._emitMessage=!0)})}connect(e,t){\nvar n=this;this._connecting=!0,this.stream.setNoDelay(!0),this.stream.connect(e,\nt),this.stream.once(\"connect\",function(){n._keepAlive&&n.stream.setKeepAlive(!0,\nn._keepAliveInitialDelayMillis),n.emit(\"connect\")});let i=a(function(s){n._ending&&\n(s.code===\"ECONNRESET\"||s.code===\"EPIPE\")||n.emit(\"error\",s)},\"reportStreamError\");\nif(this.stream.on(\"error\",i),this.stream.on(\"close\",function(){n.emit(\"end\")}),!this.\nssl)return this.attachListeners(this.stream);this.stream.once(\"data\",function(s){\nvar o=s.toString(\"utf8\");switch(o){case\"S\":break;case\"N\":return n.stream.end(),n.\nemit(\"error\",new Error(\"The server does not support SSL connections\"));default:return n.\nstream.end(),n.emit(\"error\",new Error(\"There was an error establishing an SSL co\\\nnnection\"))}var u=(_s(),N(Es));let c={socket:n.stream};n.ssl!==!0&&(Object.assign(\nc,n.ssl),\"key\"in n.ssl&&(c.key=n.ssl.key)),As.isIP(t)===0&&(c.servername=t);try{\nn.stream=u.connect(c)}catch(h){return n.emit(\"error\",h)}n.attachListeners(n.stream),\nn.stream.on(\"error\",i),n.emit(\"sslconnect\")})}attachListeners(e){e.on(\"end\",()=>{\nthis.emit(\"end\")}),fc(e,t=>{var n=t.name===\"error\"?\"errorMessage\":t.name;this._emitMessage&&\nthis.emit(\"message\",t),this.emit(n,t)})}requestSsl(){this.stream.write(Q.requestSsl())}startup(e){\nthis.stream.write(Q.startup(e))}cancel(e,t){this._send(Q.cancel(e,t))}password(e){\nthis._send(Q.password(e))}sendSASLInitialResponseMessage(e,t){this._send(Q.sendSASLInitialResponseMessage(\ne,t))}sendSCRAMClientFinalMessage(e){this._send(Q.sendSCRAMClientFinalMessage(e))}_send(e){\nreturn this.stream.writable?this.stream.write(e):!1}query(e){this._send(Q.query(\ne))}parse(e){this._send(Q.parse(e))}bind(e){this._send(Q.bind(e))}execute(e){this.\n_send(Q.execute(e))}flush(){this.stream.writable&&this.stream.write(Cs)}sync(){this.\n_ending=!0,this._send(Cs),this._send(pc)}ref(){this.stream.ref()}unref(){this.stream.\nunref()}end(){if(this._ending=!0,!this._connecting||!this.stream.writable){this.\nstream.end();return}return this.stream.write(dc,()=>{this.stream.end()})}close(e){\nthis._send(Q.close(e))}describe(e){this._send(Q.describe(e))}sendCopyFromChunk(e){\nthis._send(Q.copyData(e))}endCopyFrom(){this._send(Q.copyDone())}sendCopyFail(e){\nthis._send(Q.copyFail(e))}};a(un,\"Connection\");var an=un;Is.exports=an});var Bs=T((sf,Ps)=>{\"use strict\";p();var yc=we().EventEmitter,nf=(He(),N(je)),mc=et(),\nhn=qi(),gc=Zi(),wc=hr(),bc=mt(),Ts=ps(),Sc=Xe(),xc=cn(),ln=class ln extends yc{constructor(e){\nsuper(),this.connectionParameters=new bc(e),this.user=this.connectionParameters.\nuser,this.database=this.connectionParameters.database,this.port=this.connectionParameters.\nport,this.host=this.connectionParameters.host,Object.defineProperty(this,\"passwo\\\nrd\",{configurable:!0,enumerable:!1,writable:!0,value:this.connectionParameters.password}),\nthis.replication=this.connectionParameters.replication;var t=e||{};this._Promise=\nt.Promise||b.Promise,this._types=new wc(t.types),this._ending=!1,this._connecting=\n!1,this._connected=!1,this._connectionError=!1,this._queryable=!0,this.connection=\nt.connection||new xc({stream:t.stream,ssl:this.connectionParameters.ssl,keepAlive:t.\nkeepAlive||!1,keepAliveInitialDelayMillis:t.keepAliveInitialDelayMillis||0,encoding:this.\nconnectionParameters.client_encoding||\"utf8\"}),this.queryQueue=[],this.binary=t.\nbinary||Sc.binary,this.processID=null,this.secretKey=null,this.ssl=this.connectionParameters.\nssl||!1,this.ssl&&this.ssl.key&&Object.defineProperty(this.ssl,\"key\",{enumerable:!1}),\nthis._connectionTimeoutMillis=t.connectionTimeoutMillis||0}_errorAllQueries(e){let t=a(\nn=>{m.nextTick(()=>{n.handleError(e,this.connection)})},\"enqueueError\");this.activeQuery&&\n(t(this.activeQuery),this.activeQuery=null),this.queryQueue.forEach(t),this.queryQueue.\nlength=0}_connect(e){var t=this,n=this.connection;if(this._connectionCallback=e,\nthis._connecting||this._connected){let i=new Error(\"Client has already been conn\\\nected. You cannot reuse a client.\");m.nextTick(()=>{e(i)});return}this._connecting=\n!0,this.connectionTimeoutHandle,this._connectionTimeoutMillis>0&&(this.connectionTimeoutHandle=\nsetTimeout(()=>{n._ending=!0,n.stream.destroy(new Error(\"timeout expired\"))},this.\n_connectionTimeoutMillis)),this.host&&this.host.indexOf(\"/\")===0?n.connect(this.\nhost+\"/.s.PGSQL.\"+this.port):n.connect(this.port,this.host),n.on(\"connect\",function(){\nt.ssl?n.requestSsl():n.startup(t.getStartupConf())}),n.on(\"sslconnect\",function(){\nn.startup(t.getStartupConf())}),this._attachListeners(n),n.once(\"end\",()=>{let i=this.\n_ending?new Error(\"Connection terminated\"):new Error(\"Connection terminated unex\\\npectedly\");clearTimeout(this.connectionTimeoutHandle),this._errorAllQueries(i),this.\n_ending||(this._connecting&&!this._connectionError?this._connectionCallback?this.\n_connectionCallback(i):this._handleErrorEvent(i):this._connectionError||this._handleErrorEvent(\ni)),m.nextTick(()=>{this.emit(\"end\")})})}connect(e){if(e){this._connect(e);return}\nreturn new this._Promise((t,n)=>{this._connect(i=>{i?n(i):t()})})}_attachListeners(e){\ne.on(\"authenticationCleartextPassword\",this._handleAuthCleartextPassword.bind(this)),\ne.on(\"authenticationMD5Password\",this._handleAuthMD5Password.bind(this)),e.on(\"a\\\nuthenticationSASL\",this._handleAuthSASL.bind(this)),e.on(\"authenticationSASLCont\\\ninue\",this._handleAuthSASLContinue.bind(this)),e.on(\"authenticationSASLFinal\",this.\n_handleAuthSASLFinal.bind(this)),e.on(\"backendKeyData\",this._handleBackendKeyData.\nbind(this)),e.on(\"error\",this._handleErrorEvent.bind(this)),e.on(\"errorMessage\",\nthis._handleErrorMessage.bind(this)),e.on(\"readyForQuery\",this._handleReadyForQuery.\nbind(this)),e.on(\"notice\",this._handleNotice.bind(this)),e.on(\"rowDescription\",this.\n_handleRowDescription.bind(this)),e.on(\"dataRow\",this._handleDataRow.bind(this)),\ne.on(\"portalSuspended\",this._handlePortalSuspended.bind(this)),e.on(\"emptyQuery\",\nthis._handleEmptyQuery.bind(this)),e.on(\"commandComplete\",this._handleCommandComplete.\nbind(this)),e.on(\"parseComplete\",this._handleParseComplete.bind(this)),e.on(\"cop\\\nyInResponse\",this._handleCopyInResponse.bind(this)),e.on(\"copyData\",this._handleCopyData.\nbind(this)),e.on(\"notification\",this._handleNotification.bind(this))}_checkPgPass(e){\nlet t=this.connection;typeof this.password==\"function\"?this._Promise.resolve().then(\n()=>this.password()).then(n=>{if(n!==void 0){if(typeof n!=\"string\"){t.emit(\"erro\\\nr\",new TypeError(\"Password must be a string\"));return}this.connectionParameters.\npassword=this.password=n}else this.connectionParameters.password=this.password=null;\ne()}).catch(n=>{t.emit(\"error\",n)}):this.password!==null?e():gc(this.connectionParameters,\nn=>{n!==void 0&&(this.connectionParameters.password=this.password=n),e()})}_handleAuthCleartextPassword(e){\nthis._checkPgPass(()=>{this.connection.password(this.password)})}_handleAuthMD5Password(e){\nthis._checkPgPass(()=>{let t=mc.postgresMd5PasswordHash(this.user,this.password,\ne.salt);this.connection.password(t)})}_handleAuthSASL(e){this._checkPgPass(()=>{\nthis.saslSession=hn.startSession(e.mechanisms),this.connection.sendSASLInitialResponseMessage(\nthis.saslSession.mechanism,this.saslSession.response)})}_handleAuthSASLContinue(e){\nhn.continueSession(this.saslSession,this.password,e.data),this.connection.sendSCRAMClientFinalMessage(\nthis.saslSession.response)}_handleAuthSASLFinal(e){hn.finalizeSession(this.saslSession,\ne.data),this.saslSession=null}_handleBackendKeyData(e){this.processID=e.processID,\nthis.secretKey=e.secretKey}_handleReadyForQuery(e){this._connecting&&(this._connecting=\n!1,this._connected=!0,clearTimeout(this.connectionTimeoutHandle),this._connectionCallback&&\n(this._connectionCallback(null,this),this._connectionCallback=null),this.emit(\"c\\\nonnect\"));let{activeQuery:t}=this;this.activeQuery=null,this.readyForQuery=!0,t&&\nt.handleReadyForQuery(this.connection),this._pulseQueryQueue()}_handleErrorWhileConnecting(e){\nif(!this._connectionError){if(this._connectionError=!0,clearTimeout(this.connectionTimeoutHandle),\nthis._connectionCallback)return this._connectionCallback(e);this.emit(\"error\",e)}}_handleErrorEvent(e){\nif(this._connecting)return this._handleErrorWhileConnecting(e);this._queryable=!1,\nthis._errorAllQueries(e),this.emit(\"error\",e)}_handleErrorMessage(e){if(this._connecting)\nreturn this._handleErrorWhileConnecting(e);let t=this.activeQuery;if(!t){this._handleErrorEvent(\ne);return}this.activeQuery=null,t.handleError(e,this.connection)}_handleRowDescription(e){\nthis.activeQuery.handleRowDescription(e)}_handleDataRow(e){this.activeQuery.handleDataRow(\ne)}_handlePortalSuspended(e){this.activeQuery.handlePortalSuspended(this.connection)}_handleEmptyQuery(e){\nthis.activeQuery.handleEmptyQuery(this.connection)}_handleCommandComplete(e){this.\nactiveQuery.handleCommandComplete(e,this.connection)}_handleParseComplete(e){this.\nactiveQuery.name&&(this.connection.parsedStatements[this.activeQuery.name]=this.\nactiveQuery.text)}_handleCopyInResponse(e){this.activeQuery.handleCopyInResponse(\nthis.connection)}_handleCopyData(e){this.activeQuery.handleCopyData(e,this.connection)}_handleNotification(e){\nthis.emit(\"notification\",e)}_handleNotice(e){this.emit(\"notice\",e)}getStartupConf(){\nvar e=this.connectionParameters,t={user:e.user,database:e.database},n=e.application_name||\ne.fallback_application_name;return n&&(t.application_name=n),e.replication&&(t.replication=\n\"\"+e.replication),e.statement_timeout&&(t.statement_timeout=String(parseInt(e.statement_timeout,\n10))),e.lock_timeout&&(t.lock_timeout=String(parseInt(e.lock_timeout,10))),e.idle_in_transaction_session_timeout&&\n(t.idle_in_transaction_session_timeout=String(parseInt(e.idle_in_transaction_session_timeout,\n10))),e.options&&(t.options=e.options),t}cancel(e,t){if(e.activeQuery===t){var n=this.\nconnection;this.host&&this.host.indexOf(\"/\")===0?n.connect(this.host+\"/.s.PGSQL.\"+\nthis.port):n.connect(this.port,this.host),n.on(\"connect\",function(){n.cancel(e.processID,\ne.secretKey)})}else e.queryQueue.indexOf(t)!==-1&&e.queryQueue.splice(e.queryQueue.\nindexOf(t),1)}setTypeParser(e,t,n){return this._types.setTypeParser(e,t,n)}getTypeParser(e,t){\nreturn this._types.getTypeParser(e,t)}escapeIdentifier(e){return'\"'+e.replace(/\"/g,\n'\"\"')+'\"'}escapeLiteral(e){for(var t=!1,n=\"'\",i=0;i<e.length;i++){var s=e[i];s===\n\"'\"?n+=s+s:s===\"\\\\\"?(n+=s+s,t=!0):n+=s}return n+=\"'\",t===!0&&(n=\" E\"+n),n}_pulseQueryQueue(){\nif(this.readyForQuery===!0)if(this.activeQuery=this.queryQueue.shift(),this.activeQuery){\nthis.readyForQuery=!1,this.hasExecuted=!0;let e=this.activeQuery.submit(this.connection);\ne&&m.nextTick(()=>{this.activeQuery.handleError(e,this.connection),this.readyForQuery=\n!0,this._pulseQueryQueue()})}else this.hasExecuted&&(this.activeQuery=null,this.\nemit(\"drain\"))}query(e,t,n){var i,s,o,u,c;if(e==null)throw new TypeError(\"Client\\\n was passed a null or undefined query\");return typeof e.submit==\"function\"?(o=e.\nquery_timeout||this.connectionParameters.query_timeout,s=i=e,typeof t==\"function\"&&\n(i.callback=i.callback||t)):(o=this.connectionParameters.query_timeout,i=new Ts(\ne,t,n),i.callback||(s=new this._Promise((h,l)=>{i.callback=(y,x)=>y?l(y):h(x)}))),\no&&(c=i.callback,u=setTimeout(()=>{var h=new Error(\"Query read timeout\");m.nextTick(\n()=>{i.handleError(h,this.connection)}),c(h),i.callback=()=>{};var l=this.queryQueue.\nindexOf(i);l>-1&&this.queryQueue.splice(l,1),this._pulseQueryQueue()},o),i.callback=\n(h,l)=>{clearTimeout(u),c(h,l)}),this.binary&&!i.binary&&(i.binary=!0),i._result&&\n!i._result._types&&(i._result._types=this._types),this._queryable?this._ending?(m.\nnextTick(()=>{i.handleError(new Error(\"Client was closed and is not queryable\"),\nthis.connection)}),s):(this.queryQueue.push(i),this._pulseQueryQueue(),s):(m.nextTick(\n()=>{i.handleError(new Error(\"Client has encountered a connection error and is n\\\not queryable\"),this.connection)}),s)}ref(){this.connection.ref()}unref(){this.connection.\nunref()}end(e){if(this._ending=!0,!this.connection._connecting)if(e)e();else return this.\n_Promise.resolve();if(this.activeQuery||!this._queryable?this.connection.stream.\ndestroy():this.connection.end(),e)this.connection.once(\"end\",e);else return new this.\n_Promise(t=>{this.connection.once(\"end\",t)})}};a(ln,\"Client\");var Et=ln;Et.Query=\nTs;Ps.exports=Et});var Ms=T((uf,Fs)=>{\"use strict\";p();var vc=we().EventEmitter,Ls=a(function(){},\"\\\nNOOP\"),Rs=a((r,e)=>{let t=r.findIndex(e);return t===-1?void 0:r.splice(t,1)[0]},\n\"removeWhere\"),dn=class dn{constructor(e,t,n){this.client=e,this.idleListener=t,\nthis.timeoutId=n}};a(dn,\"IdleItem\");var fn=dn,yn=class yn{constructor(e){this.callback=\ne}};a(yn,\"PendingItem\");var Ne=yn;function Ec(){throw new Error(\"Release called \\\non client which has already been released to the pool.\")}a(Ec,\"throwOnDoubleRele\\\nase\");function _t(r,e){if(e)return{callback:e,result:void 0};let t,n,i=a(function(o,u){\no?t(o):n(u)},\"cb\"),s=new r(function(o,u){n=o,t=u}).catch(o=>{throw Error.captureStackTrace(\no),o});return{callback:i,result:s}}a(_t,\"promisify\");function _c(r,e){return a(function t(n){\nn.client=e,e.removeListener(\"error\",t),e.on(\"error\",()=>{r.log(\"additional clien\\\nt error after disconnection due to error\",n)}),r._remove(e),r.emit(\"error\",n,e)},\n\"idleListener\")}a(_c,\"makeIdleListener\");var mn=class mn extends vc{constructor(e,t){\nsuper(),this.options=Object.assign({},e),e!=null&&\"password\"in e&&Object.defineProperty(\nthis.options,\"password\",{configurable:!0,enumerable:!1,writable:!0,value:e.password}),\ne!=null&&e.ssl&&e.ssl.key&&Object.defineProperty(this.options.ssl,\"key\",{enumerable:!1}),\nthis.options.max=this.options.max||this.options.poolSize||10,this.options.maxUses=\nthis.options.maxUses||1/0,this.options.allowExitOnIdle=this.options.allowExitOnIdle||\n!1,this.options.maxLifetimeSeconds=this.options.maxLifetimeSeconds||0,this.log=this.\noptions.log||function(){},this.Client=this.options.Client||t||At().Client,this.Promise=\nthis.options.Promise||b.Promise,typeof this.options.idleTimeoutMillis>\"u\"&&(this.\noptions.idleTimeoutMillis=1e4),this._clients=[],this._idle=[],this._expired=new WeakSet,\nthis._pendingQueue=[],this._endCallback=void 0,this.ending=!1,this.ended=!1}_isFull(){\nreturn this._clients.length>=this.options.max}_pulseQueue(){if(this.log(\"pulse q\\\nueue\"),this.ended){this.log(\"pulse queue ended\");return}if(this.ending){this.log(\n\"pulse queue on ending\"),this._idle.length&&this._idle.slice().map(t=>{this._remove(\nt.client)}),this._clients.length||(this.ended=!0,this._endCallback());return}if(!this.\n_pendingQueue.length){this.log(\"no queued requests\");return}if(!this._idle.length&&\nthis._isFull())return;let e=this._pendingQueue.shift();if(this._idle.length){let t=this.\n_idle.pop();clearTimeout(t.timeoutId);let n=t.client;n.ref&&n.ref();let i=t.idleListener;\nreturn this._acquireClient(n,e,i,!1)}if(!this._isFull())return this.newClient(e);\nthrow new Error(\"unexpected condition\")}_remove(e){let t=Rs(this._idle,n=>n.client===\ne);t!==void 0&&clearTimeout(t.timeoutId),this._clients=this._clients.filter(n=>n!==\ne),e.end(),this.emit(\"remove\",e)}connect(e){if(this.ending){let i=new Error(\"Can\\\nnot use a pool after calling end on the pool\");return e?e(i):this.Promise.reject(\ni)}let t=_t(this.Promise,e),n=t.result;if(this._isFull()||this._idle.length){if(this.\n_idle.length&&m.nextTick(()=>this._pulseQueue()),!this.options.connectionTimeoutMillis)\nreturn this._pendingQueue.push(new Ne(t.callback)),n;let i=a((u,c,h)=>{clearTimeout(\no),t.callback(u,c,h)},\"queueCallback\"),s=new Ne(i),o=setTimeout(()=>{Rs(this._pendingQueue,\nu=>u.callback===i),s.timedOut=!0,t.callback(new Error(\"timeout exceeded when try\\\ning to connect\"))},this.options.connectionTimeoutMillis);return this._pendingQueue.\npush(s),n}return this.newClient(new Ne(t.callback)),n}newClient(e){let t=new this.\nClient(this.options);this._clients.push(t);let n=_c(this,t);this.log(\"checking c\\\nlient timeout\");let i,s=!1;this.options.connectionTimeoutMillis&&(i=setTimeout(()=>{\nthis.log(\"ending client due to timeout\"),s=!0,t.connection?t.connection.stream.destroy():\nt.end()},this.options.connectionTimeoutMillis)),this.log(\"connecting new client\"),\nt.connect(o=>{if(i&&clearTimeout(i),t.on(\"error\",n),o)this.log(\"client failed to\\\n connect\",o),this._clients=this._clients.filter(u=>u!==t),s&&(o.message=\"Connect\\\nion terminated due to connection timeout\"),this._pulseQueue(),e.timedOut||e.callback(\no,void 0,Ls);else{if(this.log(\"new client connected\"),this.options.maxLifetimeSeconds!==\n0){let u=setTimeout(()=>{this.log(\"ending client due to expired lifetime\"),this.\n_expired.add(t),this._idle.findIndex(h=>h.client===t)!==-1&&this._acquireClient(\nt,new Ne((h,l,y)=>y()),n,!1)},this.options.maxLifetimeSeconds*1e3);u.unref(),t.once(\n\"end\",()=>clearTimeout(u))}return this._acquireClient(t,e,n,!0)}})}_acquireClient(e,t,n,i){\ni&&this.emit(\"connect\",e),this.emit(\"acquire\",e),e.release=this._releaseOnce(e,n),\ne.removeListener(\"error\",n),t.timedOut?i&&this.options.verify?this.options.verify(\ne,e.release):e.release():i&&this.options.verify?this.options.verify(e,s=>{if(s)return e.\nrelease(s),t.callback(s,void 0,Ls);t.callback(void 0,e,e.release)}):t.callback(void 0,\ne,e.release)}_releaseOnce(e,t){let n=!1;return i=>{n&&Ec(),n=!0,this._release(e,\nt,i)}}_release(e,t,n){if(e.on(\"error\",t),e._poolUseCount=(e._poolUseCount||0)+1,\nthis.emit(\"release\",n,e),n||this.ending||!e._queryable||e._ending||e._poolUseCount>=\nthis.options.maxUses){e._poolUseCount>=this.options.maxUses&&this.log(\"remove ex\\\npended client\"),this._remove(e),this._pulseQueue();return}if(this._expired.has(e)){\nthis.log(\"remove expired client\"),this._expired.delete(e),this._remove(e),this._pulseQueue();\nreturn}let s;this.options.idleTimeoutMillis&&(s=setTimeout(()=>{this.log(\"remove\\\n idle client\"),this._remove(e)},this.options.idleTimeoutMillis),this.options.allowExitOnIdle&&\ns.unref()),this.options.allowExitOnIdle&&e.unref(),this._idle.push(new fn(e,t,s)),\nthis._pulseQueue()}query(e,t,n){if(typeof e==\"function\"){let s=_t(this.Promise,e);\nreturn S(function(){return s.callback(new Error(\"Passing a function as the first\\\n parameter to pool.query is not supported\"))}),s.result}typeof t==\"function\"&&(n=\nt,t=void 0);let i=_t(this.Promise,n);return n=i.callback,this.connect((s,o)=>{if(s)\nreturn n(s);let u=!1,c=a(h=>{u||(u=!0,o.release(h),n(h))},\"onError\");o.once(\"err\\\nor\",c),this.log(\"dispatching query\");try{o.query(e,t,(h,l)=>{if(this.log(\"query \\\ndispatched\"),o.removeListener(\"error\",c),!u)return u=!0,o.release(h),h?n(h):n(void 0,\nl)})}catch(h){return o.release(h),n(h)}}),i.result}end(e){if(this.log(\"ending\"),\nthis.ending){let n=new Error(\"Called end on pool more than once\");return e?e(n):\nthis.Promise.reject(n)}this.ending=!0;let t=_t(this.Promise,e);return this._endCallback=\nt.callback,this._pulseQueue(),t.result}get waitingCount(){return this._pendingQueue.\nlength}get idleCount(){return this._idle.length}get expiredCount(){return this._clients.\nreduce((e,t)=>e+(this._expired.has(t)?1:0),0)}get totalCount(){return this._clients.\nlength}};a(mn,\"Pool\");var pn=mn;Fs.exports=pn});var Ds={};ie(Ds,{default:()=>Ac});var Ac,ks=z(()=>{\"use strict\";p();Ac={}});var Us=T((ff,Cc)=>{Cc.exports={name:\"pg\",version:\"8.8.0\",description:\"PostgreSQL\\\n client - pure javascript & libpq with the same API\",keywords:[\"database\",\"libpq\",\n\"pg\",\"postgre\",\"postgres\",\"postgresql\",\"rdbms\"],homepage:\"https://github.com/bri\\\nanc/node-postgres\",repository:{type:\"git\",url:\"git://github.com/brianc/node-post\\\ngres.git\",directory:\"packages/pg\"},author:\"Brian Carlson <brian.m.carlson@gmail.\\\ncom>\",main:\"./lib\",dependencies:{\"buffer-writer\":\"2.0.0\",\"packet-reader\":\"1.0.0\",\n\"pg-connection-string\":\"^2.5.0\",\"pg-pool\":\"^3.5.2\",\"pg-protocol\":\"^1.5.0\",\"pg-ty\\\npes\":\"^2.1.0\",pgpass:\"1.x\"},devDependencies:{async:\"2.6.4\",bluebird:\"3.5.2\",co:\"\\\n4.6.0\",\"pg-copy-streams\":\"0.3.0\"},peerDependencies:{\"pg-native\":\">=3.0.1\"},peerDependenciesMeta:{\n\"pg-native\":{optional:!0}},scripts:{test:\"make test-all\"},files:[\"lib\",\"SPONSORS\\\n.md\"],license:\"MIT\",engines:{node:\">= 8.0.0\"},gitHead:\"c99fb2c127ddf8d712500db2c\\\n7b9a5491a178655\"}});var qs=T((pf,Ns)=>{\"use strict\";p();var Os=we().EventEmitter,Ic=(He(),N(je)),gn=et(),\nqe=Ns.exports=function(r,e,t){Os.call(this),r=gn.normalizeQueryConfig(r,e,t),this.\ntext=r.text,this.values=r.values,this.name=r.name,this.callback=r.callback,this.\nstate=\"new\",this._arrayMode=r.rowMode===\"array\",this._emitRowEvents=!1,this.on(\"\\\nnewListener\",function(n){n===\"row\"&&(this._emitRowEvents=!0)}.bind(this))};Ic.inherits(\nqe,Os);var Tc={sqlState:\"code\",statementPosition:\"position\",messagePrimary:\"mess\\\nage\",context:\"where\",schemaName:\"schema\",tableName:\"table\",columnName:\"column\",dataTypeName:\"\\\ndataType\",constraintName:\"constraint\",sourceFile:\"file\",sourceLine:\"line\",sourceFunction:\"\\\nroutine\"};qe.prototype.handleError=function(r){var e=this.native.pq.resultErrorFields();\nif(e)for(var t in e){var n=Tc[t]||t;r[n]=e[t]}this.callback?this.callback(r):this.\nemit(\"error\",r),this.state=\"error\"};qe.prototype.then=function(r,e){return this.\n_getPromise().then(r,e)};qe.prototype.catch=function(r){return this._getPromise().\ncatch(r)};qe.prototype._getPromise=function(){return this._promise?this._promise:\n(this._promise=new Promise(function(r,e){this._once(\"end\",r),this._once(\"error\",\ne)}.bind(this)),this._promise)};qe.prototype.submit=function(r){this.state=\"runn\\\ning\";var e=this;this.native=r.native,r.native.arrayMode=this._arrayMode;var t=a(\nfunction(s,o,u){if(r.native.arrayMode=!1,S(function(){e.emit(\"_done\")}),s)return e.\nhandleError(s);e._emitRowEvents&&(u.length>1?o.forEach((c,h)=>{c.forEach(l=>{e.emit(\n\"row\",l,u[h])})}):o.forEach(function(c){e.emit(\"row\",c,u)})),e.state=\"end\",e.emit(\n\"end\",u),e.callback&&e.callback(null,u)},\"after\");if(m.domain&&(t=m.domain.bind(\nt)),this.name){this.name.length>63&&(console.error(\"Warning! Postgres only suppo\\\nrts 63 characters for query names.\"),console.error(\"You supplied %s (%s)\",this.name,\nthis.name.length),console.error(\"This can cause conflicts and silent errors exec\\\nuting queries\"));var n=(this.values||[]).map(gn.prepareValue);if(r.namedQueries[this.\nname]){if(this.text&&r.namedQueries[this.name]!==this.text){let s=new Error(`Pre\\\npared statements must be unique - '${this.name}' was used for a different statem\\\nent`);return t(s)}return r.native.execute(this.name,n,t)}return r.native.prepare(\nthis.name,this.text,n.length,function(s){return s?t(s):(r.namedQueries[e.name]=e.\ntext,e.native.execute(e.name,n,t))})}else if(this.values){if(!Array.isArray(this.\nvalues)){let s=new Error(\"Query values must be an array\");return t(s)}var i=this.\nvalues.map(gn.prepareValue);r.native.query(this.text,i,t)}else r.native.query(this.\ntext,t)}});var Hs=T((gf,js)=>{\"use strict\";p();var Pc=(ks(),N(Ds)),Bc=hr(),mf=Us(),Qs=we().\nEventEmitter,Lc=(He(),N(je)),Rc=mt(),Ws=qs(),J=js.exports=function(r){Qs.call(this),\nr=r||{},this._Promise=r.Promise||b.Promise,this._types=new Bc(r.types),this.native=\nnew Pc({types:this._types}),this._queryQueue=[],this._ending=!1,this._connecting=\n!1,this._connected=!1,this._queryable=!0;var e=this.connectionParameters=new Rc(\nr);this.user=e.user,Object.defineProperty(this,\"password\",{configurable:!0,enumerable:!1,\nwritable:!0,value:e.password}),this.database=e.database,this.host=e.host,this.port=\ne.port,this.namedQueries={}};J.Query=Ws;Lc.inherits(J,Qs);J.prototype._errorAllQueries=\nfunction(r){let e=a(t=>{m.nextTick(()=>{t.native=this.native,t.handleError(r)})},\n\"enqueueError\");this._hasActiveQuery()&&(e(this._activeQuery),this._activeQuery=\nnull),this._queryQueue.forEach(e),this._queryQueue.length=0};J.prototype._connect=\nfunction(r){var e=this;if(this._connecting){m.nextTick(()=>r(new Error(\"Client h\\\nas already been connected. You cannot reuse a client.\")));return}this._connecting=\n!0,this.connectionParameters.getLibpqConnectionString(function(t,n){if(t)return r(\nt);e.native.connect(n,function(i){if(i)return e.native.end(),r(i);e._connected=!0,\ne.native.on(\"error\",function(s){e._queryable=!1,e._errorAllQueries(s),e.emit(\"er\\\nror\",s)}),e.native.on(\"notification\",function(s){e.emit(\"notification\",{channel:s.\nrelname,payload:s.extra})}),e.emit(\"connect\"),e._pulseQueryQueue(!0),r()})})};J.\nprototype.connect=function(r){if(r){this._connect(r);return}return new this._Promise(\n(e,t)=>{this._connect(n=>{n?t(n):e()})})};J.prototype.query=function(r,e,t){var n,\ni,s,o,u;if(r==null)throw new TypeError(\"Client was passed a null or undefined qu\\\nery\");if(typeof r.submit==\"function\")s=r.query_timeout||this.connectionParameters.\nquery_timeout,i=n=r,typeof e==\"function\"&&(r.callback=e);else if(s=this.connectionParameters.\nquery_timeout,n=new Ws(r,e,t),!n.callback){let c,h;i=new this._Promise((l,y)=>{c=\nl,h=y}),n.callback=(l,y)=>l?h(l):c(y)}return s&&(u=n.callback,o=setTimeout(()=>{\nvar c=new Error(\"Query read timeout\");m.nextTick(()=>{n.handleError(c,this.connection)}),\nu(c),n.callback=()=>{};var h=this._queryQueue.indexOf(n);h>-1&&this._queryQueue.\nsplice(h,1),this._pulseQueryQueue()},s),n.callback=(c,h)=>{clearTimeout(o),u(c,h)}),\nthis._queryable?this._ending?(n.native=this.native,m.nextTick(()=>{n.handleError(\nnew Error(\"Client was closed and is not queryable\"))}),i):(this._queryQueue.push(\nn),this._pulseQueryQueue(),i):(n.native=this.native,m.nextTick(()=>{n.handleError(\nnew Error(\"Client has encountered a connection error and is not queryable\"))}),i)};\nJ.prototype.end=function(r){var e=this;this._ending=!0,this._connected||this.once(\n\"connect\",this.end.bind(this,r));var t;return r||(t=new this._Promise(function(n,i){\nr=a(s=>s?i(s):n(),\"cb\")})),this.native.end(function(){e._errorAllQueries(new Error(\n\"Connection terminated\")),m.nextTick(()=>{e.emit(\"end\"),r&&r()})}),t};J.prototype.\n_hasActiveQuery=function(){return this._activeQuery&&this._activeQuery.state!==\"\\\nerror\"&&this._activeQuery.state!==\"end\"};J.prototype._pulseQueryQueue=function(r){\nif(this._connected&&!this._hasActiveQuery()){var e=this._queryQueue.shift();if(!e){\nr||this.emit(\"drain\");return}this._activeQuery=e,e.submit(this);var t=this;e.once(\n\"_done\",function(){t._pulseQueryQueue()})}};J.prototype.cancel=function(r){this.\n_activeQuery===r?this.native.cancel(function(){}):this._queryQueue.indexOf(r)!==\n-1&&this._queryQueue.splice(this._queryQueue.indexOf(r),1)};J.prototype.ref=function(){};\nJ.prototype.unref=function(){};J.prototype.setTypeParser=function(r,e,t){return this.\n_types.setTypeParser(r,e,t)};J.prototype.getTypeParser=function(r,e){return this.\n_types.getTypeParser(r,e)}});var wn=T((Sf,Gs)=>{\"use strict\";p();Gs.exports=Hs()});var At=T((vf,rt)=>{\"use strict\";p();var Fc=Bs(),Mc=Xe(),Dc=cn(),kc=Ms(),{DatabaseError:Uc}=on(),\nOc=a(r=>{var e;return e=class extends kc{constructor(n){super(n,r)}},a(e,\"BoundP\\\nool\"),e},\"poolFactory\"),bn=a(function(r){this.defaults=Mc,this.Client=r,this.Query=\nthis.Client.Query,this.Pool=Oc(this.Client),this._pools=[],this.Connection=Dc,this.\ntypes=Je(),this.DatabaseError=Uc},\"PG\");typeof m.env.NODE_PG_FORCE_NATIVE<\"u\"?rt.\nexports=new bn(wn()):(rt.exports=new bn(Fc),Object.defineProperty(rt.exports,\"na\\\ntive\",{configurable:!0,enumerable:!1,get(){var r=null;try{r=new bn(wn())}catch(e){\nif(e.code!==\"MODULE_NOT_FOUND\")throw e}return Object.defineProperty(rt.exports,\"\\\nnative\",{value:r}),r}}))});p();var Ct=Qe(At());gt();p();fr();gt();var Ks=Qe(et());var Sn=class Sn extends Error{constructor(){super(...arguments);_(this,\"name\",\"N\\\neonDbError\");_(this,\"severity\");_(this,\"code\");_(this,\"detail\");_(this,\"hint\");_(\nthis,\"position\");_(this,\"internalPosition\");_(this,\"internalQuery\");_(this,\"wher\\\ne\");_(this,\"schema\");_(this,\"table\");_(this,\"column\");_(this,\"dataType\");_(this,\n\"constraint\");_(this,\"file\");_(this,\"line\");_(this,\"routine\");_(this,\"sourceErro\\\nr\")}};a(Sn,\"NeonDbError\");var Ce=Sn,$s=\"transaction() expects an array of querie\\\ns, or a function returning an array of queries\",Nc=[\"severity\",\"code\",\"detail\",\"\\\nhint\",\"position\",\"internalPosition\",\"internalQuery\",\"where\",\"schema\",\"table\",\"co\\\nlumn\",\"dataType\",\"constraint\",\"file\",\"line\",\"routine\"];function zs(r,{arrayMode:e,\nfullResults:t,fetchOptions:n,isolationLevel:i,readOnly:s,deferrable:o,queryCallback:u,\nresultCallback:c}={}){if(!r)throw new Error(\"No database connection string was p\\\nrovided to `neon()`. Perhaps an environment variable has not been set?\");let h;try{\nh=lr(r)}catch{throw new Error(\"Database connection string provided to `neon()` i\\\ns not a valid URL. Connection string: \"+String(r))}let{protocol:l,username:y,password:x,\nhostname:C,port:B,pathname:W}=h;if(l!==\"postgres:\"&&l!==\"postgresql:\"||!y||!x||!C||\n!W)throw new Error(\"Database connection string format for `neon()` should be: po\\\nstgresql://user:<EMAIL>/dbname?option=value\");function X(A,...w){let P,\nV;if(typeof A==\"string\")P=A,V=w[1],w=w[0]??[];else{P=\"\";for(let j=0;j<A.length;j++)\nP+=A[j],j<w.length&&(P+=\"$\"+(j+1))}w=w.map(j=>(0,Ks.prepareValue)(j));let k={query:P,\nparams:w};return u&&u(k),qc(de,k,V)}a(X,\"resolve\"),X.transaction=async(A,w)=>{if(typeof A==\n\"function\"&&(A=A(X)),!Array.isArray(A))throw new Error($s);A.forEach(k=>{if(k[Symbol.\ntoStringTag]!==\"NeonQueryPromise\")throw new Error($s)});let P=A.map(k=>k.parameterizedQuery),\nV=A.map(k=>k.opts??{});return de(P,V,w)};async function de(A,w,P){let{fetchEndpoint:V,\nfetchFunction:k}=Ae,j=typeof V==\"function\"?V(C,B):V,ce=Array.isArray(A)?{queries:A}:\nA,ee=n??{},R=e??!1,G=t??!1,he=i,ye=s,ve=o;P!==void 0&&(P.fetchOptions!==void 0&&\n(ee={...ee,...P.fetchOptions}),P.arrayMode!==void 0&&(R=P.arrayMode),P.fullResults!==\nvoid 0&&(G=P.fullResults),P.isolationLevel!==void 0&&(he=P.isolationLevel),P.readOnly!==\nvoid 0&&(ye=P.readOnly),P.deferrable!==void 0&&(ve=P.deferrable)),w!==void 0&&!Array.\nisArray(w)&&w.fetchOptions!==void 0&&(ee={...ee,...w.fetchOptions});let me={\"Neo\\\nn-Connection-String\":r,\"Neon-Raw-Text-Output\":\"true\",\"Neon-Array-Mode\":\"true\"};Array.\nisArray(A)&&(he!==void 0&&(me[\"Neon-Batch-Isolation-Level\"]=he),ye!==void 0&&(me[\"\\\nNeon-Batch-Read-Only\"]=String(ye)),ve!==void 0&&(me[\"Neon-Batch-Deferrable\"]=String(\nve)));let se;try{se=await(k??fetch)(j,{method:\"POST\",body:JSON.stringify(ce),headers:me,\n...ee})}catch(oe){let O=new Ce(`Error connecting to database: ${oe.message}`);throw O.\nsourceError=oe,O}if(se.ok){let oe=await se.json();if(Array.isArray(A)){let O=oe.\nresults;if(!Array.isArray(O))throw new Ce(\"Neon internal error: unexpected resul\\\nt format\");return O.map((K,le)=>{let _n=w[le]??{},Js=_n.arrayMode??R,Xs=_n.fullResults??\nG;return Vs(K,{arrayMode:Js,fullResults:Xs,parameterizedQuery:A[le],resultCallback:c})})}else{\nlet O=w??{},K=O.arrayMode??R,le=O.fullResults??G;return Vs(oe,{arrayMode:K,fullResults:le,\nparameterizedQuery:A,resultCallback:c})}}else{let{status:oe}=se;if(oe===400){let O=await se.\njson(),K=new Ce(O.message);for(let le of Nc)K[le]=O[le]??void 0;throw K}else{let O=await se.\ntext();throw new Ce(`Server error (HTTP status ${oe}): ${O}`)}}}return a(de,\"exe\\\ncute\"),X}a(zs,\"neon\");function qc(r,e,t){return{[Symbol.toStringTag]:\"NeonQueryP\\\nromise\",parameterizedQuery:e,opts:t,then:a((n,i)=>r(e,t).then(n,i),\"then\"),catch:a(\nn=>r(e,t).catch(n),\"catch\"),finally:a(n=>r(e,t).finally(n),\"finally\")}}a(qc,\"cre\\\nateNeonQueryPromise\");function Vs(r,{arrayMode:e,fullResults:t,parameterizedQuery:n,\nresultCallback:i}){let s=r.fields.map(c=>c.name),o=r.fields.map(c=>xe.types.getTypeParser(\nc.dataTypeID)),u=e===!0?r.rows.map(c=>c.map((h,l)=>h===null?null:o[l](h))):r.rows.\nmap(c=>Object.fromEntries(c.map((h,l)=>[s[l],h===null?null:o[l](h)])));return i&&\ni(n,r,u,{arrayMode:e,fullResults:t}),t?(r.viaNeonFetch=!0,r.rowAsArray=e,r.rows=\nu,r):u}a(Vs,\"processQueryResult\");var Zs=Qe(mt()),xe=Qe(At());var vn=class vn extends Ct.Client{constructor(t){super(t);this.config=t}get neonConfig(){\nreturn this.connection.stream}connect(t){let{neonConfig:n}=this;n.forceDisablePgSSL&&\n(this.ssl=this.connection.ssl=!1),this.ssl&&n.useSecureWebSocket&&console.warn(\"\\\nSSL is enabled for both Postgres (e.g. ?sslmode=require in the connection string\\\n + forceDisablePgSSL = false) and the WebSocket tunnel (useSecureWebSocket = tru\\\ne). Double encryption will increase latency and CPU usage. It may be appropriate\\\n to disable SSL in the Postgres connection parameters or set forceDisablePgSSL =\\\n true.\");let i=this.config?.host!==void 0||this.config?.connectionString!==void 0||\nm.env.PGHOST!==void 0,s=m.env.USER??m.env.USERNAME;if(!i&&this.host===\"localhost\"&&\nthis.user===s&&this.database===s&&this.password===null)throw new Error(`No datab\\\nase host or connection string was set, and key parameters have default values (h\\\nost: localhost, user: ${s}, db: ${s}, password: null). Is an environment variabl\\\ne missing? Alternatively, if you intended to connect with these parameters, plea\\\nse set the host to 'localhost' explicitly.`);let o=super.connect(t),u=n.pipelineTLS&&\nthis.ssl,c=n.pipelineConnect===\"password\";if(!u&&!n.pipelineConnect)return o;let h=this.\nconnection;if(u&&h.on(\"connect\",()=>h.stream.emit(\"data\",\"S\")),c){h.removeAllListeners(\n\"authenticationCleartextPassword\"),h.removeAllListeners(\"readyForQuery\"),h.once(\n\"readyForQuery\",()=>h.on(\"readyForQuery\",this._handleReadyForQuery.bind(this)));\nlet l=this.ssl?\"sslconnect\":\"connect\";h.on(l,()=>{this._handleAuthCleartextPassword(),\nthis._handleReadyForQuery()})}return o}async _handleAuthSASLContinue(t){let n=this.\nsaslSession,i=this.password,s=t.data;if(n.message!==\"SASLInitialResponse\"||typeof i!=\n\"string\"||typeof s!=\"string\")throw new Error(\"SASL: protocol error\");let o=Object.\nfromEntries(s.split(\",\").map(O=>{if(!/^.=/.test(O))throw new Error(\"SASL: Invali\\\nd attribute pair entry\");let K=O[0],le=O.substring(2);return[K,le]})),u=o.r,c=o.\ns,h=o.i;if(!u||!/^[!-+--~]+$/.test(u))throw new Error(\"SASL: SCRAM-SERVER-FIRST-\\\nMESSAGE: nonce missing/unprintable\");if(!c||!/^(?:[a-zA-Z0-9+/]{4})*(?:[a-zA-Z0-9+/]{2}==|[a-zA-Z0-9+/]{3}=)?$/.\ntest(c))throw new Error(\"SASL: SCRAM-SERVER-FIRST-MESSAGE: salt missing/not base\\\n64\");if(!h||!/^[1-9][0-9]*$/.test(h))throw new Error(\"SASL: SCRAM-SERVER-FIRST-M\\\nESSAGE: missing/invalid iteration count\");if(!u.startsWith(n.clientNonce))throw new Error(\n\"SASL: SCRAM-SERVER-FIRST-MESSAGE: server nonce does not start with client nonce\");\nif(u.length===n.clientNonce.length)throw new Error(\"SASL: SCRAM-SERVER-FIRST-MES\\\nSAGE: server nonce is too short\");let l=parseInt(h,10),y=d.from(c,\"base64\"),x=new TextEncoder,\nC=x.encode(i),B=await g.subtle.importKey(\"raw\",C,{name:\"HMAC\",hash:{name:\"SHA-25\\\n6\"}},!1,[\"sign\"]),W=new Uint8Array(await g.subtle.sign(\"HMAC\",B,d.concat([y,d.from(\n[0,0,0,1])]))),X=W;for(var de=0;de<l-1;de++)W=new Uint8Array(await g.subtle.sign(\n\"HMAC\",B,W)),X=d.from(X.map((O,K)=>X[K]^W[K]));let A=X,w=await g.subtle.importKey(\n\"raw\",A,{name:\"HMAC\",hash:{name:\"SHA-256\"}},!1,[\"sign\"]),P=new Uint8Array(await g.\nsubtle.sign(\"HMAC\",w,x.encode(\"Client Key\"))),V=await g.subtle.digest(\"SHA-256\",\nP),k=\"n=*,r=\"+n.clientNonce,j=\"r=\"+u+\",s=\"+c+\",i=\"+l,ce=\"c=biws,r=\"+u,ee=k+\",\"+j+\n\",\"+ce,R=await g.subtle.importKey(\"raw\",V,{name:\"HMAC\",hash:{name:\"SHA-256\"}},!1,\n[\"sign\"]);var G=new Uint8Array(await g.subtle.sign(\"HMAC\",R,x.encode(ee))),he=d.\nfrom(P.map((O,K)=>P[K]^G[K])),ye=he.toString(\"base64\");let ve=await g.subtle.importKey(\n\"raw\",A,{name:\"HMAC\",hash:{name:\"SHA-256\"}},!1,[\"sign\"]),me=await g.subtle.sign(\n\"HMAC\",ve,x.encode(\"Server Key\")),se=await g.subtle.importKey(\"raw\",me,{name:\"HM\\\nAC\",hash:{name:\"SHA-256\"}},!1,[\"sign\"]);var oe=d.from(await g.subtle.sign(\"HMAC\",\nse,x.encode(ee)));n.message=\"SASLResponse\",n.serverSignature=oe.toString(\"base64\"),\nn.response=ce+\",p=\"+ye,this.connection.sendSCRAMClientFinalMessage(this.saslSession.\nresponse)}};a(vn,\"NeonClient\");var xn=vn;function Qc(r,e){if(e)return{callback:e,\nresult:void 0};let t,n,i=a(function(o,u){o?t(o):n(u)},\"cb\"),s=new r(function(o,u){\nn=o,t=u});return{callback:i,result:s}}a(Qc,\"promisify\");var En=class En extends Ct.Pool{constructor(){\nsuper(...arguments);_(this,\"Client\",xn);_(this,\"hasFetchUnsupportedListeners\",!1)}on(t,n){\nreturn t!==\"error\"&&(this.hasFetchUnsupportedListeners=!0),super.on(t,n)}query(t,n,i){\nif(!Ae.poolQueryViaFetch||this.hasFetchUnsupportedListeners||typeof t==\"function\")\nreturn super.query(t,n,i);typeof n==\"function\"&&(i=n,n=void 0);let s=Qc(this.Promise,\ni);i=s.callback;try{let o=new Zs.default(this.options),u=encodeURIComponent,c=encodeURI,\nh=`postgresql://${u(o.user)}:${u(o.password)}@${u(o.host)}/${c(o.database)}`,l=typeof t==\n\"string\"?t:t.text,y=n??t.values??[];zs(h,{fullResults:!0,arrayMode:t.rowMode===\"\\\narray\"})(l,y).then(C=>i(void 0,C)).catch(C=>i(C))}catch(o){i(o)}return s.result}};\na(En,\"NeonPool\");var Ys=En;var export_ClientBase=xe.ClientBase;var export_Connection=xe.Connection;var export_DatabaseError=xe.DatabaseError;\nvar export_Query=xe.Query;var export_defaults=xe.defaults;var export_types=xe.types;\n\n/*! Bundled license information:\n\nieee754/index.js:\n  (*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> *)\n\nbuffer/index.js:\n  (*!\n   * The buffer module from node.js, for the browser.\n   *\n   * <AUTHOR> Aboukhadijeh <https://feross.org>\n   * @license  MIT\n   *)\n*/\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@neondatabase/serverless/index.mjs\n");

/***/ })

};
;