"use client";

import { useRouter } from "next/navigation";
import { useState } from "react";
import { Loader2, Instagram, Monitor, FileText, Square, Smartphone, CreditCard, Facebook, Youtube, Palette } from "lucide-react";

import { useCreateProject } from "@/features/projects/api/use-create-project";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface TemplateCategory {
  id: string;
  name: string;
  description: string;
  width: number;
  height: number;
  icon: React.ComponentType<{ className?: string }>;
  popular?: boolean;
}

const templateCategories: TemplateCategory[] = [
  {
    id: "instagram-post",
    name: "Instagram Post",
    description: "1080 x 1080 px",
    width: 1080,
    height: 1080,
    icon: Instagram,
    popular: true,
  },
  {
    id: "instagram-story",
    name: "Instagram Story",
    description: "1080 x 1920 px",
    width: 1080,
    height: 1920,
    icon: Smartphone,
    popular: true,
  },
  {
    id: "presentation",
    name: "Presentation",
    description: "1920 x 1080 px",
    width: 1920,
    height: 1080,
    icon: Monitor,
    popular: true,
  },
  {
    id: "document",
    name: "Document",
    description: "2480 x 3508 px (A4)",
    width: 2480,
    height: 3508,
    icon: FileText,
  },
  {
    id: "poster",
    name: "Poster",
    description: "1654 x 2339 px",
    width: 1654,
    height: 2339,
    icon: Square,
  },
  {
    id: "business-card",
    name: "Business Card",
    description: "1050 x 600 px",
    width: 1050,
    height: 600,
    icon: CreditCard,
  },
  {
    id: "facebook-post",
    name: "Facebook Post",
    description: "1200 x 630 px",
    width: 1200,
    height: 630,
    icon: Facebook,
  },
  {
    id: "youtube-thumbnail",
    name: "YouTube Thumbnail",
    description: "1280 x 720 px",
    width: 1280,
    height: 720,
    icon: Youtube,
  },
  {
    id: "custom",
    name: "Custom Size",
    description: "900 x 1200 px",
    width: 900,
    height: 1200,
    icon: Palette,
  },
];

export const TemplateCategories = () => {
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const mutation = useCreateProject();

  const handleCategorySelect = (category: TemplateCategory) => {
    setSelectedCategory(category.id);
    setLoading(true);
    
    mutation.mutate(
      {
        name: `${category.name} project`,
        json: "",
        width: category.width,
        height: category.height,
      },
      {
        onSuccess: ({ data }) => {
          router.push(`/editor/${data.id}`);
        },
        onError: () => {
          setLoading(false);
          setSelectedCategory(null);
        },
      }
    );
  };

  return (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-bold text-gray-900">Choose a template size</h2>
        <p className="text-gray-600">Select the perfect canvas size for your design</p>
      </div>

      {/* Popular Templates */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-800">Popular</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {templateCategories
            .filter(category => category.popular)
            .map((category) => (
              <TemplateCard
                key={category.id}
                category={category}
                isSelected={selectedCategory === category.id}
                isLoading={loading && selectedCategory === category.id}
                onClick={() => handleCategorySelect(category)}
                disabled={loading}
              />
            ))}
        </div>
      </div>

      {/* All Templates */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-800">All Templates</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
          {templateCategories.map((category) => (
            <TemplateCard
              key={category.id}
              category={category}
              isSelected={selectedCategory === category.id}
              isLoading={loading && selectedCategory === category.id}
              onClick={() => handleCategorySelect(category)}
              disabled={loading}
              compact
            />
          ))}
        </div>
      </div>
    </div>
  );
};

interface TemplateCardProps {
  category: TemplateCategory;
  isSelected: boolean;
  isLoading: boolean;
  onClick: () => void;
  disabled: boolean;
  compact?: boolean;
}

const TemplateCard = ({
  category,
  isSelected,
  isLoading,
  onClick,
  disabled,
  compact = false,
}: TemplateCardProps) => {
  const Icon = category.icon;

  return (
    <Button
      variant="outline"
      onClick={onClick}
      disabled={disabled}
      className={cn(
        "relative h-auto p-4 flex flex-col items-center space-y-3 transition-all duration-200 hover:shadow-md",
        compact ? "aspect-square" : "aspect-[4/3]",
        isSelected && "ring-2 ring-blue-500 bg-blue-50",
        disabled && "opacity-50 cursor-not-allowed"
      )}
    >
      {isLoading && (
        <div className="absolute inset-0 bg-white/80 flex items-center justify-center rounded-md">
          <Loader2 className="size-6 animate-spin text-blue-500" />
        </div>
      )}
      
      <div className={cn(
        "rounded-lg bg-gray-100 flex items-center justify-center",
        compact ? "size-8" : "size-12"
      )}>
        <Icon className={cn(
          "text-gray-600",
          compact ? "size-4" : "size-6"
        )} />
      </div>
      
      <div className="text-center space-y-1">
        <h4 className={cn(
          "font-medium text-gray-900",
          compact ? "text-xs" : "text-sm"
        )}>
          {category.name}
        </h4>
        <p className={cn(
          "text-gray-500",
          compact ? "text-xs" : "text-xs"
        )}>
          {category.description}
        </p>
      </div>
    </Button>
  );
};
