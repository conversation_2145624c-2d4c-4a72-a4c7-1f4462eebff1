import { Hono } from "hono";
import { verifyAuth } from "@hono/auth-js";
import { desc, eq } from "drizzle-orm";

import { unsplash } from "@/lib/unsplash";
import { db } from "@/db/drizzle";
import { uploadedImages } from "@/db/schema";

const DEFAULT_COUNT = 50;
const DEFAULT_COLLECTION_IDS = ["317099"];

const app = new Hono()
  .get("/", verifyAuth(), async (c) => {
    const auth = c.get("authUser");

    if (!auth.token?.id) {
      return c.json({ error: "Unauthorized" }, 401);
    }

    try {
      // Get user's uploaded images
      const userImages = await db
        .select()
        .from(uploadedImages)
        .where(eq(uploadedImages.userId, auth.token.id))
        .orderBy(desc(uploadedImages.createdAt))
        .limit(20);

      // Transform uploaded images to match expected format
      const transformedUserImages = userImages.map((img) => ({
        id: img.id,
        urls: {
          regular: img.url,
          small: img.url,
          thumb: img.url,
        },
        alt_description: img.originalName,
        links: { html: "#" },
        user: { name: "Your Upload" },
        isUserUpload: true,
      }));

      // Get Unsplash images (only if API key is configured)
      let unsplashImages = [];
      if (process.env.UNSPLASH_ACCESS_KEY) {
        try {
          const images = await unsplash.photos.getRandom({
            collectionIds: DEFAULT_COLLECTION_IDS,
            count: DEFAULT_COUNT,
          });

          if (!images.errors) {
            let response = images.response;
            if (!Array.isArray(response)) {
              response = [response];
            }
            unsplashImages = response.map((img) => ({
              ...img,
              isUserUpload: false,
            }));
          }
        } catch (error) {
          console.error("Unsplash API error:", error);
          // Continue without Unsplash images
        }
      }

      // Combine user uploads (first) with Unsplash images
      const allImages = [...transformedUserImages, ...unsplashImages];

      return c.json({ data: allImages });
    } catch (error) {
      console.error("Error fetching images:", error);
      return c.json({ error: "Something went wrong" }, 400);
    }
  });

export default app;
