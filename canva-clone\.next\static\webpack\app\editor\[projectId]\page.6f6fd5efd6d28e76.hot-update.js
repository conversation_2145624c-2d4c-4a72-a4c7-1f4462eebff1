"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[projectId]/page",{

/***/ "(app-pages-browser)/./src/features/editor/components/alignment-indicators.tsx":
/*!*****************************************************************!*\
  !*** ./src/features/editor/components/alignment-indicators.tsx ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AlignmentIndicators: function() { return /* binding */ AlignmentIndicators; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ AlignmentIndicators auto */ \nvar _s = $RefreshSig$();\n\nconst AlignmentIndicators = (param)=>{\n    let { canvas, selectedObjects } = param;\n    _s();\n    const [alignment, setAlignment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        horizontalCenter: false,\n        verticalCenter: false\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!canvas || selectedObjects.length === 0) {\n            setAlignment({\n                horizontalCenter: false,\n                verticalCenter: false\n            });\n            return;\n        }\n        const checkAlignment = ()=>{\n            const workspace = canvas.getObjects().find((obj)=>obj.name === \"clip\");\n            if (!workspace) return;\n            const workspaceCenter = workspace.getCenterPoint();\n            const tolerance = 2; // Pixel tolerance for \"centered\"\n            let isHorizontalCenter = false;\n            let isVerticalCenter = false;\n            if (selectedObjects.length === 1) {\n                // Single object alignment\n                const obj = selectedObjects[0];\n                const objCenter = obj.getCenterPoint();\n                isHorizontalCenter = Math.abs(objCenter.x - workspaceCenter.x) <= tolerance;\n                isVerticalCenter = Math.abs(objCenter.y - workspaceCenter.y) <= tolerance;\n            } else if (selectedObjects.length > 1) {\n                // Multiple objects - check if their collective center is aligned\n                let totalX = 0;\n                let totalY = 0;\n                selectedObjects.forEach((obj)=>{\n                    const center = obj.getCenterPoint();\n                    totalX += center.x;\n                    totalY += center.y;\n                });\n                const groupCenterX = totalX / selectedObjects.length;\n                const groupCenterY = totalY / selectedObjects.length;\n                isHorizontalCenter = Math.abs(groupCenterX - workspaceCenter.x) <= tolerance;\n                isVerticalCenter = Math.abs(groupCenterY - workspaceCenter.y) <= tolerance;\n            }\n            setAlignment({\n                horizontalCenter: isHorizontalCenter,\n                verticalCenter: isVerticalCenter\n            });\n        };\n        // Check alignment initially\n        checkAlignment();\n        // Listen for object movements\n        const handleObjectMoving = ()=>checkAlignment();\n        const handleObjectModified = ()=>checkAlignment();\n        canvas.on(\"object:moving\", handleObjectMoving);\n        canvas.on(\"object:modified\", handleObjectModified);\n        return ()=>{\n            canvas.off(\"object:moving\", handleObjectMoving);\n            canvas.off(\"object:modified\", handleObjectModified);\n        };\n    }, [\n        canvas,\n        selectedObjects\n    ]);\n    if (!canvas || selectedObjects.length === 0) {\n        return null;\n    }\n    const workspace = canvas.getObjects().find((obj)=>obj.name === \"clip\");\n    if (!workspace) return null;\n    const canvasElement = canvas.getElement();\n    const canvasRect = canvasElement.getBoundingClientRect();\n    const zoom = canvas.getZoom();\n    const viewportTransform = canvas.viewportTransform || [\n        1,\n        0,\n        0,\n        1,\n        0,\n        0\n    ];\n    // Calculate workspace position on screen\n    const workspaceLeft = workspace.left || 0;\n    const workspaceTop = workspace.top || 0;\n    const workspaceWidth = workspace.width || 0;\n    const workspaceHeight = workspace.height || 0;\n    // Transform workspace coordinates to screen coordinates\n    const screenLeft = workspaceLeft * zoom + viewportTransform[4];\n    const screenTop = workspaceTop * zoom + viewportTransform[5];\n    const screenWidth = workspaceWidth * zoom;\n    const screenHeight = workspaceHeight * zoom;\n    const centerX = screenLeft + screenWidth / 2;\n    const centerY = screenTop + screenHeight / 2;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"absolute inset-0 pointer-events-none z-10\",\n        children: [\n            alignment.horizontalCenter && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bg-blue-500 opacity-75\",\n                style: {\n                    left: \"\".concat(centerX - 0.5, \"px\"),\n                    top: \"\".concat(screenTop, \"px\"),\n                    width: \"1px\",\n                    height: \"\".concat(screenHeight, \"px\")\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\alignment-indicators.tsx\",\n                lineNumber: 119,\n                columnNumber: 9\n            }, undefined),\n            alignment.verticalCenter && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bg-blue-500 opacity-75\",\n                style: {\n                    left: \"\".concat(screenLeft, \"px\"),\n                    top: \"\".concat(centerY - 0.5, \"px\"),\n                    width: \"\".concat(screenWidth, \"px\"),\n                    height: \"1px\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\alignment-indicators.tsx\",\n                lineNumber: 132,\n                columnNumber: 9\n            }, undefined),\n            (alignment.horizontalCenter || alignment.verticalCenter) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-4 left-4 bg-blue-500 text-white px-2 py-1 rounded text-xs font-medium\",\n                children: [\n                    alignment.horizontalCenter && alignment.verticalCenter && \"Centered\",\n                    alignment.horizontalCenter && !alignment.verticalCenter && \"Horizontally Centered\",\n                    !alignment.horizontalCenter && alignment.verticalCenter && \"Vertically Centered\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\alignment-indicators.tsx\",\n                lineNumber: 145,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\alignment-indicators.tsx\",\n        lineNumber: 116,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AlignmentIndicators, \"6fVCJIysgRMvLA+mCfw6aTg90+E=\");\n_c = AlignmentIndicators;\nvar _c;\n$RefreshReg$(_c, \"AlignmentIndicators\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/components/alignment-indicators.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/features/editor/components/editor.tsx":
/*!***************************************************!*\
  !*** ./src/features/editor/components/editor.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Editor: function() { return /* binding */ Editor; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fabric */ \"(app-pages-browser)/./node_modules/fabric/dist/fabric.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fabric__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash.debounce */ \"(app-pages-browser)/./node_modules/lodash.debounce/index.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _features_projects_api_use_update_project__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/features/projects/api/use-update-project */ \"(app-pages-browser)/./src/features/projects/api/use-update-project.ts\");\n/* harmony import */ var _features_editor_types__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/features/editor/types */ \"(app-pages-browser)/./src/features/editor/types.ts\");\n/* harmony import */ var _features_editor_components_navbar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/features/editor/components/navbar */ \"(app-pages-browser)/./src/features/editor/components/navbar.tsx\");\n/* harmony import */ var _features_editor_components_footer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/features/editor/components/footer */ \"(app-pages-browser)/./src/features/editor/components/footer.tsx\");\n/* harmony import */ var _features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/features/editor/hooks/use-editor */ \"(app-pages-browser)/./src/features/editor/hooks/use-editor.ts\");\n/* harmony import */ var _features_editor_components_sidebar__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/features/editor/components/sidebar */ \"(app-pages-browser)/./src/features/editor/components/sidebar.tsx\");\n/* harmony import */ var _features_editor_components_toolbar__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/features/editor/components/toolbar */ \"(app-pages-browser)/./src/features/editor/components/toolbar.tsx\");\n/* harmony import */ var _features_editor_components_shape_sidebar__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/features/editor/components/shape-sidebar */ \"(app-pages-browser)/./src/features/editor/components/shape-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_fill_color_sidebar__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/features/editor/components/fill-color-sidebar */ \"(app-pages-browser)/./src/features/editor/components/fill-color-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_stroke_color_sidebar__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/features/editor/components/stroke-color-sidebar */ \"(app-pages-browser)/./src/features/editor/components/stroke-color-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_stroke_width_sidebar__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/features/editor/components/stroke-width-sidebar */ \"(app-pages-browser)/./src/features/editor/components/stroke-width-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_opacity_sidebar__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/features/editor/components/opacity-sidebar */ \"(app-pages-browser)/./src/features/editor/components/opacity-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_text_sidebar__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/features/editor/components/text-sidebar */ \"(app-pages-browser)/./src/features/editor/components/text-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_font_sidebar__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/features/editor/components/font-sidebar */ \"(app-pages-browser)/./src/features/editor/components/font-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_image_sidebar__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/features/editor/components/image-sidebar */ \"(app-pages-browser)/./src/features/editor/components/image-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_filter_sidebar__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/features/editor/components/filter-sidebar */ \"(app-pages-browser)/./src/features/editor/components/filter-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_draw_sidebar__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/features/editor/components/draw-sidebar */ \"(app-pages-browser)/./src/features/editor/components/draw-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_ai_sidebar__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/features/editor/components/ai-sidebar */ \"(app-pages-browser)/./src/features/editor/components/ai-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_template_sidebar__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/features/editor/components/template-sidebar */ \"(app-pages-browser)/./src/features/editor/components/template-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_remove_bg_sidebar__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/features/editor/components/remove-bg-sidebar */ \"(app-pages-browser)/./src/features/editor/components/remove-bg-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_settings_sidebar__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/features/editor/components/settings-sidebar */ \"(app-pages-browser)/./src/features/editor/components/settings-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_alignment_indicators__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/features/editor/components/alignment-indicators */ \"(app-pages-browser)/./src/features/editor/components/alignment-indicators.tsx\");\n/* __next_internal_client_entry_do_not_use__ Editor auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst Editor = (param)=>{\n    let { initialData } = param;\n    _s();\n    const { mutate } = (0,_features_projects_api_use_update_project__WEBPACK_IMPORTED_MODULE_4__.useUpdateProject)(initialData.id);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    const debouncedSave = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(lodash_debounce__WEBPACK_IMPORTED_MODULE_2___default()((values)=>{\n        mutate(values);\n    }, 500), [\n        mutate\n    ]);\n    const [activeTool, setActiveTool] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"select\");\n    const [canvasIsSelected, setCanvasIsSelected] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const onClearSelection = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(()=>{\n        if (_features_editor_types__WEBPACK_IMPORTED_MODULE_5__.selectionDependentTools.includes(activeTool)) {\n            setActiveTool(\"select\");\n        }\n    }, [\n        activeTool\n    ]);\n    const { init, editor } = (0,_features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_8__.useEditor)({\n        defaultState: initialData.json,\n        defaultWidth: initialData.width,\n        defaultHeight: initialData.height,\n        clearSelectionCallback: onClearSelection,\n        saveCallback: debouncedSave,\n        setCanvasIsSelected\n    });\n    const onChangeActiveTool = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)((tool)=>{\n        if (tool === \"draw\") {\n            editor === null || editor === void 0 ? void 0 : editor.enableDrawingMode();\n        }\n        if (activeTool === \"draw\") {\n            editor === null || editor === void 0 ? void 0 : editor.disableDrawingMode();\n        }\n        if (tool === activeTool) {\n            return setActiveTool(\"select\");\n        }\n        setActiveTool(tool);\n    }, [\n        activeTool,\n        editor\n    ]);\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        const canvas = new fabric__WEBPACK_IMPORTED_MODULE_1__.fabric.Canvas(canvasRef.current, {\n            controlsAboveOverlay: true,\n            preserveObjectStacking: true\n        });\n        init({\n            initialCanvas: canvas,\n            initialContainer: containerRef.current\n        });\n        return ()=>{\n            canvas.dispose();\n        };\n    }, [\n        init\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_navbar__WEBPACK_IMPORTED_MODULE_6__.Navbar, {\n                id: initialData.id,\n                editor: editor,\n                activeTool: activeTool,\n                onChangeActiveTool: onChangeActiveTool,\n                canvasIsSelected: canvasIsSelected\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute h-[calc(100%-68px)] w-full top-[68px] flex\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_sidebar__WEBPACK_IMPORTED_MODULE_9__.Sidebar, {\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_shape_sidebar__WEBPACK_IMPORTED_MODULE_11__.ShapeSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_fill_color_sidebar__WEBPACK_IMPORTED_MODULE_12__.FillColorSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_stroke_color_sidebar__WEBPACK_IMPORTED_MODULE_13__.StrokeColorSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_stroke_width_sidebar__WEBPACK_IMPORTED_MODULE_14__.StrokeWidthSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_opacity_sidebar__WEBPACK_IMPORTED_MODULE_15__.OpacitySidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_text_sidebar__WEBPACK_IMPORTED_MODULE_16__.TextSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_font_sidebar__WEBPACK_IMPORTED_MODULE_17__.FontSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_image_sidebar__WEBPACK_IMPORTED_MODULE_18__.ImageSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_template_sidebar__WEBPACK_IMPORTED_MODULE_22__.TemplateSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_filter_sidebar__WEBPACK_IMPORTED_MODULE_19__.FilterSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_ai_sidebar__WEBPACK_IMPORTED_MODULE_21__.AiSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_remove_bg_sidebar__WEBPACK_IMPORTED_MODULE_23__.RemoveBgSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_draw_sidebar__WEBPACK_IMPORTED_MODULE_20__.DrawSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_settings_sidebar__WEBPACK_IMPORTED_MODULE_24__.SettingsSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"bg-muted flex-1 overflow-auto relative flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_toolbar__WEBPACK_IMPORTED_MODULE_10__.Toolbar, {\n                                editor: editor,\n                                activeTool: activeTool,\n                                onChangeActiveTool: onChangeActiveTool\n                            }, JSON.stringify(editor === null || editor === void 0 ? void 0 : editor.canvas.getActiveObject()), false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 h-[calc(100%-124px)] bg-muted relative\",\n                                ref: containerRef,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                                        ref: canvasRef\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_alignment_indicators__WEBPACK_IMPORTED_MODULE_25__.AlignmentIndicators, {\n                                        canvas: (editor === null || editor === void 0 ? void 0 : editor.canvas) || null,\n                                        selectedObjects: (editor === null || editor === void 0 ? void 0 : editor.selectedObjects) || []\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_footer__WEBPACK_IMPORTED_MODULE_7__.Footer, {\n                                editor: editor\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n        lineNumber: 109,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Editor, \"n/S7kUBrgsra2E9lpJNgEzHIIBc=\", false, function() {\n    return [\n        _features_projects_api_use_update_project__WEBPACK_IMPORTED_MODULE_4__.useUpdateProject,\n        _features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_8__.useEditor\n    ];\n});\n_c = Editor;\nvar _c;\n$RefreshReg$(_c, \"Editor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/components/editor.tsx\n"));

/***/ })

});