"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[projectId]/page",{

/***/ "(app-pages-browser)/./src/features/editor/hooks/use-editor.ts":
/*!*************************************************!*\
  !*** ./src/features/editor/hooks/use-editor.ts ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEditor: function() { return /* binding */ useEditor; }\n/* harmony export */ });\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fabric */ \"(app-pages-browser)/./node_modules/fabric/dist/fabric.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fabric__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _features_editor_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/features/editor/types */ \"(app-pages-browser)/./src/features/editor/types.ts\");\n/* harmony import */ var _features_editor_hooks_use_history__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/editor/hooks/use-history */ \"(app-pages-browser)/./src/features/editor/hooks/use-history.ts\");\n/* harmony import */ var _features_editor_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/features/editor/utils */ \"(app-pages-browser)/./src/features/editor/utils.ts\");\n/* harmony import */ var _features_editor_hooks_use_hotkeys__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/features/editor/hooks/use-hotkeys */ \"(app-pages-browser)/./src/features/editor/hooks/use-hotkeys.ts\");\n/* harmony import */ var _features_editor_hooks_use_clipboard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/features/editor/hooks//use-clipboard */ \"(app-pages-browser)/./src/features/editor/hooks/use-clipboard.ts\");\n/* harmony import */ var _features_editor_hooks_use_auto_resize__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/features/editor/hooks/use-auto-resize */ \"(app-pages-browser)/./src/features/editor/hooks/use-auto-resize.ts\");\n/* harmony import */ var _features_editor_hooks_use_canvas_events__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/features/editor/hooks/use-canvas-events */ \"(app-pages-browser)/./src/features/editor/hooks/use-canvas-events.ts\");\n/* harmony import */ var _features_editor_hooks_use_zoom_events__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/features/editor/hooks/use-zoom-events */ \"(app-pages-browser)/./src/features/editor/hooks/use-zoom-events.ts\");\n/* harmony import */ var _features_editor_hooks_use_window_events__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/features/editor/hooks/use-window-events */ \"(app-pages-browser)/./src/features/editor/hooks/use-window-events.ts\");\n/* harmony import */ var _features_editor_hooks_use_load_state__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/features/editor/hooks/use-load-state */ \"(app-pages-browser)/./src/features/editor/hooks/use-load-state.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\nconst buildEditor = (param)=>{\n    let { save, undo, redo, canRedo, canUndo, autoZoom, copy, paste, canvas, fillColor, fontFamily, setFontFamily, setFillColor, strokeColor, setStrokeColor, strokeWidth, setStrokeWidth, selectedObjects, strokeDashArray, setStrokeDashArray } = param;\n    const generateSaveOptions = ()=>{\n        const { width, height, left, top } = getWorkspace();\n        return {\n            name: \"Image\",\n            format: \"png\",\n            quality: 1,\n            width,\n            height,\n            left,\n            top\n        };\n    };\n    const savePng = ()=>{\n        const options = generateSaveOptions();\n        canvas.setViewportTransform([\n            1,\n            0,\n            0,\n            1,\n            0,\n            0\n        ]);\n        const dataUrl = canvas.toDataURL(options);\n        (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.downloadFile)(dataUrl, \"png\");\n        autoZoom();\n    };\n    const saveSvg = ()=>{\n        const options = generateSaveOptions();\n        canvas.setViewportTransform([\n            1,\n            0,\n            0,\n            1,\n            0,\n            0\n        ]);\n        const dataUrl = canvas.toDataURL(options);\n        (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.downloadFile)(dataUrl, \"svg\");\n        autoZoom();\n    };\n    const saveJpg = ()=>{\n        const options = generateSaveOptions();\n        canvas.setViewportTransform([\n            1,\n            0,\n            0,\n            1,\n            0,\n            0\n        ]);\n        const dataUrl = canvas.toDataURL(options);\n        (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.downloadFile)(dataUrl, \"jpg\");\n        autoZoom();\n    };\n    const saveJson = async ()=>{\n        const dataUrl = canvas.toJSON(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.JSON_KEYS);\n        await (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.transformText)(dataUrl.objects);\n        const fileString = \"data:text/json;charset=utf-8,\".concat(encodeURIComponent(JSON.stringify(dataUrl, null, \"\t\")));\n        (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.downloadFile)(fileString, \"json\");\n    };\n    const loadJson = (json)=>{\n        const data = JSON.parse(json);\n        canvas.loadFromJSON(data, ()=>{\n            autoZoom();\n        });\n    };\n    const getWorkspace = ()=>{\n        return canvas.getObjects().find((object)=>object.name === \"clip\");\n    };\n    const center = (object)=>{\n        const workspace = getWorkspace();\n        const center = workspace === null || workspace === void 0 ? void 0 : workspace.getCenterPoint();\n        if (!center) return;\n        // @ts-ignore\n        canvas._centerObject(object, center);\n    };\n    const addToCanvas = (object)=>{\n        center(object);\n        canvas.add(object);\n        canvas.setActiveObject(object);\n    };\n    return {\n        savePng,\n        saveJpg,\n        saveSvg,\n        saveJson,\n        loadJson,\n        canUndo,\n        canRedo,\n        autoZoom,\n        getWorkspace,\n        zoomIn: ()=>{\n            let zoomRatio = canvas.getZoom();\n            zoomRatio += 0.05;\n            const center = canvas.getCenter();\n            canvas.zoomToPoint(new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Point(center.left, center.top), zoomRatio > 1 ? 1 : zoomRatio);\n        },\n        zoomOut: ()=>{\n            let zoomRatio = canvas.getZoom();\n            zoomRatio -= 0.05;\n            const center = canvas.getCenter();\n            canvas.zoomToPoint(new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Point(center.left, center.top), zoomRatio < 0.2 ? 0.2 : zoomRatio);\n        },\n        changeSize: (value)=>{\n            const workspace = getWorkspace();\n            workspace === null || workspace === void 0 ? void 0 : workspace.set(value);\n            autoZoom();\n            save();\n        },\n        changeBackground: (value)=>{\n            const workspace = getWorkspace();\n            workspace === null || workspace === void 0 ? void 0 : workspace.set({\n                fill: value\n            });\n            canvas.renderAll();\n            save();\n        },\n        enableDrawingMode: ()=>{\n            canvas.discardActiveObject();\n            canvas.renderAll();\n            canvas.isDrawingMode = true;\n            canvas.freeDrawingBrush.width = strokeWidth;\n            canvas.freeDrawingBrush.color = strokeColor;\n        },\n        disableDrawingMode: ()=>{\n            canvas.isDrawingMode = false;\n        },\n        onUndo: ()=>undo(),\n        onRedo: ()=>redo(),\n        onCopy: ()=>copy(),\n        onPaste: ()=>paste(),\n        changeImageFilter: (value)=>{\n            const objects = canvas.getActiveObjects();\n            objects.forEach((object)=>{\n                if (object.type === \"image\") {\n                    const imageObject = object;\n                    const effect = (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.createFilter)(value);\n                    imageObject.filters = effect ? [\n                        effect\n                    ] : [];\n                    imageObject.applyFilters();\n                    canvas.renderAll();\n                }\n            });\n        },\n        addImage: (value)=>{\n            fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Image.fromURL(value, (image)=>{\n                const workspace = getWorkspace();\n                image.scaleToWidth((workspace === null || workspace === void 0 ? void 0 : workspace.width) || 0);\n                image.scaleToHeight((workspace === null || workspace === void 0 ? void 0 : workspace.height) || 0);\n                addToCanvas(image);\n            }, {\n                crossOrigin: \"anonymous\"\n            });\n        },\n        delete: ()=>{\n            canvas.getActiveObjects().forEach((object)=>canvas.remove(object));\n            canvas.discardActiveObject();\n            canvas.renderAll();\n        },\n        addText: (value, options)=>{\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Textbox(value, {\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.TEXT_OPTIONS,\n                fill: fillColor,\n                ...options\n            });\n            addToCanvas(object);\n        },\n        getActiveOpacity: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return 1;\n            }\n            const value = selectedObject.get(\"opacity\") || 1;\n            return value;\n        },\n        changeFontSize: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, fontSize exists.\n                    object.set({\n                        fontSize: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        getActiveFontSize: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FONT_SIZE;\n            }\n            // @ts-ignore\n            // Faulty TS library, fontSize exists.\n            const value = selectedObject.get(\"fontSize\") || _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FONT_SIZE;\n            return value;\n        },\n        changeTextAlign: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, textAlign exists.\n                    object.set({\n                        textAlign: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        getActiveTextAlign: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return \"left\";\n            }\n            // @ts-ignore\n            // Faulty TS library, textAlign exists.\n            const value = selectedObject.get(\"textAlign\") || \"left\";\n            return value;\n        },\n        changeFontUnderline: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, underline exists.\n                    object.set({\n                        underline: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        getActiveFontUnderline: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return false;\n            }\n            // @ts-ignore\n            // Faulty TS library, underline exists.\n            const value = selectedObject.get(\"underline\") || false;\n            return value;\n        },\n        changeFontLinethrough: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, linethrough exists.\n                    object.set({\n                        linethrough: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        getActiveFontLinethrough: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return false;\n            }\n            // @ts-ignore\n            // Faulty TS library, linethrough exists.\n            const value = selectedObject.get(\"linethrough\") || false;\n            return value;\n        },\n        changeFontStyle: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, fontStyle exists.\n                    object.set({\n                        fontStyle: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        getActiveFontStyle: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return \"normal\";\n            }\n            // @ts-ignore\n            // Faulty TS library, fontStyle exists.\n            const value = selectedObject.get(\"fontStyle\") || \"normal\";\n            return value;\n        },\n        changeFontWeight: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, fontWeight exists.\n                    object.set({\n                        fontWeight: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        changeOpacity: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                object.set({\n                    opacity: value\n                });\n            });\n            canvas.renderAll();\n        },\n        bringForward: ()=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                canvas.bringForward(object);\n            });\n            canvas.renderAll();\n            const workspace = getWorkspace();\n            workspace === null || workspace === void 0 ? void 0 : workspace.sendToBack();\n        },\n        sendBackwards: ()=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                canvas.sendBackwards(object);\n            });\n            canvas.renderAll();\n            const workspace = getWorkspace();\n            workspace === null || workspace === void 0 ? void 0 : workspace.sendToBack();\n        },\n        changeFontFamily: (value)=>{\n            setFontFamily(value);\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, fontFamily exists.\n                    object.set({\n                        fontFamily: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        changeFillColor: (value)=>{\n            setFillColor(value);\n            canvas.getActiveObjects().forEach((object)=>{\n                object.set({\n                    fill: value\n                });\n            });\n            canvas.renderAll();\n        },\n        changeStrokeColor: (value)=>{\n            setStrokeColor(value);\n            canvas.getActiveObjects().forEach((object)=>{\n                // Text types don't have stroke\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    object.set({\n                        fill: value\n                    });\n                    return;\n                }\n                object.set({\n                    stroke: value\n                });\n            });\n            canvas.freeDrawingBrush.color = value;\n            canvas.renderAll();\n        },\n        changeStrokeWidth: (value)=>{\n            setStrokeWidth(value);\n            canvas.getActiveObjects().forEach((object)=>{\n                object.set({\n                    strokeWidth: value\n                });\n            });\n            canvas.freeDrawingBrush.width = value;\n            canvas.renderAll();\n        },\n        changeStrokeDashArray: (value)=>{\n            setStrokeDashArray(value);\n            canvas.getActiveObjects().forEach((object)=>{\n                object.set({\n                    strokeDashArray: value\n                });\n            });\n            canvas.renderAll();\n        },\n        addCircle: ()=>{\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Circle({\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.CIRCLE_OPTIONS,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        addSoftRectangle: ()=>{\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Rect({\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.RECTANGLE_OPTIONS,\n                rx: 50,\n                ry: 50,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        addRectangle: ()=>{\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Rect({\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.RECTANGLE_OPTIONS,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        addTriangle: ()=>{\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Triangle({\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.TRIANGLE_OPTIONS,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        addInverseTriangle: ()=>{\n            const HEIGHT = _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.TRIANGLE_OPTIONS.height;\n            const WIDTH = _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.TRIANGLE_OPTIONS.width;\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Polygon([\n                {\n                    x: 0,\n                    y: 0\n                },\n                {\n                    x: WIDTH,\n                    y: 0\n                },\n                {\n                    x: WIDTH / 2,\n                    y: HEIGHT\n                }\n            ], {\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.TRIANGLE_OPTIONS,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        addDiamond: ()=>{\n            const HEIGHT = _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.DIAMOND_OPTIONS.height;\n            const WIDTH = _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.DIAMOND_OPTIONS.width;\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Polygon([\n                {\n                    x: WIDTH / 2,\n                    y: 0\n                },\n                {\n                    x: WIDTH,\n                    y: HEIGHT / 2\n                },\n                {\n                    x: WIDTH / 2,\n                    y: HEIGHT\n                },\n                {\n                    x: 0,\n                    y: HEIGHT / 2\n                }\n            ], {\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.DIAMOND_OPTIONS,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        canvas,\n        getActiveFontWeight: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FONT_WEIGHT;\n            }\n            // @ts-ignore\n            // Faulty TS library, fontWeight exists.\n            const value = selectedObject.get(\"fontWeight\") || _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FONT_WEIGHT;\n            return value;\n        },\n        getActiveFontFamily: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return fontFamily;\n            }\n            // @ts-ignore\n            // Faulty TS library, fontFamily exists.\n            const value = selectedObject.get(\"fontFamily\") || fontFamily;\n            return value;\n        },\n        getActiveFillColor: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return fillColor;\n            }\n            const value = selectedObject.get(\"fill\") || fillColor;\n            // Currently, gradients & patterns are not supported\n            return value;\n        },\n        getActiveStrokeColor: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return strokeColor;\n            }\n            const value = selectedObject.get(\"stroke\") || strokeColor;\n            return value;\n        },\n        getActiveStrokeWidth: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return strokeWidth;\n            }\n            const value = selectedObject.get(\"strokeWidth\") || strokeWidth;\n            return value;\n        },\n        getActiveStrokeDashArray: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return strokeDashArray;\n            }\n            const value = selectedObject.get(\"strokeDashArray\") || strokeDashArray;\n            return value;\n        },\n        selectedObjects\n    };\n};\nconst useEditor = (param)=>{\n    let { defaultState, defaultHeight, defaultWidth, clearSelectionCallback, saveCallback } = param;\n    const initialState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(defaultState);\n    const initialWidth = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(defaultWidth);\n    const initialHeight = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(defaultHeight);\n    const [canvas, setCanvas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [container, setContainer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedObjects, setSelectedObjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [fontFamily, setFontFamily] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FONT_FAMILY);\n    const [fillColor, setFillColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FILL_COLOR);\n    const [strokeColor, setStrokeColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.STROKE_COLOR);\n    const [strokeWidth, setStrokeWidth] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.STROKE_WIDTH);\n    const [strokeDashArray, setStrokeDashArray] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.STROKE_DASH_ARRAY);\n    (0,_features_editor_hooks_use_window_events__WEBPACK_IMPORTED_MODULE_10__.useWindowEvents)();\n    const { save, canRedo, canUndo, undo, redo, canvasHistory, setHistoryIndex } = (0,_features_editor_hooks_use_history__WEBPACK_IMPORTED_MODULE_3__.useHistory)({\n        canvas,\n        saveCallback\n    });\n    const { copy, paste } = (0,_features_editor_hooks_use_clipboard__WEBPACK_IMPORTED_MODULE_6__.useClipboard)({\n        canvas\n    });\n    const { autoZoom } = (0,_features_editor_hooks_use_auto_resize__WEBPACK_IMPORTED_MODULE_7__.useAutoResize)({\n        canvas,\n        container\n    });\n    (0,_features_editor_hooks_use_canvas_events__WEBPACK_IMPORTED_MODULE_8__.useCanvasEvents)({\n        save,\n        canvas,\n        setSelectedObjects,\n        clearSelectionCallback\n    });\n    (0,_features_editor_hooks_use_zoom_events__WEBPACK_IMPORTED_MODULE_9__.useZoomEvents)({\n        canvas\n    });\n    (0,_features_editor_hooks_use_hotkeys__WEBPACK_IMPORTED_MODULE_5__.useHotkeys)({\n        undo,\n        redo,\n        copy,\n        paste,\n        save,\n        canvas\n    });\n    (0,_features_editor_hooks_use_load_state__WEBPACK_IMPORTED_MODULE_11__.useLoadState)({\n        canvas,\n        autoZoom,\n        initialState,\n        canvasHistory,\n        setHistoryIndex\n    });\n    const editor = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        if (canvas) {\n            return buildEditor({\n                save,\n                undo,\n                redo,\n                canUndo,\n                canRedo,\n                autoZoom,\n                copy,\n                paste,\n                canvas,\n                fillColor,\n                strokeWidth,\n                strokeColor,\n                setFillColor,\n                setStrokeColor,\n                setStrokeWidth,\n                strokeDashArray,\n                selectedObjects,\n                setStrokeDashArray,\n                fontFamily,\n                setFontFamily\n            });\n        }\n        return undefined;\n    }, [\n        canRedo,\n        canUndo,\n        undo,\n        redo,\n        save,\n        autoZoom,\n        copy,\n        paste,\n        canvas,\n        fillColor,\n        strokeWidth,\n        strokeColor,\n        selectedObjects,\n        strokeDashArray,\n        fontFamily\n    ]);\n    const init = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((param)=>{\n        let { initialCanvas, initialContainer } = param;\n        fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Object.prototype.set({\n            cornerColor: \"#FFF\",\n            cornerStyle: \"circle\",\n            borderColor: \"#3b82f6\",\n            borderScaleFactor: 1.5,\n            transparentCorners: false,\n            borderOpacityWhenMoving: 1,\n            cornerStrokeColor: \"#3b82f6\"\n        });\n        const initialWorkspace = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Rect({\n            width: initialWidth.current,\n            height: initialHeight.current,\n            name: \"clip\",\n            fill: \"white\",\n            selectable: false,\n            hasControls: false,\n            shadow: new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Shadow({\n                color: \"rgba(0,0,0,0.8)\",\n                blur: 5\n            })\n        });\n        initialCanvas.setWidth(initialContainer.offsetWidth);\n        initialCanvas.setHeight(initialContainer.offsetHeight);\n        initialCanvas.add(initialWorkspace);\n        initialCanvas.centerObject(initialWorkspace);\n        initialCanvas.clipPath = initialWorkspace;\n        setCanvas(initialCanvas);\n        setContainer(initialContainer);\n        const currentState = JSON.stringify(initialCanvas.toJSON(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.JSON_KEYS));\n        canvasHistory.current = [\n            currentState\n        ];\n        setHistoryIndex(0);\n    }, [\n        canvasHistory,\n        setHistoryIndex\n    ]);\n    return {\n        init,\n        editor\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/hooks/use-editor.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/features/editor/hooks/use-zoom-events.ts":
/*!******************************************************!*\
  !*** ./src/features/editor/hooks/use-zoom-events.ts ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useZoomEvents: function() { return /* binding */ useZoomEvents; }\n/* harmony export */ });\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fabric */ \"(app-pages-browser)/./node_modules/fabric/dist/fabric.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fabric__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst useZoomEvents = (param)=>{\n    let { canvas } = param;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!canvas) return;\n        let isZooming = false;\n        let lastDistance = 0;\n        // Handle trackpad pinch-to-zoom (touch events)\n        const handleTouchStart = (e)=>{\n            if (e.touches.length === 2) {\n                isZooming = true;\n                const touch1 = e.touches[0];\n                const touch2 = e.touches[1];\n                lastDistance = Math.sqrt(Math.pow(touch2.clientX - touch1.clientX, 2) + Math.pow(touch2.clientY - touch1.clientY, 2));\n                e.preventDefault();\n            }\n        };\n        const handleTouchMove = (e)=>{\n            if (e.touches.length === 2 && isZooming) {\n                const touch1 = e.touches[0];\n                const touch2 = e.touches[1];\n                const distance = Math.sqrt(Math.pow(touch2.clientX - touch1.clientX, 2) + Math.pow(touch2.clientY - touch1.clientY, 2));\n                if (lastDistance > 0) {\n                    const scale = distance / lastDistance;\n                    let zoom = canvas.getZoom() * scale;\n                    // Limit zoom levels\n                    if (zoom > 1) zoom = 1;\n                    if (zoom < 0.2) zoom = 0.2;\n                    // Get center point between fingers\n                    const centerX = (touch1.clientX + touch2.clientX) / 2;\n                    const centerY = (touch1.clientY + touch2.clientY) / 2;\n                    // Get canvas bounds\n                    const canvasElement = canvas.getElement();\n                    const rect = canvasElement.getBoundingClientRect();\n                    // Convert to canvas coordinates\n                    const point = {\n                        x: centerX - rect.left,\n                        y: centerY - rect.top\n                    };\n                    canvas.zoomToPoint(point, zoom);\n                }\n                lastDistance = distance;\n                e.preventDefault();\n            }\n        };\n        const handleTouchEnd = (e)=>{\n            if (e.touches.length < 2) {\n                isZooming = false;\n                lastDistance = 0;\n            }\n        };\n        // Handle keyboard zoom shortcuts\n        const handleKeyDown = (e)=>{\n            const isCtrlKey = e.ctrlKey || e.metaKey;\n            // Prevent default browser zoom\n            if (isCtrlKey && (e.key === \"+\" || e.key === \"-\" || e.key === \"0\")) {\n                e.preventDefault();\n            }\n            // Ctrl/Cmd + Plus: Zoom in\n            if (isCtrlKey && (e.key === \"+\" || e.key === \"=\")) {\n                let zoom = canvas.getZoom();\n                zoom += 0.1;\n                if (zoom > 1) zoom = 1;\n                const center = canvas.getCenter();\n                canvas.zoomToPoint(new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Point(center.left, center.top), zoom);\n                e.preventDefault();\n            }\n            // Ctrl/Cmd + Minus: Zoom out\n            if (isCtrlKey && e.key === \"-\") {\n                let zoom = canvas.getZoom();\n                zoom -= 0.1;\n                if (zoom < 0.2) zoom = 0.2;\n                const center = canvas.getCenter();\n                canvas.zoomToPoint(new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Point(center.left, center.top), zoom);\n                e.preventDefault();\n            }\n            // Ctrl/Cmd + 0: Reset zoom\n            if (isCtrlKey && e.key === \"0\") {\n                const center = canvas.getCenter();\n                canvas.zoomToPoint(new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Point(center.left, center.top), 1);\n                e.preventDefault();\n            }\n        };\n        // Add event listeners to canvas element\n        const canvasElement = canvas.getElement();\n        if (canvasElement) {\n            canvasElement.addEventListener(\"touchstart\", handleTouchStart, {\n                passive: false\n            });\n            canvasElement.addEventListener(\"touchmove\", handleTouchMove, {\n                passive: false\n            });\n            canvasElement.addEventListener(\"touchend\", handleTouchEnd, {\n                passive: false\n            });\n        }\n        // Add keyboard events to document\n        document.addEventListener(\"keydown\", handleKeyDown);\n        return ()=>{\n            if (canvasElement) {\n                canvasElement.removeEventListener(\"touchstart\", handleTouchStart);\n                canvasElement.removeEventListener(\"touchmove\", handleTouchMove);\n                canvasElement.removeEventListener(\"touchend\", handleTouchEnd);\n            }\n            document.removeEventListener(\"keydown\", handleKeyDown);\n        };\n    }, [\n        canvas\n    ]);\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/hooks/use-zoom-events.ts\n"));

/***/ })

});