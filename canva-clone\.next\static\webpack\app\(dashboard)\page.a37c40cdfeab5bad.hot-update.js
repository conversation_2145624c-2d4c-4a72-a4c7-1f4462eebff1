"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/facebook.js":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/facebook.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Facebook; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.399.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Facebook = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Facebook\", [\n    [\n        \"path\",\n        {\n            d: \"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z\",\n            key: \"1jg4f8\"\n        }\n    ]\n]);\n //# sourceMappingURL=facebook.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/facebook.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/palette.js":
/*!*************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/palette.js ***!
  \*************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Palette; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.399.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Palette = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Palette\", [\n    [\n        \"circle\",\n        {\n            cx: \"13.5\",\n            cy: \"6.5\",\n            r: \".5\",\n            fill: \"currentColor\",\n            key: \"1okk4w\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"17.5\",\n            cy: \"10.5\",\n            r: \".5\",\n            fill: \"currentColor\",\n            key: \"f64h9f\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"8.5\",\n            cy: \"7.5\",\n            r: \".5\",\n            fill: \"currentColor\",\n            key: \"fotxhn\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"6.5\",\n            cy: \"12.5\",\n            r: \".5\",\n            fill: \"currentColor\",\n            key: \"qy21gx\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z\",\n            key: \"12rzf8\"\n        }\n    ]\n]);\n //# sourceMappingURL=palette.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/palette.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/youtube.js":
/*!*************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/youtube.js ***!
  \*************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Youtube; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.399.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Youtube = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Youtube\", [\n    [\n        \"path\",\n        {\n            d: \"M2.5 17a24.12 24.12 0 0 1 0-10 2 2 0 0 1 1.4-1.4 49.56 49.56 0 0 1 16.2 0A2 2 0 0 1 21.5 7a24.12 24.12 0 0 1 0 10 2 2 0 0 1-1.4 1.4 49.55 49.55 0 0 1-16.2 0A2 2 0 0 1 2.5 17\",\n            key: \"1q2vi4\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m10 15 5-3-5-3z\",\n            key: \"1jp15x\"\n        }\n    ]\n]);\n //# sourceMappingURL=youtube.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/youtube.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/(dashboard)/template-categories.tsx":
/*!*****************************************************!*\
  !*** ./src/app/(dashboard)/template-categories.tsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TemplateCategories: function() { return /* binding */ TemplateCategories; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_CreditCard_Facebook_FileText_Instagram_Loader2_Monitor_Palette_Smartphone_Square_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,Facebook,FileText,Instagram,Loader2,Monitor,Palette,Smartphone,Square,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/instagram.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_Facebook_FileText_Instagram_Loader2_Monitor_Palette_Smartphone_Square_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,Facebook,FileText,Instagram,Loader2,Monitor,Palette,Smartphone,Square,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_Facebook_FileText_Instagram_Loader2_Monitor_Palette_Smartphone_Square_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,Facebook,FileText,Instagram,Loader2,Monitor,Palette,Smartphone,Square,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/monitor.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_Facebook_FileText_Instagram_Loader2_Monitor_Palette_Smartphone_Square_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,Facebook,FileText,Instagram,Loader2,Monitor,Palette,Smartphone,Square,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_Facebook_FileText_Instagram_Loader2_Monitor_Palette_Smartphone_Square_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,Facebook,FileText,Instagram,Loader2,Monitor,Palette,Smartphone,Square,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_Facebook_FileText_Instagram_Loader2_Monitor_Palette_Smartphone_Square_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,Facebook,FileText,Instagram,Loader2,Monitor,Palette,Smartphone,Square,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_Facebook_FileText_Instagram_Loader2_Monitor_Palette_Smartphone_Square_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,Facebook,FileText,Instagram,Loader2,Monitor,Palette,Smartphone,Square,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/facebook.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_Facebook_FileText_Instagram_Loader2_Monitor_Palette_Smartphone_Square_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,Facebook,FileText,Instagram,Loader2,Monitor,Palette,Smartphone,Square,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/youtube.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_Facebook_FileText_Instagram_Loader2_Monitor_Palette_Smartphone_Square_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,Facebook,FileText,Instagram,Loader2,Monitor,Palette,Smartphone,Square,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_Facebook_FileText_Instagram_Loader2_Monitor_Palette_Smartphone_Square_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,Facebook,FileText,Instagram,Loader2,Monitor,Palette,Smartphone,Square,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _features_projects_api_use_create_project__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/projects/api/use-create-project */ \"(app-pages-browser)/./src/features/projects/api/use-create-project.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ TemplateCategories auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst templateCategories = [\n    {\n        id: \"instagram-post\",\n        name: \"Instagram Post\",\n        description: \"1080 x 1080 px\",\n        width: 1080,\n        height: 1080,\n        icon: _barrel_optimize_names_CreditCard_Facebook_FileText_Instagram_Loader2_Monitor_Palette_Smartphone_Square_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        popular: true\n    },\n    {\n        id: \"instagram-story\",\n        name: \"Instagram Story\",\n        description: \"1080 x 1920 px\",\n        width: 1080,\n        height: 1920,\n        icon: _barrel_optimize_names_CreditCard_Facebook_FileText_Instagram_Loader2_Monitor_Palette_Smartphone_Square_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        popular: true\n    },\n    {\n        id: \"presentation\",\n        name: \"Presentation\",\n        description: \"1920 x 1080 px\",\n        width: 1920,\n        height: 1080,\n        icon: _barrel_optimize_names_CreditCard_Facebook_FileText_Instagram_Loader2_Monitor_Palette_Smartphone_Square_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        popular: true\n    },\n    {\n        id: \"document\",\n        name: \"Document\",\n        description: \"2480 x 3508 px (A4)\",\n        width: 2480,\n        height: 3508,\n        icon: _barrel_optimize_names_CreditCard_Facebook_FileText_Instagram_Loader2_Monitor_Palette_Smartphone_Square_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    },\n    {\n        id: \"poster\",\n        name: \"Poster\",\n        description: \"1654 x 2339 px\",\n        width: 1654,\n        height: 2339,\n        icon: _barrel_optimize_names_CreditCard_Facebook_FileText_Instagram_Loader2_Monitor_Palette_Smartphone_Square_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    },\n    {\n        id: \"business-card\",\n        name: \"Business Card\",\n        description: \"1050 x 600 px\",\n        width: 1050,\n        height: 600,\n        icon: _barrel_optimize_names_CreditCard_Facebook_FileText_Instagram_Loader2_Monitor_Palette_Smartphone_Square_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n    },\n    {\n        id: \"facebook-post\",\n        name: \"Facebook Post\",\n        description: \"1200 x 630 px\",\n        width: 1200,\n        height: 630,\n        icon: _barrel_optimize_names_CreditCard_Facebook_FileText_Instagram_Loader2_Monitor_Palette_Smartphone_Square_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n    },\n    {\n        id: \"youtube-thumbnail\",\n        name: \"YouTube Thumbnail\",\n        description: \"1280 x 720 px\",\n        width: 1280,\n        height: 720,\n        icon: _barrel_optimize_names_CreditCard_Facebook_FileText_Instagram_Loader2_Monitor_Palette_Smartphone_Square_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n    },\n    {\n        id: \"custom\",\n        name: \"Custom Size\",\n        description: \"900 x 1200 px\",\n        width: 900,\n        height: 1200,\n        icon: _barrel_optimize_names_CreditCard_Facebook_FileText_Instagram_Loader2_Monitor_Palette_Smartphone_Square_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n    }\n];\nconst TemplateCategories = ()=>{\n    _s();\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const mutation = (0,_features_projects_api_use_create_project__WEBPACK_IMPORTED_MODULE_3__.useCreateProject)();\n    const handleCategorySelect = (category)=>{\n        setSelectedCategory(category.id);\n        setLoading(true);\n        mutation.mutate({\n            name: \"\".concat(category.name, \" project\"),\n            json: \"\",\n            width: category.width,\n            height: category.height\n        }, {\n            onSuccess: (param)=>{\n                let { data } = param;\n                router.push(\"/editor/\".concat(data.id));\n            },\n            onError: ()=>{\n                setLoading(false);\n                setSelectedCategory(null);\n            }\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900\",\n                        children: \"Choose a template size\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\template-categories.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Select the perfect canvas size for your design\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\template-categories.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\template-categories.tsx\",\n                lineNumber: 130,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-800\",\n                        children: \"Popular\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\template-categories.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                        children: templateCategories.filter((category)=>category.popular).map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TemplateCard, {\n                                category: category,\n                                isSelected: selectedCategory === category.id,\n                                isLoading: loading && selectedCategory === category.id,\n                                onClick: ()=>handleCategorySelect(category),\n                                disabled: loading\n                            }, category.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\template-categories.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\template-categories.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\template-categories.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-800\",\n                        children: \"All Templates\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\template-categories.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4\",\n                        children: templateCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TemplateCard, {\n                                category: category,\n                                isSelected: selectedCategory === category.id,\n                                isLoading: loading && selectedCategory === category.id,\n                                onClick: ()=>handleCategorySelect(category),\n                                disabled: loading,\n                                compact: true\n                            }, category.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\template-categories.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\template-categories.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\template-categories.tsx\",\n                lineNumber: 155,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\template-categories.tsx\",\n        lineNumber: 129,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TemplateCategories, \"Y4no2UY2hwCwbpDAW1d8pZCOeg8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        _features_projects_api_use_create_project__WEBPACK_IMPORTED_MODULE_3__.useCreateProject\n    ];\n});\n_c = TemplateCategories;\nconst TemplateCard = (param)=>{\n    let { category, isSelected, isLoading, onClick, disabled, compact = false } = param;\n    const Icon = category.icon;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n        variant: \"outline\",\n        onClick: onClick,\n        disabled: disabled,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"relative h-auto p-4 flex flex-col items-center space-y-3 transition-all duration-200 hover:shadow-md\", compact ? \"aspect-square\" : \"aspect-[4/3]\", isSelected && \"ring-2 ring-blue-500 bg-blue-50\", disabled && \"opacity-50 cursor-not-allowed\"),\n        children: [\n            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-white/80 flex items-center justify-center rounded-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_Facebook_FileText_Instagram_Loader2_Monitor_Palette_Smartphone_Square_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    className: \"size-6 animate-spin text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\template-categories.tsx\",\n                    lineNumber: 208,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\template-categories.tsx\",\n                lineNumber: 207,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"rounded-lg bg-gray-100 flex items-center justify-center\", compact ? \"size-8\" : \"size-12\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"text-gray-600\", compact ? \"size-4\" : \"size-6\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\template-categories.tsx\",\n                    lineNumber: 216,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\template-categories.tsx\",\n                lineNumber: 212,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"font-medium text-gray-900\", compact ? \"text-xs\" : \"text-sm\"),\n                        children: category.name\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\template-categories.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"text-gray-500\", compact ? \"text-xs\" : \"text-xs\"),\n                        children: category.description\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\template-categories.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\template-categories.tsx\",\n                lineNumber: 222,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\app\\\\(dashboard)\\\\template-categories.tsx\",\n        lineNumber: 195,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = TemplateCard;\nvar _c, _c1;\n$RefreshReg$(_c, \"TemplateCategories\");\n$RefreshReg$(_c1, \"TemplateCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/template-categories.tsx\n"));

/***/ })

});