"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[projectId]/page",{

/***/ "(app-pages-browser)/./src/features/editor/hooks/use-canvas-events.ts":
/*!********************************************************!*\
  !*** ./src/features/editor/hooks/use-canvas-events.ts ***!
  \********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCanvasEvents: function() { return /* binding */ useCanvasEvents; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst useCanvasEvents = (param)=>{\n    let { save, canvas, setSelectedObjects, clearSelectionCallback } = param;\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (canvas) {\n            // Object events\n            canvas.on(\"object:added\", ()=>save());\n            canvas.on(\"object:removed\", ()=>save());\n            canvas.on(\"object:modified\", ()=>save());\n            // Selection events\n            canvas.on(\"selection:created\", (e)=>{\n                setSelectedObjects(e.selected || []);\n            });\n            canvas.on(\"selection:updated\", (e)=>{\n                setSelectedObjects(e.selected || []);\n            });\n            canvas.on(\"selection:cleared\", ()=>{\n                setSelectedObjects([]);\n                clearSelectionCallback === null || clearSelectionCallback === void 0 ? void 0 : clearSelectionCallback();\n            });\n            // Mouse wheel zoom functionality\n            const handleMouseWheel = (opt)=>{\n                const delta = opt.e.deltaY;\n                let zoom = canvas.getZoom();\n                // Much more gentle zoom sensitivity for laptops\n                const zoomStep = 0.02; // Reduced from 0.1 to 0.02 for smoother control\n                if (delta < 0) {\n                    // Zoom in\n                    zoom += zoomStep;\n                } else {\n                    // Zoom out\n                    zoom -= zoomStep;\n                }\n                // Limit zoom levels (same as button zoom limits)\n                if (zoom > 1) zoom = 1;\n                if (zoom < 0.2) zoom = 0.2;\n                // Zoom to mouse pointer position\n                canvas.zoomToPoint({\n                    x: opt.e.offsetX,\n                    y: opt.e.offsetY\n                }, zoom);\n                opt.e.preventDefault();\n                opt.e.stopPropagation();\n            };\n            // Add mouse wheel event\n            canvas.on(\"mouse:wheel\", handleMouseWheel);\n        }\n        return ()=>{\n            if (canvas) {\n                canvas.off(\"object:added\");\n                canvas.off(\"object:removed\");\n                canvas.off(\"object:modified\");\n                canvas.off(\"selection:created\");\n                canvas.off(\"selection:updated\");\n                canvas.off(\"selection:cleared\");\n                canvas.off(\"mouse:wheel\");\n            }\n        };\n    }, [\n        save,\n        canvas,\n        clearSelectionCallback,\n        setSelectedObjects // No need for this, this is from setState\n    ]);\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/hooks/use-canvas-events.ts\n"));

/***/ })

});