"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/define-data-property";
exports.ids = ["vendor-chunks/define-data-property"];
exports.modules = {

/***/ "(rsc)/./node_modules/define-data-property/index.js":
/*!****************************************************!*\
  !*** ./node_modules/define-data-property/index.js ***!
  \****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar $defineProperty = __webpack_require__(/*! es-define-property */ \"(rsc)/./node_modules/es-define-property/index.js\");\n\nvar $SyntaxError = __webpack_require__(/*! es-errors/syntax */ \"(rsc)/./node_modules/es-errors/syntax.js\");\nvar $TypeError = __webpack_require__(/*! es-errors/type */ \"(rsc)/./node_modules/es-errors/type.js\");\n\nvar gopd = __webpack_require__(/*! gopd */ \"(rsc)/./node_modules/gopd/index.js\");\n\n/** @type {import('.')} */\nmodule.exports = function defineDataProperty(\n\tobj,\n\tproperty,\n\tvalue\n) {\n\tif (!obj || (typeof obj !== 'object' && typeof obj !== 'function')) {\n\t\tthrow new $TypeError('`obj` must be an object or a function`');\n\t}\n\tif (typeof property !== 'string' && typeof property !== 'symbol') {\n\t\tthrow new $TypeError('`property` must be a string or a symbol`');\n\t}\n\tif (arguments.length > 3 && typeof arguments[3] !== 'boolean' && arguments[3] !== null) {\n\t\tthrow new $TypeError('`nonEnumerable`, if provided, must be a boolean or null');\n\t}\n\tif (arguments.length > 4 && typeof arguments[4] !== 'boolean' && arguments[4] !== null) {\n\t\tthrow new $TypeError('`nonWritable`, if provided, must be a boolean or null');\n\t}\n\tif (arguments.length > 5 && typeof arguments[5] !== 'boolean' && arguments[5] !== null) {\n\t\tthrow new $TypeError('`nonConfigurable`, if provided, must be a boolean or null');\n\t}\n\tif (arguments.length > 6 && typeof arguments[6] !== 'boolean') {\n\t\tthrow new $TypeError('`loose`, if provided, must be a boolean');\n\t}\n\n\tvar nonEnumerable = arguments.length > 3 ? arguments[3] : null;\n\tvar nonWritable = arguments.length > 4 ? arguments[4] : null;\n\tvar nonConfigurable = arguments.length > 5 ? arguments[5] : null;\n\tvar loose = arguments.length > 6 ? arguments[6] : false;\n\n\t/* @type {false | TypedPropertyDescriptor<unknown>} */\n\tvar desc = !!gopd && gopd(obj, property);\n\n\tif ($defineProperty) {\n\t\t$defineProperty(obj, property, {\n\t\t\tconfigurable: nonConfigurable === null && desc ? desc.configurable : !nonConfigurable,\n\t\t\tenumerable: nonEnumerable === null && desc ? desc.enumerable : !nonEnumerable,\n\t\t\tvalue: value,\n\t\t\twritable: nonWritable === null && desc ? desc.writable : !nonWritable\n\t\t});\n\t} else if (loose || (!nonEnumerable && !nonWritable && !nonConfigurable)) {\n\t\t// must fall back to [[Set]], and was not explicitly asked to make non-enumerable, non-writable, or non-configurable\n\t\tobj[property] = value; // eslint-disable-line no-param-reassign\n\t} else {\n\t\tthrow new $SyntaxError('This environment does not support defining a property as non-configurable, non-writable, or non-enumerable.');\n\t}\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/define-data-property/index.js\n");

/***/ })

};
;