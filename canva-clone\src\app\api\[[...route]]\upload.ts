import { z } from "zod";
import { <PERSON><PERSON> } from "hono";
import { verifyAuth } from "@hono/auth-js";
import { zValidator } from "@hono/zod-validator";
import { writeFile } from "fs/promises";
import { join } from "path";
import { v4 as uuid } from "uuid";

import { db } from "@/db/drizzle";
import { uploadedImages } from "@/db/schema";

const app = new Hono()
  .post(
    "/",
    verifyAuth(),
    async (c) => {
      const auth = c.get("authUser");

      if (!auth.token?.id) {
        return c.json({ error: "Unauthorized" }, 401);
      }

      try {
        const formData = await c.req.formData();
        const file = formData.get("file") as File;

        if (!file) {
          return c.json({ error: "No file provided" }, 400);
        }

        // Validate file type
        if (!file.type.startsWith("image/")) {
          return c.json({ error: "File must be an image" }, 400);
        }

        // Validate file size (max 4MB)
        const maxSize = 4 * 1024 * 1024; // 4MB
        if (file.size > maxSize) {
          return c.json({ error: "File size must be less than 4MB" }, 400);
        }

        // Generate unique filename
        const fileExtension = file.name.split('.').pop() || 'jpg';
        const filename = `${uuid()}.${fileExtension}`;
        
        // Convert file to buffer
        const bytes = await file.arrayBuffer();
        const buffer = Buffer.from(bytes);

        // Save file to public/uploads directory
        const uploadPath = join(process.cwd(), "public", "uploads", filename);
        await writeFile(uploadPath, buffer);

        // Save file metadata to database
        const url = `/uploads/${filename}`;
        const [uploadedImage] = await db
          .insert(uploadedImages)
          .values({
            userId: auth.token.id,
            filename,
            originalName: file.name,
            url,
            size: file.size,
            mimeType: file.type,
            createdAt: new Date(),
          })
          .returning();

        return c.json({ 
          data: {
            id: uploadedImage.id,
            url: uploadedImage.url,
            filename: uploadedImage.filename,
            originalName: uploadedImage.originalName,
          }
        });

      } catch (error) {
        console.error("Upload error:", error);
        return c.json({ error: "Failed to upload file" }, 500);
      }
    }
  );

export default app;
