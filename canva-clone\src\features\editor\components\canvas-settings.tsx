"use client";

import { useState, useEffect, useMemo } from "react";
import { Palette, Maximize2 } from "lucide-react";

import { Editor } from "@/features/editor/types";
import { ColorPicker } from "@/features/editor/components/color-picker";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Separator } from "@/components/ui/separator";

interface CanvasSettingsProps {
  editor: Editor | undefined;
}

export const CanvasSettings = ({ editor }: CanvasSettingsProps) => {
  const workspace = editor?.getWorkspace();

  const initialWidth = useMemo(() => `${workspace?.width ?? 0}`, [workspace]);
  const initialHeight = useMemo(() => `${workspace?.height ?? 0}`, [workspace]);
  const initialBackground = useMemo(() => workspace?.fill ?? "#ffffff", [workspace]);

  const [width, setWidth] = useState(initialWidth);
  const [height, setHeight] = useState(initialHeight);
  const [background, setBackground] = useState(initialBackground);

  useEffect(() => {
    setWidth(initialWidth);
    setHeight(initialHeight);
    setBackground(initialBackground);
  }, [initialWidth, initialHeight, initialBackground]);

  const changeWidth = (value: string) => setWidth(value);
  const changeHeight = (value: string) => setHeight(value);
  const changeBackground = (value: string) => {
    setBackground(value);
    editor?.changeBackground(value);
  };

  const onSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    editor?.changeSize({
      width: parseInt(width, 10),
      height: parseInt(height, 10),
    });
  };

  return (
    <div className="flex items-center gap-x-2">
      {/* Background Color Picker */}
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-x-2 h-8"
          >
            <div
              className="w-4 h-4 rounded border border-gray-300"
              style={{ backgroundColor: background as string }}
            />
            <span className="text-xs">Background</span>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80" align="start">
          <div className="space-y-4">
            <div className="space-y-2">
              <h4 className="font-medium text-sm">Canvas Background</h4>
              <p className="text-xs text-muted-foreground">
                Choose a background color for your canvas
              </p>
            </div>
            <ColorPicker
              value={background as string}
              onChange={changeBackground}
            />
          </div>
        </PopoverContent>
      </Popover>

      {/* Canvas Size */}
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-x-2 h-8"
          >
            <Maximize2 className="w-4 h-4" />
            <span className="text-xs">{width} × {height}</span>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80" align="start">
          <div className="space-y-4">
            <div className="space-y-2">
              <h4 className="font-medium text-sm">Canvas Size</h4>
              <p className="text-xs text-muted-foreground">
                Adjust the dimensions of your canvas
              </p>
            </div>
            <form onSubmit={onSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="width" className="text-xs">
                    Width
                  </Label>
                  <Input
                    id="width"
                    placeholder="Width"
                    value={width}
                    type="number"
                    onChange={(e) => changeWidth(e.target.value)}
                    className="h-8 text-xs"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="height" className="text-xs">
                    Height
                  </Label>
                  <Input
                    id="height"
                    placeholder="Height"
                    value={height}
                    type="number"
                    onChange={(e) => changeHeight(e.target.value)}
                    className="h-8 text-xs"
                  />
                </div>
              </div>
              <Button type="submit" size="sm" className="w-full h-8 text-xs">
                Apply Size
              </Button>
            </form>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
};
