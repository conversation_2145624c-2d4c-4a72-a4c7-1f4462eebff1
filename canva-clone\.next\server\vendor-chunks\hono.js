"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hono";
exports.ids = ["vendor-chunks/hono"];
exports.modules = {

/***/ "(ssr)/./node_modules/hono/dist/client/client.js":
/*!*************************************************!*\
  !*** ./node_modules/hono/dist/client/client.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hc: () => (/* binding */ hc)\n/* harmony export */ });\n/* harmony import */ var _utils_cookie_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/cookie.js */ \"(ssr)/./node_modules/hono/dist/utils/cookie.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/hono/dist/client/utils.js\");\n// src/client/client.ts\n\n\nvar createProxy = (callback, path) => {\n  const proxy = new Proxy(() => {\n  }, {\n    get(_obj, key) {\n      if (typeof key !== \"string\" || key === \"then\") {\n        return void 0;\n      }\n      return createProxy(callback, [...path, key]);\n    },\n    apply(_1, _2, args) {\n      return callback({\n        path,\n        args\n      });\n    }\n  });\n  return proxy;\n};\nvar ClientRequestImpl = class {\n  url;\n  method;\n  queryParams = void 0;\n  pathParams = {};\n  rBody;\n  cType = void 0;\n  constructor(url, method) {\n    this.url = url;\n    this.method = method;\n  }\n  fetch = async (args, opt) => {\n    if (args) {\n      if (args.query) {\n        for (const [k, v] of Object.entries(args.query)) {\n          if (v === void 0) {\n            continue;\n          }\n          this.queryParams ||= new URLSearchParams();\n          if (Array.isArray(v)) {\n            for (const v2 of v) {\n              this.queryParams.append(k, v2);\n            }\n          } else {\n            this.queryParams.set(k, v);\n          }\n        }\n      }\n      if (args.form) {\n        const form = new FormData();\n        for (const [k, v] of Object.entries(args.form)) {\n          if (Array.isArray(v)) {\n            for (const v2 of v) {\n              form.append(k, v2);\n            }\n          } else {\n            form.append(k, v);\n          }\n        }\n        this.rBody = form;\n      }\n      if (args.json) {\n        this.rBody = JSON.stringify(args.json);\n        this.cType = \"application/json\";\n      }\n      if (args.param) {\n        this.pathParams = args.param;\n      }\n    }\n    let methodUpperCase = this.method.toUpperCase();\n    const headerValues = {\n      ...args?.header ?? {},\n      ...typeof opt?.headers === \"function\" ? await opt.headers() : opt?.headers ? opt.headers : {}\n    };\n    if (args?.cookie) {\n      const cookies = [];\n      for (const [key, value] of Object.entries(args.cookie)) {\n        cookies.push((0,_utils_cookie_js__WEBPACK_IMPORTED_MODULE_0__.serialize)(key, value, { path: \"/\" }));\n      }\n      headerValues[\"Cookie\"] = cookies.join(\",\");\n    }\n    if (this.cType) {\n      headerValues[\"Content-Type\"] = this.cType;\n    }\n    const headers = new Headers(headerValues ?? void 0);\n    let url = this.url;\n    url = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.removeIndexString)(url);\n    url = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.replaceUrlParam)(url, this.pathParams);\n    if (this.queryParams) {\n      url = url + \"?\" + this.queryParams.toString();\n    }\n    methodUpperCase = this.method.toUpperCase();\n    const setBody = !(methodUpperCase === \"GET\" || methodUpperCase === \"HEAD\");\n    return (opt?.fetch || fetch)(url, {\n      body: setBody ? this.rBody : void 0,\n      method: methodUpperCase,\n      headers,\n      ...opt?.init\n    });\n  };\n};\nvar hc = (baseUrl, options) => createProxy(function proxyCallback(opts) {\n  const parts = [...opts.path];\n  if (parts[parts.length - 1] === \"toString\") {\n    if (parts[parts.length - 2] === \"name\") {\n      return parts[parts.length - 3] || \"\";\n    }\n    return proxyCallback.toString();\n  }\n  if (parts[parts.length - 1] === \"valueOf\") {\n    if (parts[parts.length - 2] === \"name\") {\n      return parts[parts.length - 3] || \"\";\n    }\n    return proxyCallback;\n  }\n  let method = \"\";\n  if (/^\\$/.test(parts[parts.length - 1])) {\n    const last = parts.pop();\n    if (last) {\n      method = last.replace(/^\\$/, \"\");\n    }\n  }\n  const path = parts.join(\"/\");\n  const url = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.mergePath)(baseUrl, path);\n  if (method === \"url\") {\n    if (opts.args[0] && opts.args[0].param) {\n      return new URL((0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.replaceUrlParam)(url, opts.args[0].param));\n    }\n    return new URL(url);\n  }\n  if (method === \"ws\") {\n    const webSocketUrl = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.replaceUrlProtocol)(\n      opts.args[0] && opts.args[0].param ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.replaceUrlParam)(url, opts.args[0].param) : url,\n      \"ws\"\n    );\n    const targetUrl = new URL(webSocketUrl);\n    for (const key in opts.args[0]?.query) {\n      targetUrl.searchParams.set(key, opts.args[0].query[key]);\n    }\n    return new WebSocket(targetUrl.toString());\n  }\n  const req = new ClientRequestImpl(url, method);\n  if (method) {\n    options ??= {};\n    const args = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.deepMerge)(options, { ...opts.args[1] ?? {} });\n    return req.fetch(opts.args[0], args);\n  }\n  return req;\n}, []);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hono/dist/client/client.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hono/dist/client/index.js":
/*!************************************************!*\
  !*** ./node_modules/hono/dist/client/index.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hc: () => (/* reexport safe */ _client_js__WEBPACK_IMPORTED_MODULE_0__.hc)\n/* harmony export */ });\n/* harmony import */ var _client_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client.js */ \"(ssr)/./node_modules/hono/dist/client/client.js\");\n// src/client/index.ts\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaG9uby9kaXN0L2NsaWVudC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ2lDO0FBRy9CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGhlLWNhbnZhcy8uL25vZGVfbW9kdWxlcy9ob25vL2Rpc3QvY2xpZW50L2luZGV4LmpzPzY2ZGEiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL2NsaWVudC9pbmRleC50c1xuaW1wb3J0IHsgaGMgfSBmcm9tIFwiLi9jbGllbnQuanNcIjtcbmV4cG9ydCB7XG4gIGhjXG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hono/dist/client/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hono/dist/client/utils.js":
/*!************************************************!*\
  !*** ./node_modules/hono/dist/client/utils.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deepMerge: () => (/* binding */ deepMerge),\n/* harmony export */   mergePath: () => (/* binding */ mergePath),\n/* harmony export */   removeIndexString: () => (/* binding */ removeIndexString),\n/* harmony export */   replaceUrlParam: () => (/* binding */ replaceUrlParam),\n/* harmony export */   replaceUrlProtocol: () => (/* binding */ replaceUrlProtocol)\n/* harmony export */ });\n// src/client/utils.ts\nvar mergePath = (base, path) => {\n  base = base.replace(/\\/+$/, \"\");\n  base = base + \"/\";\n  path = path.replace(/^\\/+/, \"\");\n  return base + path;\n};\nvar replaceUrlParam = (urlString, params) => {\n  for (const [k, v] of Object.entries(params)) {\n    const reg = new RegExp(\"/:\" + k + \"(?:{[^/]+})?\");\n    urlString = urlString.replace(reg, `/${v}`);\n  }\n  return urlString;\n};\nvar replaceUrlProtocol = (urlString, protocol) => {\n  switch (protocol) {\n    case \"ws\":\n      return urlString.replace(/^http/, \"ws\");\n    case \"http\":\n      return urlString.replace(/^ws/, \"http\");\n  }\n};\nvar removeIndexString = (urlSting) => {\n  if (/^https?:\\/\\/[^\\/]+?\\/index$/.test(urlSting)) {\n    return urlSting.replace(/\\/index$/, \"/\");\n  }\n  return urlSting.replace(/\\/index$/, \"\");\n};\nfunction isObject(item) {\n  return typeof item === \"object\" && item !== null && !Array.isArray(item);\n}\nfunction deepMerge(target, source) {\n  if (!isObject(target) && !isObject(source)) {\n    return source;\n  }\n  const merged = { ...target };\n  for (const key in source) {\n    const value = source[key];\n    if (isObject(merged[key]) && isObject(value)) {\n      merged[key] = deepMerge(merged[key], value);\n    } else {\n      merged[key] = value;\n    }\n  }\n  return merged;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hono/dist/client/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hono/dist/utils/cookie.js":
/*!************************************************!*\
  !*** ./node_modules/hono/dist/utils/cookie.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parse: () => (/* binding */ parse),\n/* harmony export */   parseSigned: () => (/* binding */ parseSigned),\n/* harmony export */   serialize: () => (/* binding */ serialize),\n/* harmony export */   serializeSigned: () => (/* binding */ serializeSigned)\n/* harmony export */ });\n/* harmony import */ var _url_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./url.js */ \"(ssr)/./node_modules/hono/dist/utils/url.js\");\n// src/utils/cookie.ts\n\nvar algorithm = { name: \"HMAC\", hash: \"SHA-256\" };\nvar getCryptoKey = async (secret) => {\n  const secretBuf = typeof secret === \"string\" ? new TextEncoder().encode(secret) : secret;\n  return await crypto.subtle.importKey(\"raw\", secretBuf, algorithm, false, [\"sign\", \"verify\"]);\n};\nvar makeSignature = async (value, secret) => {\n  const key = await getCryptoKey(secret);\n  const signature = await crypto.subtle.sign(algorithm.name, key, new TextEncoder().encode(value));\n  return btoa(String.fromCharCode(...new Uint8Array(signature)));\n};\nvar verifySignature = async (base64Signature, value, secret) => {\n  try {\n    const signatureBinStr = atob(base64Signature);\n    const signature = new Uint8Array(signatureBinStr.length);\n    for (let i = 0, len = signatureBinStr.length; i < len; i++) {\n      signature[i] = signatureBinStr.charCodeAt(i);\n    }\n    return await crypto.subtle.verify(algorithm, secret, signature, new TextEncoder().encode(value));\n  } catch (e) {\n    return false;\n  }\n};\nvar validCookieNameRegEx = /^[\\w!#$%&'*.^`|~+-]+$/;\nvar validCookieValueRegEx = /^[ !#-:<-[\\]-~]*$/;\nvar parse = (cookie, name) => {\n  const pairs = cookie.trim().split(\";\");\n  return pairs.reduce((parsedCookie, pairStr) => {\n    pairStr = pairStr.trim();\n    const valueStartPos = pairStr.indexOf(\"=\");\n    if (valueStartPos === -1) {\n      return parsedCookie;\n    }\n    const cookieName = pairStr.substring(0, valueStartPos).trim();\n    if (name && name !== cookieName || !validCookieNameRegEx.test(cookieName)) {\n      return parsedCookie;\n    }\n    let cookieValue = pairStr.substring(valueStartPos + 1).trim();\n    if (cookieValue.startsWith('\"') && cookieValue.endsWith('\"')) {\n      cookieValue = cookieValue.slice(1, -1);\n    }\n    if (validCookieValueRegEx.test(cookieValue)) {\n      parsedCookie[cookieName] = (0,_url_js__WEBPACK_IMPORTED_MODULE_0__.decodeURIComponent_)(cookieValue);\n    }\n    return parsedCookie;\n  }, {});\n};\nvar parseSigned = async (cookie, secret, name) => {\n  const parsedCookie = {};\n  const secretKey = await getCryptoKey(secret);\n  for (const [key, value] of Object.entries(parse(cookie, name))) {\n    const signatureStartPos = value.lastIndexOf(\".\");\n    if (signatureStartPos < 1) {\n      continue;\n    }\n    const signedValue = value.substring(0, signatureStartPos);\n    const signature = value.substring(signatureStartPos + 1);\n    if (signature.length !== 44 || !signature.endsWith(\"=\")) {\n      continue;\n    }\n    const isVerified = await verifySignature(signature, signedValue, secretKey);\n    parsedCookie[key] = isVerified ? signedValue : false;\n  }\n  return parsedCookie;\n};\nvar _serialize = (name, value, opt = {}) => {\n  let cookie = `${name}=${value}`;\n  if (name.startsWith(\"__Secure-\") && !opt.secure) {\n    throw new Error(\"__Secure- Cookie must have Secure attributes\");\n  }\n  if (name.startsWith(\"__Host-\")) {\n    if (!opt.secure) {\n      throw new Error(\"__Host- Cookie must have Secure attributes\");\n    }\n    if (opt.path !== \"/\") {\n      throw new Error('__Host- Cookie must have Path attributes with \"/\"');\n    }\n    if (opt.domain) {\n      throw new Error(\"__Host- Cookie must not have Domain attributes\");\n    }\n  }\n  if (opt && typeof opt.maxAge === \"number\" && opt.maxAge >= 0) {\n    if (opt.maxAge > 3456e4) {\n      throw new Error(\n        \"Cookies Max-Age SHOULD NOT be greater than 400 days (34560000 seconds) in duration.\"\n      );\n    }\n    cookie += `; Max-Age=${Math.floor(opt.maxAge)}`;\n  }\n  if (opt.domain && opt.prefix !== \"host\") {\n    cookie += `; Domain=${opt.domain}`;\n  }\n  if (opt.path) {\n    cookie += `; Path=${opt.path}`;\n  }\n  if (opt.expires) {\n    if (opt.expires.getTime() - Date.now() > 3456e7) {\n      throw new Error(\n        \"Cookies Expires SHOULD NOT be greater than 400 days (34560000 seconds) in the future.\"\n      );\n    }\n    cookie += `; Expires=${opt.expires.toUTCString()}`;\n  }\n  if (opt.httpOnly) {\n    cookie += \"; HttpOnly\";\n  }\n  if (opt.secure) {\n    cookie += \"; Secure\";\n  }\n  if (opt.sameSite) {\n    cookie += `; SameSite=${opt.sameSite.charAt(0).toUpperCase() + opt.sameSite.slice(1)}`;\n  }\n  if (opt.partitioned) {\n    if (!opt.secure) {\n      throw new Error(\"Partitioned Cookie must have Secure attributes\");\n    }\n    cookie += \"; Partitioned\";\n  }\n  return cookie;\n};\nvar serialize = (name, value, opt) => {\n  value = encodeURIComponent(value);\n  return _serialize(name, value, opt);\n};\nvar serializeSigned = async (name, value, secret, opt = {}) => {\n  const signature = await makeSignature(value, secret);\n  value = `${value}.${signature}`;\n  value = encodeURIComponent(value);\n  return _serialize(name, value, opt);\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hono/dist/utils/cookie.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hono/dist/utils/url.js":
/*!*********************************************!*\
  !*** ./node_modules/hono/dist/utils/url.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkOptionalParameter: () => (/* binding */ checkOptionalParameter),\n/* harmony export */   decodeURIComponent_: () => (/* binding */ decodeURIComponent_),\n/* harmony export */   getPath: () => (/* binding */ getPath),\n/* harmony export */   getPathNoStrict: () => (/* binding */ getPathNoStrict),\n/* harmony export */   getPattern: () => (/* binding */ getPattern),\n/* harmony export */   getQueryParam: () => (/* binding */ getQueryParam),\n/* harmony export */   getQueryParams: () => (/* binding */ getQueryParams),\n/* harmony export */   getQueryStrings: () => (/* binding */ getQueryStrings),\n/* harmony export */   mergePath: () => (/* binding */ mergePath),\n/* harmony export */   splitPath: () => (/* binding */ splitPath),\n/* harmony export */   splitRoutingPath: () => (/* binding */ splitRoutingPath)\n/* harmony export */ });\n// src/utils/url.ts\nvar splitPath = (path) => {\n  const paths = path.split(\"/\");\n  if (paths[0] === \"\") {\n    paths.shift();\n  }\n  return paths;\n};\nvar splitRoutingPath = (routePath) => {\n  const { groups, path } = extractGroupsFromPath(routePath);\n  const paths = splitPath(path);\n  return replaceGroupMarks(paths, groups);\n};\nvar extractGroupsFromPath = (path) => {\n  const groups = [];\n  path = path.replace(/\\{[^}]+\\}/g, (match, index) => {\n    const mark = `@${index}`;\n    groups.push([mark, match]);\n    return mark;\n  });\n  return { groups, path };\n};\nvar replaceGroupMarks = (paths, groups) => {\n  for (let i = groups.length - 1; i >= 0; i--) {\n    const [mark] = groups[i];\n    for (let j = paths.length - 1; j >= 0; j--) {\n      if (paths[j].includes(mark)) {\n        paths[j] = paths[j].replace(mark, groups[i][1]);\n        break;\n      }\n    }\n  }\n  return paths;\n};\nvar patternCache = {};\nvar getPattern = (label) => {\n  if (label === \"*\") {\n    return \"*\";\n  }\n  const match = label.match(/^\\:([^\\{\\}]+)(?:\\{(.+)\\})?$/);\n  if (match) {\n    if (!patternCache[label]) {\n      if (match[2]) {\n        patternCache[label] = [label, match[1], new RegExp(\"^\" + match[2] + \"$\")];\n      } else {\n        patternCache[label] = [label, match[1], true];\n      }\n    }\n    return patternCache[label];\n  }\n  return null;\n};\nvar tryDecodeURI = (str) => {\n  try {\n    return decodeURI(str);\n  } catch {\n    return str.replace(/(?:%[0-9A-Fa-f]{2})+/g, (match) => {\n      try {\n        return decodeURI(match);\n      } catch {\n        return match;\n      }\n    });\n  }\n};\nvar getPath = (request) => {\n  const url = request.url;\n  const start = url.indexOf(\"/\", 8);\n  let i = start;\n  for (; i < url.length; i++) {\n    const charCode = url.charCodeAt(i);\n    if (charCode === 37) {\n      const queryIndex = url.indexOf(\"?\", i);\n      const path = url.slice(start, queryIndex === -1 ? void 0 : queryIndex);\n      return tryDecodeURI(path.includes(\"%25\") ? path.replace(/%25/g, \"%2525\") : path);\n    } else if (charCode === 63) {\n      break;\n    }\n  }\n  return url.slice(start, i);\n};\nvar getQueryStrings = (url) => {\n  const queryIndex = url.indexOf(\"?\", 8);\n  return queryIndex === -1 ? \"\" : \"?\" + url.slice(queryIndex + 1);\n};\nvar getPathNoStrict = (request) => {\n  const result = getPath(request);\n  return result.length > 1 && result[result.length - 1] === \"/\" ? result.slice(0, -1) : result;\n};\nvar mergePath = (...paths) => {\n  let p = \"\";\n  let endsWithSlash = false;\n  for (let path of paths) {\n    if (p[p.length - 1] === \"/\") {\n      p = p.slice(0, -1);\n      endsWithSlash = true;\n    }\n    if (path[0] !== \"/\") {\n      path = `/${path}`;\n    }\n    if (path === \"/\" && endsWithSlash) {\n      p = `${p}/`;\n    } else if (path !== \"/\") {\n      p = `${p}${path}`;\n    }\n    if (path === \"/\" && p === \"\") {\n      p = \"/\";\n    }\n  }\n  return p;\n};\nvar checkOptionalParameter = (path) => {\n  if (!path.match(/\\:.+\\?$/)) {\n    return null;\n  }\n  const segments = path.split(\"/\");\n  const results = [];\n  let basePath = \"\";\n  segments.forEach((segment) => {\n    if (segment !== \"\" && !/\\:/.test(segment)) {\n      basePath += \"/\" + segment;\n    } else if (/\\:/.test(segment)) {\n      if (/\\?/.test(segment)) {\n        if (results.length === 0 && basePath === \"\") {\n          results.push(\"/\");\n        } else {\n          results.push(basePath);\n        }\n        const optionalSegment = segment.replace(\"?\", \"\");\n        basePath += \"/\" + optionalSegment;\n        results.push(basePath);\n      } else {\n        basePath += \"/\" + segment;\n      }\n    }\n  });\n  return results.filter((v, i, a) => a.indexOf(v) === i);\n};\nvar _decodeURI = (value) => {\n  if (!/[%+]/.test(value)) {\n    return value;\n  }\n  if (value.indexOf(\"+\") !== -1) {\n    value = value.replace(/\\+/g, \" \");\n  }\n  return /%/.test(value) ? decodeURIComponent_(value) : value;\n};\nvar _getQueryParam = (url, key, multiple) => {\n  let encoded;\n  if (!multiple && key && !/[%+]/.test(key)) {\n    let keyIndex2 = url.indexOf(`?${key}`, 8);\n    if (keyIndex2 === -1) {\n      keyIndex2 = url.indexOf(`&${key}`, 8);\n    }\n    while (keyIndex2 !== -1) {\n      const trailingKeyCode = url.charCodeAt(keyIndex2 + key.length + 1);\n      if (trailingKeyCode === 61) {\n        const valueIndex = keyIndex2 + key.length + 2;\n        const endIndex = url.indexOf(\"&\", valueIndex);\n        return _decodeURI(url.slice(valueIndex, endIndex === -1 ? void 0 : endIndex));\n      } else if (trailingKeyCode == 38 || isNaN(trailingKeyCode)) {\n        return \"\";\n      }\n      keyIndex2 = url.indexOf(`&${key}`, keyIndex2 + 1);\n    }\n    encoded = /[%+]/.test(url);\n    if (!encoded) {\n      return void 0;\n    }\n  }\n  const results = {};\n  encoded ??= /[%+]/.test(url);\n  let keyIndex = url.indexOf(\"?\", 8);\n  while (keyIndex !== -1) {\n    const nextKeyIndex = url.indexOf(\"&\", keyIndex + 1);\n    let valueIndex = url.indexOf(\"=\", keyIndex);\n    if (valueIndex > nextKeyIndex && nextKeyIndex !== -1) {\n      valueIndex = -1;\n    }\n    let name = url.slice(\n      keyIndex + 1,\n      valueIndex === -1 ? nextKeyIndex === -1 ? void 0 : nextKeyIndex : valueIndex\n    );\n    if (encoded) {\n      name = _decodeURI(name);\n    }\n    keyIndex = nextKeyIndex;\n    if (name === \"\") {\n      continue;\n    }\n    let value;\n    if (valueIndex === -1) {\n      value = \"\";\n    } else {\n      value = url.slice(valueIndex + 1, nextKeyIndex === -1 ? void 0 : nextKeyIndex);\n      if (encoded) {\n        value = _decodeURI(value);\n      }\n    }\n    if (multiple) {\n      if (!(results[name] && Array.isArray(results[name]))) {\n        results[name] = [];\n      }\n      ;\n      results[name].push(value);\n    } else {\n      results[name] ??= value;\n    }\n  }\n  return key ? results[key] : results;\n};\nvar getQueryParam = _getQueryParam;\nvar getQueryParams = (url, key) => {\n  return _getQueryParam(url, key, true);\n};\nvar decodeURIComponent_ = decodeURIComponent;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hono/dist/utils/url.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/hono/dist/adapter/vercel/handler.js":
/*!**********************************************************!*\
  !*** ./node_modules/hono/dist/adapter/vercel/handler.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handle: () => (/* binding */ handle)\n/* harmony export */ });\n// src/adapter/vercel/handler.ts\nvar handle = (app) => (req, requestContext) => {\n  return app.fetch(req, {}, requestContext);\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvaG9uby9kaXN0L2FkYXB0ZXIvdmVyY2VsL2hhbmRsZXIuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQSwwQkFBMEI7QUFDMUI7QUFHRSIsInNvdXJjZXMiOlsid2VicGFjazovL3RoZS1jYW52YXMvLi9ub2RlX21vZHVsZXMvaG9uby9kaXN0L2FkYXB0ZXIvdmVyY2VsL2hhbmRsZXIuanM/MWEzYSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvYWRhcHRlci92ZXJjZWwvaGFuZGxlci50c1xudmFyIGhhbmRsZSA9IChhcHApID0+IChyZXEsIHJlcXVlc3RDb250ZXh0KSA9PiB7XG4gIHJldHVybiBhcHAuZmV0Y2gocmVxLCB7fSwgcmVxdWVzdENvbnRleHQpO1xufTtcbmV4cG9ydCB7XG4gIGhhbmRsZVxufTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/hono/dist/adapter/vercel/handler.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/hono/dist/adapter/vercel/index.js":
/*!********************************************************!*\
  !*** ./node_modules/hono/dist/adapter/vercel/index.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handle: () => (/* reexport safe */ _handler_js__WEBPACK_IMPORTED_MODULE_0__.handle)\n/* harmony export */ });\n/* harmony import */ var _handler_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./handler.js */ \"(rsc)/./node_modules/hono/dist/adapter/vercel/handler.js\");\n// src/adapter/vercel/index.ts\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvaG9uby9kaXN0L2FkYXB0ZXIvdmVyY2VsL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDc0M7QUFHcEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90aGUtY2FudmFzLy4vbm9kZV9tb2R1bGVzL2hvbm8vZGlzdC9hZGFwdGVyL3ZlcmNlbC9pbmRleC5qcz80NjA2Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy9hZGFwdGVyL3ZlcmNlbC9pbmRleC50c1xuaW1wb3J0IHsgaGFuZGxlIH0gZnJvbSBcIi4vaGFuZGxlci5qc1wiO1xuZXhwb3J0IHtcbiAgaGFuZGxlXG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/hono/dist/adapter/vercel/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/hono/dist/compose.js":
/*!*******************************************!*\
  !*** ./node_modules/hono/dist/compose.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   compose: () => (/* binding */ compose)\n/* harmony export */ });\n/* harmony import */ var _context_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./context.js */ \"(rsc)/./node_modules/hono/dist/context.js\");\n// src/compose.ts\n\nvar compose = (middleware, onError, onNotFound) => {\n  return (context, next) => {\n    let index = -1;\n    return dispatch(0);\n    async function dispatch(i) {\n      if (i <= index) {\n        throw new Error(\"next() called multiple times\");\n      }\n      index = i;\n      let res;\n      let isError = false;\n      let handler;\n      if (middleware[i]) {\n        handler = middleware[i][0][0];\n        if (context instanceof _context_js__WEBPACK_IMPORTED_MODULE_0__.Context) {\n          context.req.routeIndex = i;\n        }\n      } else {\n        handler = i === middleware.length && next || void 0;\n      }\n      if (!handler) {\n        if (context instanceof _context_js__WEBPACK_IMPORTED_MODULE_0__.Context && context.finalized === false && onNotFound) {\n          res = await onNotFound(context);\n        }\n      } else {\n        try {\n          res = await handler(context, () => {\n            return dispatch(i + 1);\n          });\n        } catch (err) {\n          if (err instanceof Error && context instanceof _context_js__WEBPACK_IMPORTED_MODULE_0__.Context && onError) {\n            context.error = err;\n            res = await onError(err, context);\n            isError = true;\n          } else {\n            throw err;\n          }\n        }\n      }\n      if (res && (context.finalized === false || isError)) {\n        context.res = res;\n      }\n      return context;\n    }\n  };\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/hono/dist/compose.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/hono/dist/context.js":
/*!*******************************************!*\
  !*** ./node_modules/hono/dist/context.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Context: () => (/* binding */ Context),\n/* harmony export */   TEXT_PLAIN: () => (/* binding */ TEXT_PLAIN)\n/* harmony export */ });\n/* harmony import */ var _request_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./request.js */ \"(rsc)/./node_modules/hono/dist/request.js\");\n/* harmony import */ var _utils_html_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/html.js */ \"(rsc)/./node_modules/hono/dist/utils/html.js\");\n// src/context.ts\n\n\nvar TEXT_PLAIN = \"text/plain; charset=UTF-8\";\nvar setHeaders = (headers, map = {}) => {\n  Object.entries(map).forEach(([key, value]) => headers.set(key, value));\n  return headers;\n};\nvar Context = class {\n  #rawRequest;\n  #req;\n  env = {};\n  #var;\n  finalized = false;\n  error;\n  #status = 200;\n  #executionCtx;\n  #headers;\n  #preparedHeaders;\n  #res;\n  #isFresh = true;\n  #layout;\n  #renderer;\n  #notFoundHandler;\n  #matchResult;\n  #path;\n  constructor(req, options) {\n    this.#rawRequest = req;\n    if (options) {\n      this.#executionCtx = options.executionCtx;\n      this.env = options.env;\n      this.#notFoundHandler = options.notFoundHandler;\n      this.#path = options.path;\n      this.#matchResult = options.matchResult;\n    }\n  }\n  get req() {\n    this.#req ??= new _request_js__WEBPACK_IMPORTED_MODULE_0__.HonoRequest(this.#rawRequest, this.#path, this.#matchResult);\n    return this.#req;\n  }\n  get event() {\n    if (this.#executionCtx && \"respondWith\" in this.#executionCtx) {\n      return this.#executionCtx;\n    } else {\n      throw Error(\"This context has no FetchEvent\");\n    }\n  }\n  get executionCtx() {\n    if (this.#executionCtx) {\n      return this.#executionCtx;\n    } else {\n      throw Error(\"This context has no ExecutionContext\");\n    }\n  }\n  get res() {\n    this.#isFresh = false;\n    return this.#res ||= new Response(\"404 Not Found\", { status: 404 });\n  }\n  set res(_res) {\n    this.#isFresh = false;\n    if (this.#res && _res) {\n      this.#res.headers.delete(\"content-type\");\n      for (const [k, v] of this.#res.headers.entries()) {\n        if (k === \"set-cookie\") {\n          const cookies = this.#res.headers.getSetCookie();\n          _res.headers.delete(\"set-cookie\");\n          for (const cookie of cookies) {\n            _res.headers.append(\"set-cookie\", cookie);\n          }\n        } else {\n          _res.headers.set(k, v);\n        }\n      }\n    }\n    this.#res = _res;\n    this.finalized = true;\n  }\n  render = (...args) => {\n    this.#renderer ??= (content) => this.html(content);\n    return this.#renderer(...args);\n  };\n  setLayout = (layout) => this.#layout = layout;\n  getLayout = () => this.#layout;\n  setRenderer = (renderer) => {\n    this.#renderer = renderer;\n  };\n  header = (name, value, options) => {\n    if (value === void 0) {\n      if (this.#headers) {\n        this.#headers.delete(name);\n      } else if (this.#preparedHeaders) {\n        delete this.#preparedHeaders[name.toLocaleLowerCase()];\n      }\n      if (this.finalized) {\n        this.res.headers.delete(name);\n      }\n      return;\n    }\n    if (options?.append) {\n      if (!this.#headers) {\n        this.#isFresh = false;\n        this.#headers = new Headers(this.#preparedHeaders);\n        this.#preparedHeaders = {};\n      }\n      this.#headers.append(name, value);\n    } else {\n      if (this.#headers) {\n        this.#headers.set(name, value);\n      } else {\n        this.#preparedHeaders ??= {};\n        this.#preparedHeaders[name.toLowerCase()] = value;\n      }\n    }\n    if (this.finalized) {\n      if (options?.append) {\n        this.res.headers.append(name, value);\n      } else {\n        this.res.headers.set(name, value);\n      }\n    }\n  };\n  status = (status) => {\n    this.#isFresh = false;\n    this.#status = status;\n  };\n  set = (key, value) => {\n    this.#var ??= {};\n    this.#var[key] = value;\n  };\n  get = (key) => {\n    return this.#var ? this.#var[key] : void 0;\n  };\n  get var() {\n    return { ...this.#var };\n  }\n  newResponse = (data, arg, headers) => {\n    if (this.#isFresh && !headers && !arg && this.#status === 200) {\n      return new Response(data, {\n        headers: this.#preparedHeaders\n      });\n    }\n    if (arg && typeof arg !== \"number\") {\n      const header = new Headers(arg.headers);\n      if (this.#headers) {\n        this.#headers.forEach((v, k) => {\n          if (k === \"set-cookie\") {\n            header.append(k, v);\n          } else {\n            header.set(k, v);\n          }\n        });\n      }\n      const headers2 = setHeaders(header, this.#preparedHeaders);\n      return new Response(data, {\n        headers: headers2,\n        status: arg.status ?? this.#status\n      });\n    }\n    const status = typeof arg === \"number\" ? arg : this.#status;\n    this.#preparedHeaders ??= {};\n    this.#headers ??= new Headers();\n    setHeaders(this.#headers, this.#preparedHeaders);\n    if (this.#res) {\n      this.#res.headers.forEach((v, k) => {\n        if (k === \"set-cookie\") {\n          this.#headers?.append(k, v);\n        } else {\n          this.#headers?.set(k, v);\n        }\n      });\n      setHeaders(this.#headers, this.#preparedHeaders);\n    }\n    headers ??= {};\n    for (const [k, v] of Object.entries(headers)) {\n      if (typeof v === \"string\") {\n        this.#headers.set(k, v);\n      } else {\n        this.#headers.delete(k);\n        for (const v2 of v) {\n          this.#headers.append(k, v2);\n        }\n      }\n    }\n    return new Response(data, {\n      status,\n      headers: this.#headers\n    });\n  };\n  body = (data, arg, headers) => {\n    return typeof arg === \"number\" ? this.newResponse(data, arg, headers) : this.newResponse(data, arg);\n  };\n  text = (text, arg, headers) => {\n    if (!this.#preparedHeaders) {\n      if (this.#isFresh && !headers && !arg) {\n        return new Response(text);\n      }\n      this.#preparedHeaders = {};\n    }\n    this.#preparedHeaders[\"content-type\"] = TEXT_PLAIN;\n    return typeof arg === \"number\" ? this.newResponse(text, arg, headers) : this.newResponse(text, arg);\n  };\n  json = (object, arg, headers) => {\n    const body = JSON.stringify(object);\n    this.#preparedHeaders ??= {};\n    this.#preparedHeaders[\"content-type\"] = \"application/json; charset=UTF-8\";\n    return typeof arg === \"number\" ? this.newResponse(body, arg, headers) : this.newResponse(body, arg);\n  };\n  html = (html, arg, headers) => {\n    this.#preparedHeaders ??= {};\n    this.#preparedHeaders[\"content-type\"] = \"text/html; charset=UTF-8\";\n    if (typeof html === \"object\") {\n      if (!(html instanceof Promise)) {\n        html = html.toString();\n      }\n      if (html instanceof Promise) {\n        return html.then((html2) => (0,_utils_html_js__WEBPACK_IMPORTED_MODULE_1__.resolveCallback)(html2, _utils_html_js__WEBPACK_IMPORTED_MODULE_1__.HtmlEscapedCallbackPhase.Stringify, false, {})).then((html2) => {\n          return typeof arg === \"number\" ? this.newResponse(html2, arg, headers) : this.newResponse(html2, arg);\n        });\n      }\n    }\n    return typeof arg === \"number\" ? this.newResponse(html, arg, headers) : this.newResponse(html, arg);\n  };\n  redirect = (location, status) => {\n    this.#headers ??= new Headers();\n    this.#headers.set(\"Location\", location);\n    return this.newResponse(null, status ?? 302);\n  };\n  notFound = () => {\n    this.#notFoundHandler ??= () => new Response();\n    return this.#notFoundHandler(this);\n  };\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/hono/dist/context.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/hono/dist/helper/adapter/index.js":
/*!********************************************************!*\
  !*** ./node_modules/hono/dist/helper/adapter/index.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkUserAgentEquals: () => (/* binding */ checkUserAgentEquals),\n/* harmony export */   env: () => (/* binding */ env),\n/* harmony export */   getRuntimeKey: () => (/* binding */ getRuntimeKey),\n/* harmony export */   knownUserAgents: () => (/* binding */ knownUserAgents)\n/* harmony export */ });\n// src/helper/adapter/index.ts\nvar env = (c, runtime) => {\n  const global = globalThis;\n  const globalEnv = global?.process?.env;\n  runtime ??= getRuntimeKey();\n  const runtimeEnvHandlers = {\n    bun: () => globalEnv,\n    node: () => globalEnv,\n    \"edge-light\": () => globalEnv,\n    deno: () => {\n      return Deno.env.toObject();\n    },\n    workerd: () => c.env,\n    fastly: () => ({}),\n    other: () => ({})\n  };\n  return runtimeEnvHandlers[runtime]();\n};\nvar knownUserAgents = {\n  deno: \"Deno\",\n  bun: \"Bun\",\n  workerd: \"Cloudflare-Workers\",\n  node: \"Node.js\"\n};\nvar getRuntimeKey = () => {\n  const global = globalThis;\n  const userAgentSupported = typeof navigator !== \"undefined\" && typeof navigator.userAgent === \"string\";\n  if (userAgentSupported) {\n    for (const [runtimeKey, userAgent] of Object.entries(knownUserAgents)) {\n      if (checkUserAgentEquals(userAgent)) {\n        return runtimeKey;\n      }\n    }\n  }\n  if (typeof global?.EdgeRuntime === \"string\") {\n    return \"edge-light\";\n  }\n  if (global?.fastly !== void 0) {\n    return \"fastly\";\n  }\n  if (global?.process?.release?.name === \"node\") {\n    return \"node\";\n  }\n  return \"other\";\n};\nvar checkUserAgentEquals = (platform) => {\n  const userAgent = navigator.userAgent;\n  return userAgent.startsWith(platform);\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/hono/dist/helper/adapter/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/hono/dist/helper/cookie/index.js":
/*!*******************************************************!*\
  !*** ./node_modules/hono/dist/helper/cookie/index.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deleteCookie: () => (/* binding */ deleteCookie),\n/* harmony export */   getCookie: () => (/* binding */ getCookie),\n/* harmony export */   getSignedCookie: () => (/* binding */ getSignedCookie),\n/* harmony export */   setCookie: () => (/* binding */ setCookie),\n/* harmony export */   setSignedCookie: () => (/* binding */ setSignedCookie)\n/* harmony export */ });\n/* harmony import */ var _utils_cookie_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../utils/cookie.js */ \"(rsc)/./node_modules/hono/dist/utils/cookie.js\");\n// src/helper/cookie/index.ts\n\nvar getCookie = (c, key, prefix) => {\n  const cookie = c.req.raw.headers.get(\"Cookie\");\n  if (typeof key === \"string\") {\n    if (!cookie) {\n      return void 0;\n    }\n    let finalKey = key;\n    if (prefix === \"secure\") {\n      finalKey = \"__Secure-\" + key;\n    } else if (prefix === \"host\") {\n      finalKey = \"__Host-\" + key;\n    }\n    const obj2 = (0,_utils_cookie_js__WEBPACK_IMPORTED_MODULE_0__.parse)(cookie, finalKey);\n    return obj2[finalKey];\n  }\n  if (!cookie) {\n    return {};\n  }\n  const obj = (0,_utils_cookie_js__WEBPACK_IMPORTED_MODULE_0__.parse)(cookie);\n  return obj;\n};\nvar getSignedCookie = async (c, secret, key, prefix) => {\n  const cookie = c.req.raw.headers.get(\"Cookie\");\n  if (typeof key === \"string\") {\n    if (!cookie) {\n      return void 0;\n    }\n    let finalKey = key;\n    if (prefix === \"secure\") {\n      finalKey = \"__Secure-\" + key;\n    } else if (prefix === \"host\") {\n      finalKey = \"__Host-\" + key;\n    }\n    const obj2 = await (0,_utils_cookie_js__WEBPACK_IMPORTED_MODULE_0__.parseSigned)(cookie, secret, finalKey);\n    return obj2[finalKey];\n  }\n  if (!cookie) {\n    return {};\n  }\n  const obj = await (0,_utils_cookie_js__WEBPACK_IMPORTED_MODULE_0__.parseSigned)(cookie, secret);\n  return obj;\n};\nvar setCookie = (c, name, value, opt) => {\n  let cookie;\n  if (opt?.prefix === \"secure\") {\n    cookie = (0,_utils_cookie_js__WEBPACK_IMPORTED_MODULE_0__.serialize)(\"__Secure-\" + name, value, { path: \"/\", ...opt, secure: true });\n  } else if (opt?.prefix === \"host\") {\n    cookie = (0,_utils_cookie_js__WEBPACK_IMPORTED_MODULE_0__.serialize)(\"__Host-\" + name, value, {\n      ...opt,\n      path: \"/\",\n      secure: true,\n      domain: void 0\n    });\n  } else {\n    cookie = (0,_utils_cookie_js__WEBPACK_IMPORTED_MODULE_0__.serialize)(name, value, { path: \"/\", ...opt });\n  }\n  c.header(\"set-cookie\", cookie, { append: true });\n};\nvar setSignedCookie = async (c, name, value, secret, opt) => {\n  let cookie;\n  if (opt?.prefix === \"secure\") {\n    cookie = await (0,_utils_cookie_js__WEBPACK_IMPORTED_MODULE_0__.serializeSigned)(\"__Secure-\" + name, value, secret, {\n      path: \"/\",\n      ...opt,\n      secure: true\n    });\n  } else if (opt?.prefix === \"host\") {\n    cookie = await (0,_utils_cookie_js__WEBPACK_IMPORTED_MODULE_0__.serializeSigned)(\"__Host-\" + name, value, secret, {\n      ...opt,\n      path: \"/\",\n      secure: true,\n      domain: void 0\n    });\n  } else {\n    cookie = await (0,_utils_cookie_js__WEBPACK_IMPORTED_MODULE_0__.serializeSigned)(name, value, secret, { path: \"/\", ...opt });\n  }\n  c.header(\"set-cookie\", cookie, { append: true });\n};\nvar deleteCookie = (c, name, opt) => {\n  const deletedCookie = getCookie(c, name);\n  setCookie(c, name, \"\", { ...opt, maxAge: 0 });\n  return deletedCookie;\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/hono/dist/helper/cookie/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/hono/dist/hono-base.js":
/*!*********************************************!*\
  !*** ./node_modules/hono/dist/hono-base.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   COMPOSED_HANDLER: () => (/* binding */ COMPOSED_HANDLER),\n/* harmony export */   HonoBase: () => (/* binding */ Hono)\n/* harmony export */ });\n/* harmony import */ var _compose_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./compose.js */ \"(rsc)/./node_modules/hono/dist/compose.js\");\n/* harmony import */ var _context_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./context.js */ \"(rsc)/./node_modules/hono/dist/context.js\");\n/* harmony import */ var _router_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./router.js */ \"(rsc)/./node_modules/hono/dist/router.js\");\n/* harmony import */ var _utils_url_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils/url.js */ \"(rsc)/./node_modules/hono/dist/utils/url.js\");\n// src/hono-base.ts\n\n\n\n\nvar COMPOSED_HANDLER = Symbol(\"composedHandler\");\nvar notFoundHandler = (c) => {\n  return c.text(\"404 Not Found\", 404);\n};\nvar errorHandler = (err, c) => {\n  if (\"getResponse\" in err) {\n    return err.getResponse();\n  }\n  console.error(err);\n  return c.text(\"Internal Server Error\", 500);\n};\nvar Hono = class {\n  get;\n  post;\n  put;\n  delete;\n  options;\n  patch;\n  all;\n  on;\n  use;\n  router;\n  getPath;\n  _basePath = \"/\";\n  #path = \"/\";\n  routes = [];\n  constructor(options = {}) {\n    const allMethods = [..._router_js__WEBPACK_IMPORTED_MODULE_2__.METHODS, _router_js__WEBPACK_IMPORTED_MODULE_2__.METHOD_NAME_ALL_LOWERCASE];\n    allMethods.forEach((method) => {\n      this[method] = (args1, ...args) => {\n        if (typeof args1 === \"string\") {\n          this.#path = args1;\n        } else {\n          this.addRoute(method, this.#path, args1);\n        }\n        args.forEach((handler) => {\n          if (typeof handler !== \"string\") {\n            this.addRoute(method, this.#path, handler);\n          }\n        });\n        return this;\n      };\n    });\n    this.on = (method, path, ...handlers) => {\n      for (const p of [path].flat()) {\n        this.#path = p;\n        for (const m of [method].flat()) {\n          handlers.map((handler) => {\n            this.addRoute(m.toUpperCase(), this.#path, handler);\n          });\n        }\n      }\n      return this;\n    };\n    this.use = (arg1, ...handlers) => {\n      if (typeof arg1 === \"string\") {\n        this.#path = arg1;\n      } else {\n        this.#path = \"*\";\n        handlers.unshift(arg1);\n      }\n      handlers.forEach((handler) => {\n        this.addRoute(_router_js__WEBPACK_IMPORTED_MODULE_2__.METHOD_NAME_ALL, this.#path, handler);\n      });\n      return this;\n    };\n    const strict = options.strict ?? true;\n    delete options.strict;\n    Object.assign(this, options);\n    this.getPath = strict ? options.getPath ?? _utils_url_js__WEBPACK_IMPORTED_MODULE_3__.getPath : _utils_url_js__WEBPACK_IMPORTED_MODULE_3__.getPathNoStrict;\n  }\n  clone() {\n    const clone = new Hono({\n      router: this.router,\n      getPath: this.getPath\n    });\n    clone.routes = this.routes;\n    return clone;\n  }\n  notFoundHandler = notFoundHandler;\n  errorHandler = errorHandler;\n  route(path, app) {\n    const subApp = this.basePath(path);\n    app.routes.map((r) => {\n      let handler;\n      if (app.errorHandler === errorHandler) {\n        handler = r.handler;\n      } else {\n        handler = async (c, next) => (await (0,_compose_js__WEBPACK_IMPORTED_MODULE_0__.compose)([], app.errorHandler)(c, () => r.handler(c, next))).res;\n        handler[COMPOSED_HANDLER] = r.handler;\n      }\n      subApp.addRoute(r.method, r.path, handler);\n    });\n    return this;\n  }\n  basePath(path) {\n    const subApp = this.clone();\n    subApp._basePath = (0,_utils_url_js__WEBPACK_IMPORTED_MODULE_3__.mergePath)(this._basePath, path);\n    return subApp;\n  }\n  onError = (handler) => {\n    this.errorHandler = handler;\n    return this;\n  };\n  notFound = (handler) => {\n    this.notFoundHandler = handler;\n    return this;\n  };\n  mount(path, applicationHandler, options) {\n    let replaceRequest;\n    let optionHandler;\n    if (options) {\n      if (typeof options === \"function\") {\n        optionHandler = options;\n      } else {\n        optionHandler = options.optionHandler;\n        replaceRequest = options.replaceRequest;\n      }\n    }\n    const getOptions = optionHandler ? (c) => {\n      const options2 = optionHandler(c);\n      return Array.isArray(options2) ? options2 : [options2];\n    } : (c) => {\n      let executionContext = void 0;\n      try {\n        executionContext = c.executionCtx;\n      } catch {\n      }\n      return [c.env, executionContext];\n    };\n    replaceRequest ||= (() => {\n      const mergedPath = (0,_utils_url_js__WEBPACK_IMPORTED_MODULE_3__.mergePath)(this._basePath, path);\n      const pathPrefixLength = mergedPath === \"/\" ? 0 : mergedPath.length;\n      return (request) => {\n        const url = new URL(request.url);\n        url.pathname = url.pathname.slice(pathPrefixLength) || \"/\";\n        return new Request(url, request);\n      };\n    })();\n    const handler = async (c, next) => {\n      const res = await applicationHandler(replaceRequest(c.req.raw), ...getOptions(c));\n      if (res) {\n        return res;\n      }\n      await next();\n    };\n    this.addRoute(_router_js__WEBPACK_IMPORTED_MODULE_2__.METHOD_NAME_ALL, (0,_utils_url_js__WEBPACK_IMPORTED_MODULE_3__.mergePath)(path, \"*\"), handler);\n    return this;\n  }\n  addRoute(method, path, handler) {\n    method = method.toUpperCase();\n    path = (0,_utils_url_js__WEBPACK_IMPORTED_MODULE_3__.mergePath)(this._basePath, path);\n    const r = { path, method, handler };\n    this.router.add(method, path, [handler, r]);\n    this.routes.push(r);\n  }\n  matchRoute(method, path) {\n    return this.router.match(method, path);\n  }\n  handleError(err, c) {\n    if (err instanceof Error) {\n      return this.errorHandler(err, c);\n    }\n    throw err;\n  }\n  dispatch(request, executionCtx, env, method) {\n    if (method === \"HEAD\") {\n      return (async () => new Response(null, await this.dispatch(request, executionCtx, env, \"GET\")))();\n    }\n    const path = this.getPath(request, { env });\n    const matchResult = this.matchRoute(method, path);\n    const c = new _context_js__WEBPACK_IMPORTED_MODULE_1__.Context(request, {\n      path,\n      matchResult,\n      env,\n      executionCtx,\n      notFoundHandler: this.notFoundHandler\n    });\n    if (matchResult[0].length === 1) {\n      let res;\n      try {\n        res = matchResult[0][0][0][0](c, async () => {\n          c.res = await this.notFoundHandler(c);\n        });\n      } catch (err) {\n        return this.handleError(err, c);\n      }\n      return res instanceof Promise ? res.then(\n        (resolved) => resolved || (c.finalized ? c.res : this.notFoundHandler(c))\n      ).catch((err) => this.handleError(err, c)) : res ?? this.notFoundHandler(c);\n    }\n    const composed = (0,_compose_js__WEBPACK_IMPORTED_MODULE_0__.compose)(matchResult[0], this.errorHandler, this.notFoundHandler);\n    return (async () => {\n      try {\n        const context = await composed(c);\n        if (!context.finalized) {\n          throw new Error(\n            \"Context is not finalized. Did you forget to return a Response object or `await next()`?\"\n          );\n        }\n        return context.res;\n      } catch (err) {\n        return this.handleError(err, c);\n      }\n    })();\n  }\n  fetch = (request, ...rest) => {\n    return this.dispatch(request, rest[1], rest[0], request.method);\n  };\n  request = (input, requestInit, Env, executionCtx) => {\n    if (input instanceof Request) {\n      if (requestInit !== void 0) {\n        input = new Request(input, requestInit);\n      }\n      return this.fetch(input, Env, executionCtx);\n    }\n    input = input.toString();\n    const path = /^https?:\\/\\//.test(input) ? input : `http://localhost${(0,_utils_url_js__WEBPACK_IMPORTED_MODULE_3__.mergePath)(\"/\", input)}`;\n    const req = new Request(path, requestInit);\n    return this.fetch(req, Env, executionCtx);\n  };\n  fire = () => {\n    addEventListener(\"fetch\", (event) => {\n      event.respondWith(this.dispatch(event.request, event, void 0, event.request.method));\n    });\n  };\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/hono/dist/hono-base.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/hono/dist/hono.js":
/*!****************************************!*\
  !*** ./node_modules/hono/dist/hono.js ***!
  \****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Hono: () => (/* binding */ Hono)\n/* harmony export */ });\n/* harmony import */ var _hono_base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./hono-base.js */ \"(rsc)/./node_modules/hono/dist/hono-base.js\");\n/* harmony import */ var _router_reg_exp_router_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./router/reg-exp-router/index.js */ \"(rsc)/./node_modules/hono/dist/router/reg-exp-router/index.js\");\n/* harmony import */ var _router_smart_router_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./router/smart-router/index.js */ \"(rsc)/./node_modules/hono/dist/router/smart-router/index.js\");\n/* harmony import */ var _router_trie_router_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./router/trie-router/index.js */ \"(rsc)/./node_modules/hono/dist/router/trie-router/index.js\");\n// src/hono.ts\n\n\n\n\nvar Hono = class extends _hono_base_js__WEBPACK_IMPORTED_MODULE_0__.HonoBase {\n  constructor(options = {}) {\n    super(options);\n    this.router = options.router ?? new _router_smart_router_index_js__WEBPACK_IMPORTED_MODULE_2__.SmartRouter({\n      routers: [new _router_reg_exp_router_index_js__WEBPACK_IMPORTED_MODULE_1__.RegExpRouter(), new _router_trie_router_index_js__WEBPACK_IMPORTED_MODULE_3__.TrieRouter()]\n    });\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvaG9uby9kaXN0L2hvbm8uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBQTtBQUMwQztBQUNzQjtBQUNIO0FBQ0Y7QUFDM0QseUJBQXlCLG1EQUFRO0FBQ2pDLDBCQUEwQjtBQUMxQjtBQUNBLHdDQUF3QyxzRUFBVztBQUNuRCxvQkFBb0IseUVBQVksUUFBUSxvRUFBVTtBQUNsRCxLQUFLO0FBQ0w7QUFDQTtBQUdFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGhlLWNhbnZhcy8uL25vZGVfbW9kdWxlcy9ob25vL2Rpc3QvaG9uby5qcz8zNzBmIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy9ob25vLnRzXG5pbXBvcnQgeyBIb25vQmFzZSB9IGZyb20gXCIuL2hvbm8tYmFzZS5qc1wiO1xuaW1wb3J0IHsgUmVnRXhwUm91dGVyIH0gZnJvbSBcIi4vcm91dGVyL3JlZy1leHAtcm91dGVyL2luZGV4LmpzXCI7XG5pbXBvcnQgeyBTbWFydFJvdXRlciB9IGZyb20gXCIuL3JvdXRlci9zbWFydC1yb3V0ZXIvaW5kZXguanNcIjtcbmltcG9ydCB7IFRyaWVSb3V0ZXIgfSBmcm9tIFwiLi9yb3V0ZXIvdHJpZS1yb3V0ZXIvaW5kZXguanNcIjtcbnZhciBIb25vID0gY2xhc3MgZXh0ZW5kcyBIb25vQmFzZSB7XG4gIGNvbnN0cnVjdG9yKG9wdGlvbnMgPSB7fSkge1xuICAgIHN1cGVyKG9wdGlvbnMpO1xuICAgIHRoaXMucm91dGVyID0gb3B0aW9ucy5yb3V0ZXIgPz8gbmV3IFNtYXJ0Um91dGVyKHtcbiAgICAgIHJvdXRlcnM6IFtuZXcgUmVnRXhwUm91dGVyKCksIG5ldyBUcmllUm91dGVyKCldXG4gICAgfSk7XG4gIH1cbn07XG5leHBvcnQge1xuICBIb25vXG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/hono/dist/hono.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/hono/dist/http-exception.js":
/*!**************************************************!*\
  !*** ./node_modules/hono/dist/http-exception.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HTTPException: () => (/* binding */ HTTPException)\n/* harmony export */ });\n// src/http-exception.ts\nvar HTTPException = class extends Error {\n  res;\n  status;\n  constructor(status = 500, options) {\n    super(options?.message, { cause: options?.cause });\n    this.res = options?.res;\n    this.status = status;\n  }\n  getResponse() {\n    if (this.res) {\n      const newResponse = new Response(this.res.body, {\n        status: this.status,\n        headers: this.res.headers\n      });\n      return newResponse;\n    }\n    return new Response(this.message, {\n      status: this.status\n    });\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvaG9uby9kaXN0L2h0dHAtZXhjZXB0aW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOEJBQThCLHVCQUF1QjtBQUNyRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFHRSIsInNvdXJjZXMiOlsid2VicGFjazovL3RoZS1jYW52YXMvLi9ub2RlX21vZHVsZXMvaG9uby9kaXN0L2h0dHAtZXhjZXB0aW9uLmpzPzM3OWYiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL2h0dHAtZXhjZXB0aW9uLnRzXG52YXIgSFRUUEV4Y2VwdGlvbiA9IGNsYXNzIGV4dGVuZHMgRXJyb3Ige1xuICByZXM7XG4gIHN0YXR1cztcbiAgY29uc3RydWN0b3Ioc3RhdHVzID0gNTAwLCBvcHRpb25zKSB7XG4gICAgc3VwZXIob3B0aW9ucz8ubWVzc2FnZSwgeyBjYXVzZTogb3B0aW9ucz8uY2F1c2UgfSk7XG4gICAgdGhpcy5yZXMgPSBvcHRpb25zPy5yZXM7XG4gICAgdGhpcy5zdGF0dXMgPSBzdGF0dXM7XG4gIH1cbiAgZ2V0UmVzcG9uc2UoKSB7XG4gICAgaWYgKHRoaXMucmVzKSB7XG4gICAgICBjb25zdCBuZXdSZXNwb25zZSA9IG5ldyBSZXNwb25zZSh0aGlzLnJlcy5ib2R5LCB7XG4gICAgICAgIHN0YXR1czogdGhpcy5zdGF0dXMsXG4gICAgICAgIGhlYWRlcnM6IHRoaXMucmVzLmhlYWRlcnNcbiAgICAgIH0pO1xuICAgICAgcmV0dXJuIG5ld1Jlc3BvbnNlO1xuICAgIH1cbiAgICByZXR1cm4gbmV3IFJlc3BvbnNlKHRoaXMubWVzc2FnZSwge1xuICAgICAgc3RhdHVzOiB0aGlzLnN0YXR1c1xuICAgIH0pO1xuICB9XG59O1xuZXhwb3J0IHtcbiAgSFRUUEV4Y2VwdGlvblxufTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/hono/dist/http-exception.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/hono/dist/index.js":
/*!*****************************************!*\
  !*** ./node_modules/hono/dist/index.js ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Hono: () => (/* reexport safe */ _hono_js__WEBPACK_IMPORTED_MODULE_0__.Hono)\n/* harmony export */ });\n/* harmony import */ var _hono_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./hono.js */ \"(rsc)/./node_modules/hono/dist/hono.js\");\n// src/index.ts\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvaG9uby9kaXN0L2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDaUM7QUFHL0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90aGUtY2FudmFzLy4vbm9kZV9tb2R1bGVzL2hvbm8vZGlzdC9pbmRleC5qcz9hMTllIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy9pbmRleC50c1xuaW1wb3J0IHsgSG9ubyB9IGZyb20gXCIuL2hvbm8uanNcIjtcbmV4cG9ydCB7XG4gIEhvbm9cbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/hono/dist/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/hono/dist/request.js":
/*!*******************************************!*\
  !*** ./node_modules/hono/dist/request.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HonoRequest: () => (/* binding */ HonoRequest)\n/* harmony export */ });\n/* harmony import */ var _utils_body_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/body.js */ \"(rsc)/./node_modules/hono/dist/utils/body.js\");\n/* harmony import */ var _utils_url_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/url.js */ \"(rsc)/./node_modules/hono/dist/utils/url.js\");\n// src/request.ts\n\n\nvar HonoRequest = class {\n  raw;\n  #validatedData;\n  #matchResult;\n  routeIndex = 0;\n  path;\n  bodyCache = {};\n  constructor(request, path = \"/\", matchResult = [[]]) {\n    this.raw = request;\n    this.path = path;\n    this.#matchResult = matchResult;\n    this.#validatedData = {};\n  }\n  param(key) {\n    return key ? this.getDecodedParam(key) : this.getAllDecodedParams();\n  }\n  getDecodedParam(key) {\n    const paramKey = this.#matchResult[0][this.routeIndex][1][key];\n    const param = this.getParamValue(paramKey);\n    return param ? /\\%/.test(param) ? (0,_utils_url_js__WEBPACK_IMPORTED_MODULE_1__.decodeURIComponent_)(param) : param : void 0;\n  }\n  getAllDecodedParams() {\n    const decoded = {};\n    const keys = Object.keys(this.#matchResult[0][this.routeIndex][1]);\n    for (const key of keys) {\n      const value = this.getParamValue(this.#matchResult[0][this.routeIndex][1][key]);\n      if (value && typeof value === \"string\") {\n        decoded[key] = /\\%/.test(value) ? (0,_utils_url_js__WEBPACK_IMPORTED_MODULE_1__.decodeURIComponent_)(value) : value;\n      }\n    }\n    return decoded;\n  }\n  getParamValue(paramKey) {\n    return this.#matchResult[1] ? this.#matchResult[1][paramKey] : paramKey;\n  }\n  query(key) {\n    return (0,_utils_url_js__WEBPACK_IMPORTED_MODULE_1__.getQueryParam)(this.url, key);\n  }\n  queries(key) {\n    return (0,_utils_url_js__WEBPACK_IMPORTED_MODULE_1__.getQueryParams)(this.url, key);\n  }\n  header(name) {\n    if (name) {\n      return this.raw.headers.get(name.toLowerCase()) ?? void 0;\n    }\n    const headerData = {};\n    this.raw.headers.forEach((value, key) => {\n      headerData[key] = value;\n    });\n    return headerData;\n  }\n  async parseBody(options) {\n    return this.bodyCache.parsedBody ??= await (0,_utils_body_js__WEBPACK_IMPORTED_MODULE_0__.parseBody)(this, options);\n  }\n  cachedBody = (key) => {\n    const { bodyCache, raw } = this;\n    const cachedBody = bodyCache[key];\n    if (cachedBody) {\n      return cachedBody;\n    }\n    const anyCachedKey = Object.keys(bodyCache)[0];\n    if (anyCachedKey) {\n      return bodyCache[anyCachedKey].then((body) => {\n        if (anyCachedKey === \"json\") {\n          body = JSON.stringify(body);\n        }\n        return new Response(body)[key]();\n      });\n    }\n    return bodyCache[key] = raw[key]();\n  };\n  json() {\n    return this.cachedBody(\"json\");\n  }\n  text() {\n    return this.cachedBody(\"text\");\n  }\n  arrayBuffer() {\n    return this.cachedBody(\"arrayBuffer\");\n  }\n  blob() {\n    return this.cachedBody(\"blob\");\n  }\n  formData() {\n    return this.cachedBody(\"formData\");\n  }\n  addValidatedData(target, data) {\n    this.#validatedData[target] = data;\n  }\n  valid(target) {\n    return this.#validatedData[target];\n  }\n  get url() {\n    return this.raw.url;\n  }\n  get method() {\n    return this.raw.method;\n  }\n  get matchedRoutes() {\n    return this.#matchResult[0].map(([[, route]]) => route);\n  }\n  get routePath() {\n    return this.#matchResult[0].map(([[, route]]) => route)[this.routeIndex].path;\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvaG9uby9kaXN0L3JlcXVlc3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUE7QUFDNEM7QUFDd0M7QUFDcEY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQ0FBc0Msa0VBQW1CO0FBQ3pEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMENBQTBDLGtFQUFtQjtBQUM3RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyw0REFBYTtBQUN4QjtBQUNBO0FBQ0EsV0FBVyw2REFBYztBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLCtDQUErQyx5REFBUztBQUN4RDtBQUNBO0FBQ0EsWUFBWSxpQkFBaUI7QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUdFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGhlLWNhbnZhcy8uL25vZGVfbW9kdWxlcy9ob25vL2Rpc3QvcmVxdWVzdC5qcz80NGMyIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy9yZXF1ZXN0LnRzXG5pbXBvcnQgeyBwYXJzZUJvZHkgfSBmcm9tIFwiLi91dGlscy9ib2R5LmpzXCI7XG5pbXBvcnQgeyBkZWNvZGVVUklDb21wb25lbnRfLCBnZXRRdWVyeVBhcmFtLCBnZXRRdWVyeVBhcmFtcyB9IGZyb20gXCIuL3V0aWxzL3VybC5qc1wiO1xudmFyIEhvbm9SZXF1ZXN0ID0gY2xhc3Mge1xuICByYXc7XG4gICN2YWxpZGF0ZWREYXRhO1xuICAjbWF0Y2hSZXN1bHQ7XG4gIHJvdXRlSW5kZXggPSAwO1xuICBwYXRoO1xuICBib2R5Q2FjaGUgPSB7fTtcbiAgY29uc3RydWN0b3IocmVxdWVzdCwgcGF0aCA9IFwiL1wiLCBtYXRjaFJlc3VsdCA9IFtbXV0pIHtcbiAgICB0aGlzLnJhdyA9IHJlcXVlc3Q7XG4gICAgdGhpcy5wYXRoID0gcGF0aDtcbiAgICB0aGlzLiNtYXRjaFJlc3VsdCA9IG1hdGNoUmVzdWx0O1xuICAgIHRoaXMuI3ZhbGlkYXRlZERhdGEgPSB7fTtcbiAgfVxuICBwYXJhbShrZXkpIHtcbiAgICByZXR1cm4ga2V5ID8gdGhpcy5nZXREZWNvZGVkUGFyYW0oa2V5KSA6IHRoaXMuZ2V0QWxsRGVjb2RlZFBhcmFtcygpO1xuICB9XG4gIGdldERlY29kZWRQYXJhbShrZXkpIHtcbiAgICBjb25zdCBwYXJhbUtleSA9IHRoaXMuI21hdGNoUmVzdWx0WzBdW3RoaXMucm91dGVJbmRleF1bMV1ba2V5XTtcbiAgICBjb25zdCBwYXJhbSA9IHRoaXMuZ2V0UGFyYW1WYWx1ZShwYXJhbUtleSk7XG4gICAgcmV0dXJuIHBhcmFtID8gL1xcJS8udGVzdChwYXJhbSkgPyBkZWNvZGVVUklDb21wb25lbnRfKHBhcmFtKSA6IHBhcmFtIDogdm9pZCAwO1xuICB9XG4gIGdldEFsbERlY29kZWRQYXJhbXMoKSB7XG4gICAgY29uc3QgZGVjb2RlZCA9IHt9O1xuICAgIGNvbnN0IGtleXMgPSBPYmplY3Qua2V5cyh0aGlzLiNtYXRjaFJlc3VsdFswXVt0aGlzLnJvdXRlSW5kZXhdWzFdKTtcbiAgICBmb3IgKGNvbnN0IGtleSBvZiBrZXlzKSB7XG4gICAgICBjb25zdCB2YWx1ZSA9IHRoaXMuZ2V0UGFyYW1WYWx1ZSh0aGlzLiNtYXRjaFJlc3VsdFswXVt0aGlzLnJvdXRlSW5kZXhdWzFdW2tleV0pO1xuICAgICAgaWYgKHZhbHVlICYmIHR5cGVvZiB2YWx1ZSA9PT0gXCJzdHJpbmdcIikge1xuICAgICAgICBkZWNvZGVkW2tleV0gPSAvXFwlLy50ZXN0KHZhbHVlKSA/IGRlY29kZVVSSUNvbXBvbmVudF8odmFsdWUpIDogdmFsdWU7XG4gICAgICB9XG4gICAgfVxuICAgIHJldHVybiBkZWNvZGVkO1xuICB9XG4gIGdldFBhcmFtVmFsdWUocGFyYW1LZXkpIHtcbiAgICByZXR1cm4gdGhpcy4jbWF0Y2hSZXN1bHRbMV0gPyB0aGlzLiNtYXRjaFJlc3VsdFsxXVtwYXJhbUtleV0gOiBwYXJhbUtleTtcbiAgfVxuICBxdWVyeShrZXkpIHtcbiAgICByZXR1cm4gZ2V0UXVlcnlQYXJhbSh0aGlzLnVybCwga2V5KTtcbiAgfVxuICBxdWVyaWVzKGtleSkge1xuICAgIHJldHVybiBnZXRRdWVyeVBhcmFtcyh0aGlzLnVybCwga2V5KTtcbiAgfVxuICBoZWFkZXIobmFtZSkge1xuICAgIGlmIChuYW1lKSB7XG4gICAgICByZXR1cm4gdGhpcy5yYXcuaGVhZGVycy5nZXQobmFtZS50b0xvd2VyQ2FzZSgpKSA/PyB2b2lkIDA7XG4gICAgfVxuICAgIGNvbnN0IGhlYWRlckRhdGEgPSB7fTtcbiAgICB0aGlzLnJhdy5oZWFkZXJzLmZvckVhY2goKHZhbHVlLCBrZXkpID0+IHtcbiAgICAgIGhlYWRlckRhdGFba2V5XSA9IHZhbHVlO1xuICAgIH0pO1xuICAgIHJldHVybiBoZWFkZXJEYXRhO1xuICB9XG4gIGFzeW5jIHBhcnNlQm9keShvcHRpb25zKSB7XG4gICAgcmV0dXJuIHRoaXMuYm9keUNhY2hlLnBhcnNlZEJvZHkgPz89IGF3YWl0IHBhcnNlQm9keSh0aGlzLCBvcHRpb25zKTtcbiAgfVxuICBjYWNoZWRCb2R5ID0gKGtleSkgPT4ge1xuICAgIGNvbnN0IHsgYm9keUNhY2hlLCByYXcgfSA9IHRoaXM7XG4gICAgY29uc3QgY2FjaGVkQm9keSA9IGJvZHlDYWNoZVtrZXldO1xuICAgIGlmIChjYWNoZWRCb2R5KSB7XG4gICAgICByZXR1cm4gY2FjaGVkQm9keTtcbiAgICB9XG4gICAgY29uc3QgYW55Q2FjaGVkS2V5ID0gT2JqZWN0LmtleXMoYm9keUNhY2hlKVswXTtcbiAgICBpZiAoYW55Q2FjaGVkS2V5KSB7XG4gICAgICByZXR1cm4gYm9keUNhY2hlW2FueUNhY2hlZEtleV0udGhlbigoYm9keSkgPT4ge1xuICAgICAgICBpZiAoYW55Q2FjaGVkS2V5ID09PSBcImpzb25cIikge1xuICAgICAgICAgIGJvZHkgPSBKU09OLnN0cmluZ2lmeShib2R5KTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gbmV3IFJlc3BvbnNlKGJvZHkpW2tleV0oKTtcbiAgICAgIH0pO1xuICAgIH1cbiAgICByZXR1cm4gYm9keUNhY2hlW2tleV0gPSByYXdba2V5XSgpO1xuICB9O1xuICBqc29uKCkge1xuICAgIHJldHVybiB0aGlzLmNhY2hlZEJvZHkoXCJqc29uXCIpO1xuICB9XG4gIHRleHQoKSB7XG4gICAgcmV0dXJuIHRoaXMuY2FjaGVkQm9keShcInRleHRcIik7XG4gIH1cbiAgYXJyYXlCdWZmZXIoKSB7XG4gICAgcmV0dXJuIHRoaXMuY2FjaGVkQm9keShcImFycmF5QnVmZmVyXCIpO1xuICB9XG4gIGJsb2IoKSB7XG4gICAgcmV0dXJuIHRoaXMuY2FjaGVkQm9keShcImJsb2JcIik7XG4gIH1cbiAgZm9ybURhdGEoKSB7XG4gICAgcmV0dXJuIHRoaXMuY2FjaGVkQm9keShcImZvcm1EYXRhXCIpO1xuICB9XG4gIGFkZFZhbGlkYXRlZERhdGEodGFyZ2V0LCBkYXRhKSB7XG4gICAgdGhpcy4jdmFsaWRhdGVkRGF0YVt0YXJnZXRdID0gZGF0YTtcbiAgfVxuICB2YWxpZCh0YXJnZXQpIHtcbiAgICByZXR1cm4gdGhpcy4jdmFsaWRhdGVkRGF0YVt0YXJnZXRdO1xuICB9XG4gIGdldCB1cmwoKSB7XG4gICAgcmV0dXJuIHRoaXMucmF3LnVybDtcbiAgfVxuICBnZXQgbWV0aG9kKCkge1xuICAgIHJldHVybiB0aGlzLnJhdy5tZXRob2Q7XG4gIH1cbiAgZ2V0IG1hdGNoZWRSb3V0ZXMoKSB7XG4gICAgcmV0dXJuIHRoaXMuI21hdGNoUmVzdWx0WzBdLm1hcCgoW1ssIHJvdXRlXV0pID0+IHJvdXRlKTtcbiAgfVxuICBnZXQgcm91dGVQYXRoKCkge1xuICAgIHJldHVybiB0aGlzLiNtYXRjaFJlc3VsdFswXS5tYXAoKFtbLCByb3V0ZV1dKSA9PiByb3V0ZSlbdGhpcy5yb3V0ZUluZGV4XS5wYXRoO1xuICB9XG59O1xuZXhwb3J0IHtcbiAgSG9ub1JlcXVlc3Rcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/hono/dist/request.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/hono/dist/router.js":
/*!******************************************!*\
  !*** ./node_modules/hono/dist/router.js ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MESSAGE_MATCHER_IS_ALREADY_BUILT: () => (/* binding */ MESSAGE_MATCHER_IS_ALREADY_BUILT),\n/* harmony export */   METHODS: () => (/* binding */ METHODS),\n/* harmony export */   METHOD_NAME_ALL: () => (/* binding */ METHOD_NAME_ALL),\n/* harmony export */   METHOD_NAME_ALL_LOWERCASE: () => (/* binding */ METHOD_NAME_ALL_LOWERCASE),\n/* harmony export */   UnsupportedPathError: () => (/* binding */ UnsupportedPathError)\n/* harmony export */ });\n// src/router.ts\nvar METHOD_NAME_ALL = \"ALL\";\nvar METHOD_NAME_ALL_LOWERCASE = \"all\";\nvar METHODS = [\"get\", \"post\", \"put\", \"delete\", \"options\", \"patch\"];\nvar MESSAGE_MATCHER_IS_ALREADY_BUILT = \"Can not add a route since the matcher is already built.\";\nvar UnsupportedPathError = class extends Error {\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvaG9uby9kaXN0L3JvdXRlci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBT0UiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90aGUtY2FudmFzLy4vbm9kZV9tb2R1bGVzL2hvbm8vZGlzdC9yb3V0ZXIuanM/MGY1MSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvcm91dGVyLnRzXG52YXIgTUVUSE9EX05BTUVfQUxMID0gXCJBTExcIjtcbnZhciBNRVRIT0RfTkFNRV9BTExfTE9XRVJDQVNFID0gXCJhbGxcIjtcbnZhciBNRVRIT0RTID0gW1wiZ2V0XCIsIFwicG9zdFwiLCBcInB1dFwiLCBcImRlbGV0ZVwiLCBcIm9wdGlvbnNcIiwgXCJwYXRjaFwiXTtcbnZhciBNRVNTQUdFX01BVENIRVJfSVNfQUxSRUFEWV9CVUlMVCA9IFwiQ2FuIG5vdCBhZGQgYSByb3V0ZSBzaW5jZSB0aGUgbWF0Y2hlciBpcyBhbHJlYWR5IGJ1aWx0LlwiO1xudmFyIFVuc3VwcG9ydGVkUGF0aEVycm9yID0gY2xhc3MgZXh0ZW5kcyBFcnJvciB7XG59O1xuZXhwb3J0IHtcbiAgTUVTU0FHRV9NQVRDSEVSX0lTX0FMUkVBRFlfQlVJTFQsXG4gIE1FVEhPRFMsXG4gIE1FVEhPRF9OQU1FX0FMTCxcbiAgTUVUSE9EX05BTUVfQUxMX0xPV0VSQ0FTRSxcbiAgVW5zdXBwb3J0ZWRQYXRoRXJyb3Jcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/hono/dist/router.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/hono/dist/router/reg-exp-router/index.js":
/*!***************************************************************!*\
  !*** ./node_modules/hono/dist/router/reg-exp-router/index.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RegExpRouter: () => (/* reexport safe */ _router_js__WEBPACK_IMPORTED_MODULE_0__.RegExpRouter)\n/* harmony export */ });\n/* harmony import */ var _router_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./router.js */ \"(rsc)/./node_modules/hono/dist/router/reg-exp-router/router.js\");\n// src/router/reg-exp-router/index.ts\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvaG9uby9kaXN0L3JvdXRlci9yZWctZXhwLXJvdXRlci9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQzJDO0FBR3pDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGhlLWNhbnZhcy8uL25vZGVfbW9kdWxlcy9ob25vL2Rpc3Qvcm91dGVyL3JlZy1leHAtcm91dGVyL2luZGV4LmpzPzQ2OTIiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL3JvdXRlci9yZWctZXhwLXJvdXRlci9pbmRleC50c1xuaW1wb3J0IHsgUmVnRXhwUm91dGVyIH0gZnJvbSBcIi4vcm91dGVyLmpzXCI7XG5leHBvcnQge1xuICBSZWdFeHBSb3V0ZXJcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/hono/dist/router/reg-exp-router/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/hono/dist/router/reg-exp-router/node.js":
/*!**************************************************************!*\
  !*** ./node_modules/hono/dist/router/reg-exp-router/node.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Node: () => (/* binding */ Node),\n/* harmony export */   PATH_ERROR: () => (/* binding */ PATH_ERROR)\n/* harmony export */ });\n// src/router/reg-exp-router/node.ts\nvar LABEL_REG_EXP_STR = \"[^/]+\";\nvar ONLY_WILDCARD_REG_EXP_STR = \".*\";\nvar TAIL_WILDCARD_REG_EXP_STR = \"(?:|/.*)\";\nvar PATH_ERROR = Symbol();\nvar regExpMetaChars = new Set(\".\\\\+*[^]$()\");\nfunction compareKey(a, b) {\n  if (a.length === 1) {\n    return b.length === 1 ? a < b ? -1 : 1 : -1;\n  }\n  if (b.length === 1) {\n    return 1;\n  }\n  if (a === ONLY_WILDCARD_REG_EXP_STR || a === TAIL_WILDCARD_REG_EXP_STR) {\n    return 1;\n  } else if (b === ONLY_WILDCARD_REG_EXP_STR || b === TAIL_WILDCARD_REG_EXP_STR) {\n    return -1;\n  }\n  if (a === LABEL_REG_EXP_STR) {\n    return 1;\n  } else if (b === LABEL_REG_EXP_STR) {\n    return -1;\n  }\n  return a.length === b.length ? a < b ? -1 : 1 : b.length - a.length;\n}\nvar Node = class {\n  index;\n  varIndex;\n  children = /* @__PURE__ */ Object.create(null);\n  insert(tokens, index, paramMap, context, pathErrorCheckOnly) {\n    if (tokens.length === 0) {\n      if (this.index !== void 0) {\n        throw PATH_ERROR;\n      }\n      if (pathErrorCheckOnly) {\n        return;\n      }\n      this.index = index;\n      return;\n    }\n    const [token, ...restTokens] = tokens;\n    const pattern = token === \"*\" ? restTokens.length === 0 ? [\"\", \"\", ONLY_WILDCARD_REG_EXP_STR] : [\"\", \"\", LABEL_REG_EXP_STR] : token === \"/*\" ? [\"\", \"\", TAIL_WILDCARD_REG_EXP_STR] : token.match(/^\\:([^\\{\\}]+)(?:\\{(.+)\\})?$/);\n    let node;\n    if (pattern) {\n      const name = pattern[1];\n      let regexpStr = pattern[2] || LABEL_REG_EXP_STR;\n      if (name && pattern[2]) {\n        regexpStr = regexpStr.replace(/^\\((?!\\?:)(?=[^)]+\\)$)/, \"(?:\");\n        if (/\\((?!\\?:)/.test(regexpStr)) {\n          throw PATH_ERROR;\n        }\n      }\n      node = this.children[regexpStr];\n      if (!node) {\n        if (Object.keys(this.children).some(\n          (k) => k !== ONLY_WILDCARD_REG_EXP_STR && k !== TAIL_WILDCARD_REG_EXP_STR\n        )) {\n          throw PATH_ERROR;\n        }\n        if (pathErrorCheckOnly) {\n          return;\n        }\n        node = this.children[regexpStr] = new Node();\n        if (name !== \"\") {\n          node.varIndex = context.varIndex++;\n        }\n      }\n      if (!pathErrorCheckOnly && name !== \"\") {\n        paramMap.push([name, node.varIndex]);\n      }\n    } else {\n      node = this.children[token];\n      if (!node) {\n        if (Object.keys(this.children).some(\n          (k) => k.length > 1 && k !== ONLY_WILDCARD_REG_EXP_STR && k !== TAIL_WILDCARD_REG_EXP_STR\n        )) {\n          throw PATH_ERROR;\n        }\n        if (pathErrorCheckOnly) {\n          return;\n        }\n        node = this.children[token] = new Node();\n      }\n    }\n    node.insert(restTokens, index, paramMap, context, pathErrorCheckOnly);\n  }\n  buildRegExpStr() {\n    const childKeys = Object.keys(this.children).sort(compareKey);\n    const strList = childKeys.map((k) => {\n      const c = this.children[k];\n      return (typeof c.varIndex === \"number\" ? `(${k})@${c.varIndex}` : regExpMetaChars.has(k) ? `\\\\${k}` : k) + c.buildRegExpStr();\n    });\n    if (typeof this.index === \"number\") {\n      strList.unshift(`#${this.index}`);\n    }\n    if (strList.length === 0) {\n      return \"\";\n    }\n    if (strList.length === 1) {\n      return strList[0];\n    }\n    return \"(?:\" + strList.join(\"|\") + \")\";\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/hono/dist/router/reg-exp-router/node.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/hono/dist/router/reg-exp-router/router.js":
/*!****************************************************************!*\
  !*** ./node_modules/hono/dist/router/reg-exp-router/router.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RegExpRouter: () => (/* binding */ RegExpRouter)\n/* harmony export */ });\n/* harmony import */ var _router_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../router.js */ \"(rsc)/./node_modules/hono/dist/router.js\");\n/* harmony import */ var _utils_url_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../utils/url.js */ \"(rsc)/./node_modules/hono/dist/utils/url.js\");\n/* harmony import */ var _node_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./node.js */ \"(rsc)/./node_modules/hono/dist/router/reg-exp-router/node.js\");\n/* harmony import */ var _trie_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./trie.js */ \"(rsc)/./node_modules/hono/dist/router/reg-exp-router/trie.js\");\n// src/router/reg-exp-router/router.ts\n\n\n\n\nvar emptyParam = [];\nvar nullMatcher = [/^$/, [], /* @__PURE__ */ Object.create(null)];\nvar wildcardRegExpCache = /* @__PURE__ */ Object.create(null);\nfunction buildWildcardRegExp(path) {\n  return wildcardRegExpCache[path] ??= new RegExp(\n    path === \"*\" ? \"\" : `^${path.replace(\n      /\\/\\*$|([.\\\\+*[^\\]$()])/g,\n      (_, metaChar) => metaChar ? `\\\\${metaChar}` : \"(?:|/.*)\"\n    )}$`\n  );\n}\nfunction clearWildcardRegExpCache() {\n  wildcardRegExpCache = /* @__PURE__ */ Object.create(null);\n}\nfunction buildMatcherFromPreprocessedRoutes(routes) {\n  const trie = new _trie_js__WEBPACK_IMPORTED_MODULE_3__.Trie();\n  const handlerData = [];\n  if (routes.length === 0) {\n    return nullMatcher;\n  }\n  const routesWithStaticPathFlag = routes.map(\n    (route) => [!/\\*|\\/:/.test(route[0]), ...route]\n  ).sort(\n    ([isStaticA, pathA], [isStaticB, pathB]) => isStaticA ? 1 : isStaticB ? -1 : pathA.length - pathB.length\n  );\n  const staticMap = /* @__PURE__ */ Object.create(null);\n  for (let i = 0, j = -1, len = routesWithStaticPathFlag.length; i < len; i++) {\n    const [pathErrorCheckOnly, path, handlers] = routesWithStaticPathFlag[i];\n    if (pathErrorCheckOnly) {\n      staticMap[path] = [handlers.map(([h]) => [h, /* @__PURE__ */ Object.create(null)]), emptyParam];\n    } else {\n      j++;\n    }\n    let paramAssoc;\n    try {\n      paramAssoc = trie.insert(path, j, pathErrorCheckOnly);\n    } catch (e) {\n      throw e === _node_js__WEBPACK_IMPORTED_MODULE_2__.PATH_ERROR ? new _router_js__WEBPACK_IMPORTED_MODULE_0__.UnsupportedPathError(path) : e;\n    }\n    if (pathErrorCheckOnly) {\n      continue;\n    }\n    handlerData[j] = handlers.map(([h, paramCount]) => {\n      const paramIndexMap = /* @__PURE__ */ Object.create(null);\n      paramCount -= 1;\n      for (; paramCount >= 0; paramCount--) {\n        const [key, value] = paramAssoc[paramCount];\n        paramIndexMap[key] = value;\n      }\n      return [h, paramIndexMap];\n    });\n  }\n  const [regexp, indexReplacementMap, paramReplacementMap] = trie.buildRegExp();\n  for (let i = 0, len = handlerData.length; i < len; i++) {\n    for (let j = 0, len2 = handlerData[i].length; j < len2; j++) {\n      const map = handlerData[i][j]?.[1];\n      if (!map) {\n        continue;\n      }\n      const keys = Object.keys(map);\n      for (let k = 0, len3 = keys.length; k < len3; k++) {\n        map[keys[k]] = paramReplacementMap[map[keys[k]]];\n      }\n    }\n  }\n  const handlerMap = [];\n  for (const i in indexReplacementMap) {\n    handlerMap[i] = handlerData[indexReplacementMap[i]];\n  }\n  return [regexp, handlerMap, staticMap];\n}\nfunction findMiddleware(middleware, path) {\n  if (!middleware) {\n    return void 0;\n  }\n  for (const k of Object.keys(middleware).sort((a, b) => b.length - a.length)) {\n    if (buildWildcardRegExp(k).test(path)) {\n      return [...middleware[k]];\n    }\n  }\n  return void 0;\n}\nvar RegExpRouter = class {\n  name = \"RegExpRouter\";\n  middleware;\n  routes;\n  constructor() {\n    this.middleware = { [_router_js__WEBPACK_IMPORTED_MODULE_0__.METHOD_NAME_ALL]: /* @__PURE__ */ Object.create(null) };\n    this.routes = { [_router_js__WEBPACK_IMPORTED_MODULE_0__.METHOD_NAME_ALL]: /* @__PURE__ */ Object.create(null) };\n  }\n  add(method, path, handler) {\n    const { middleware, routes } = this;\n    if (!middleware || !routes) {\n      throw new Error(_router_js__WEBPACK_IMPORTED_MODULE_0__.MESSAGE_MATCHER_IS_ALREADY_BUILT);\n    }\n    if (!middleware[method]) {\n      ;\n      [middleware, routes].forEach((handlerMap) => {\n        handlerMap[method] = /* @__PURE__ */ Object.create(null);\n        Object.keys(handlerMap[_router_js__WEBPACK_IMPORTED_MODULE_0__.METHOD_NAME_ALL]).forEach((p) => {\n          handlerMap[method][p] = [...handlerMap[_router_js__WEBPACK_IMPORTED_MODULE_0__.METHOD_NAME_ALL][p]];\n        });\n      });\n    }\n    if (path === \"/*\") {\n      path = \"*\";\n    }\n    const paramCount = (path.match(/\\/:/g) || []).length;\n    if (/\\*$/.test(path)) {\n      const re = buildWildcardRegExp(path);\n      if (method === _router_js__WEBPACK_IMPORTED_MODULE_0__.METHOD_NAME_ALL) {\n        Object.keys(middleware).forEach((m) => {\n          middleware[m][path] ||= findMiddleware(middleware[m], path) || findMiddleware(middleware[_router_js__WEBPACK_IMPORTED_MODULE_0__.METHOD_NAME_ALL], path) || [];\n        });\n      } else {\n        middleware[method][path] ||= findMiddleware(middleware[method], path) || findMiddleware(middleware[_router_js__WEBPACK_IMPORTED_MODULE_0__.METHOD_NAME_ALL], path) || [];\n      }\n      Object.keys(middleware).forEach((m) => {\n        if (method === _router_js__WEBPACK_IMPORTED_MODULE_0__.METHOD_NAME_ALL || method === m) {\n          Object.keys(middleware[m]).forEach((p) => {\n            re.test(p) && middleware[m][p].push([handler, paramCount]);\n          });\n        }\n      });\n      Object.keys(routes).forEach((m) => {\n        if (method === _router_js__WEBPACK_IMPORTED_MODULE_0__.METHOD_NAME_ALL || method === m) {\n          Object.keys(routes[m]).forEach(\n            (p) => re.test(p) && routes[m][p].push([handler, paramCount])\n          );\n        }\n      });\n      return;\n    }\n    const paths = (0,_utils_url_js__WEBPACK_IMPORTED_MODULE_1__.checkOptionalParameter)(path) || [path];\n    for (let i = 0, len = paths.length; i < len; i++) {\n      const path2 = paths[i];\n      Object.keys(routes).forEach((m) => {\n        if (method === _router_js__WEBPACK_IMPORTED_MODULE_0__.METHOD_NAME_ALL || method === m) {\n          routes[m][path2] ||= [\n            ...findMiddleware(middleware[m], path2) || findMiddleware(middleware[_router_js__WEBPACK_IMPORTED_MODULE_0__.METHOD_NAME_ALL], path2) || []\n          ];\n          routes[m][path2].push([handler, paramCount - len + i + 1]);\n        }\n      });\n    }\n  }\n  match(method, path) {\n    clearWildcardRegExpCache();\n    const matchers = this.buildAllMatchers();\n    this.match = (method2, path2) => {\n      const matcher = matchers[method2] || matchers[_router_js__WEBPACK_IMPORTED_MODULE_0__.METHOD_NAME_ALL];\n      const staticMatch = matcher[2][path2];\n      if (staticMatch) {\n        return staticMatch;\n      }\n      const match = path2.match(matcher[0]);\n      if (!match) {\n        return [[], emptyParam];\n      }\n      const index = match.indexOf(\"\", 1);\n      return [matcher[1][index], match];\n    };\n    return this.match(method, path);\n  }\n  buildAllMatchers() {\n    const matchers = /* @__PURE__ */ Object.create(null);\n    [...Object.keys(this.routes), ...Object.keys(this.middleware)].forEach((method) => {\n      matchers[method] ||= this.buildMatcher(method);\n    });\n    this.middleware = this.routes = void 0;\n    return matchers;\n  }\n  buildMatcher(method) {\n    const routes = [];\n    let hasOwnRoute = method === _router_js__WEBPACK_IMPORTED_MODULE_0__.METHOD_NAME_ALL;\n    [this.middleware, this.routes].forEach((r) => {\n      const ownRoute = r[method] ? Object.keys(r[method]).map((path) => [path, r[method][path]]) : [];\n      if (ownRoute.length !== 0) {\n        hasOwnRoute ||= true;\n        routes.push(...ownRoute);\n      } else if (method !== _router_js__WEBPACK_IMPORTED_MODULE_0__.METHOD_NAME_ALL) {\n        routes.push(\n          ...Object.keys(r[_router_js__WEBPACK_IMPORTED_MODULE_0__.METHOD_NAME_ALL]).map((path) => [path, r[_router_js__WEBPACK_IMPORTED_MODULE_0__.METHOD_NAME_ALL][path]])\n        );\n      }\n    });\n    if (!hasOwnRoute) {\n      return null;\n    } else {\n      return buildMatcherFromPreprocessedRoutes(routes);\n    }\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/hono/dist/router/reg-exp-router/router.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/hono/dist/router/reg-exp-router/trie.js":
/*!**************************************************************!*\
  !*** ./node_modules/hono/dist/router/reg-exp-router/trie.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Trie: () => (/* binding */ Trie)\n/* harmony export */ });\n/* harmony import */ var _node_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node.js */ \"(rsc)/./node_modules/hono/dist/router/reg-exp-router/node.js\");\n// src/router/reg-exp-router/trie.ts\n\nvar Trie = class {\n  context = { varIndex: 0 };\n  root = new _node_js__WEBPACK_IMPORTED_MODULE_0__.Node();\n  insert(path, index, pathErrorCheckOnly) {\n    const paramAssoc = [];\n    const groups = [];\n    for (let i = 0; ; ) {\n      let replaced = false;\n      path = path.replace(/\\{[^}]+\\}/g, (m) => {\n        const mark = `@\\\\${i}`;\n        groups[i] = [mark, m];\n        i++;\n        replaced = true;\n        return mark;\n      });\n      if (!replaced) {\n        break;\n      }\n    }\n    const tokens = path.match(/(?::[^\\/]+)|(?:\\/\\*$)|./g) || [];\n    for (let i = groups.length - 1; i >= 0; i--) {\n      const [mark] = groups[i];\n      for (let j = tokens.length - 1; j >= 0; j--) {\n        if (tokens[j].indexOf(mark) !== -1) {\n          tokens[j] = tokens[j].replace(mark, groups[i][1]);\n          break;\n        }\n      }\n    }\n    this.root.insert(tokens, index, paramAssoc, this.context, pathErrorCheckOnly);\n    return paramAssoc;\n  }\n  buildRegExp() {\n    let regexp = this.root.buildRegExpStr();\n    if (regexp === \"\") {\n      return [/^$/, [], []];\n    }\n    let captureIndex = 0;\n    const indexReplacementMap = [];\n    const paramReplacementMap = [];\n    regexp = regexp.replace(/#(\\d+)|@(\\d+)|\\.\\*\\$/g, (_, handlerIndex, paramIndex) => {\n      if (typeof handlerIndex !== \"undefined\") {\n        indexReplacementMap[++captureIndex] = Number(handlerIndex);\n        return \"$()\";\n      }\n      if (typeof paramIndex !== \"undefined\") {\n        paramReplacementMap[Number(paramIndex)] = ++captureIndex;\n        return \"\";\n      }\n      return \"\";\n    });\n    return [new RegExp(`^${regexp}`), indexReplacementMap, paramReplacementMap];\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/hono/dist/router/reg-exp-router/trie.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/hono/dist/router/smart-router/index.js":
/*!*************************************************************!*\
  !*** ./node_modules/hono/dist/router/smart-router/index.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SmartRouter: () => (/* reexport safe */ _router_js__WEBPACK_IMPORTED_MODULE_0__.SmartRouter)\n/* harmony export */ });\n/* harmony import */ var _router_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./router.js */ \"(rsc)/./node_modules/hono/dist/router/smart-router/router.js\");\n// src/router/smart-router/index.ts\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvaG9uby9kaXN0L3JvdXRlci9zbWFydC1yb3V0ZXIvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUMwQztBQUd4QyIsInNvdXJjZXMiOlsid2VicGFjazovL3RoZS1jYW52YXMvLi9ub2RlX21vZHVsZXMvaG9uby9kaXN0L3JvdXRlci9zbWFydC1yb3V0ZXIvaW5kZXguanM/MDExYyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvcm91dGVyL3NtYXJ0LXJvdXRlci9pbmRleC50c1xuaW1wb3J0IHsgU21hcnRSb3V0ZXIgfSBmcm9tIFwiLi9yb3V0ZXIuanNcIjtcbmV4cG9ydCB7XG4gIFNtYXJ0Um91dGVyXG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/hono/dist/router/smart-router/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/hono/dist/router/smart-router/router.js":
/*!**************************************************************!*\
  !*** ./node_modules/hono/dist/router/smart-router/router.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SmartRouter: () => (/* binding */ SmartRouter)\n/* harmony export */ });\n/* harmony import */ var _router_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../router.js */ \"(rsc)/./node_modules/hono/dist/router.js\");\n// src/router/smart-router/router.ts\n\nvar SmartRouter = class {\n  name = \"SmartRouter\";\n  routers = [];\n  routes = [];\n  constructor(init) {\n    Object.assign(this, init);\n  }\n  add(method, path, handler) {\n    if (!this.routes) {\n      throw new Error(_router_js__WEBPACK_IMPORTED_MODULE_0__.MESSAGE_MATCHER_IS_ALREADY_BUILT);\n    }\n    this.routes.push([method, path, handler]);\n  }\n  match(method, path) {\n    if (!this.routes) {\n      throw new Error(\"Fatal error\");\n    }\n    const { routers, routes } = this;\n    const len = routers.length;\n    let i = 0;\n    let res;\n    for (; i < len; i++) {\n      const router = routers[i];\n      try {\n        routes.forEach((args) => {\n          router.add(...args);\n        });\n        res = router.match(method, path);\n      } catch (e) {\n        if (e instanceof _router_js__WEBPACK_IMPORTED_MODULE_0__.UnsupportedPathError) {\n          continue;\n        }\n        throw e;\n      }\n      this.match = router.match.bind(router);\n      this.routers = [router];\n      this.routes = void 0;\n      break;\n    }\n    if (i === len) {\n      throw new Error(\"Fatal error\");\n    }\n    this.name = `SmartRouter + ${this.activeRouter.name}`;\n    return res;\n  }\n  get activeRouter() {\n    if (this.routes || this.routers.length !== 1) {\n      throw new Error(\"No active router has been determined yet.\");\n    }\n    return this.routers[0];\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvaG9uby9kaXN0L3JvdXRlci9zbWFydC1yb3V0ZXIvcm91dGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDeUY7QUFDekY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLHdFQUFnQztBQUN0RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksa0JBQWtCO0FBQzlCO0FBQ0E7QUFDQTtBQUNBLFdBQVcsU0FBUztBQUNwQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBLFFBQVE7QUFDUix5QkFBeUIsNERBQW9CO0FBQzdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQyx1QkFBdUI7QUFDeEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBR0UiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90aGUtY2FudmFzLy4vbm9kZV9tb2R1bGVzL2hvbm8vZGlzdC9yb3V0ZXIvc21hcnQtcm91dGVyL3JvdXRlci5qcz81MzQwIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy9yb3V0ZXIvc21hcnQtcm91dGVyL3JvdXRlci50c1xuaW1wb3J0IHsgTUVTU0FHRV9NQVRDSEVSX0lTX0FMUkVBRFlfQlVJTFQsIFVuc3VwcG9ydGVkUGF0aEVycm9yIH0gZnJvbSBcIi4uLy4uL3JvdXRlci5qc1wiO1xudmFyIFNtYXJ0Um91dGVyID0gY2xhc3Mge1xuICBuYW1lID0gXCJTbWFydFJvdXRlclwiO1xuICByb3V0ZXJzID0gW107XG4gIHJvdXRlcyA9IFtdO1xuICBjb25zdHJ1Y3Rvcihpbml0KSB7XG4gICAgT2JqZWN0LmFzc2lnbih0aGlzLCBpbml0KTtcbiAgfVxuICBhZGQobWV0aG9kLCBwYXRoLCBoYW5kbGVyKSB7XG4gICAgaWYgKCF0aGlzLnJvdXRlcykge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKE1FU1NBR0VfTUFUQ0hFUl9JU19BTFJFQURZX0JVSUxUKTtcbiAgICB9XG4gICAgdGhpcy5yb3V0ZXMucHVzaChbbWV0aG9kLCBwYXRoLCBoYW5kbGVyXSk7XG4gIH1cbiAgbWF0Y2gobWV0aG9kLCBwYXRoKSB7XG4gICAgaWYgKCF0aGlzLnJvdXRlcykge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKFwiRmF0YWwgZXJyb3JcIik7XG4gICAgfVxuICAgIGNvbnN0IHsgcm91dGVycywgcm91dGVzIH0gPSB0aGlzO1xuICAgIGNvbnN0IGxlbiA9IHJvdXRlcnMubGVuZ3RoO1xuICAgIGxldCBpID0gMDtcbiAgICBsZXQgcmVzO1xuICAgIGZvciAoOyBpIDwgbGVuOyBpKyspIHtcbiAgICAgIGNvbnN0IHJvdXRlciA9IHJvdXRlcnNbaV07XG4gICAgICB0cnkge1xuICAgICAgICByb3V0ZXMuZm9yRWFjaCgoYXJncykgPT4ge1xuICAgICAgICAgIHJvdXRlci5hZGQoLi4uYXJncyk7XG4gICAgICAgIH0pO1xuICAgICAgICByZXMgPSByb3V0ZXIubWF0Y2gobWV0aG9kLCBwYXRoKTtcbiAgICAgIH0gY2F0Y2ggKGUpIHtcbiAgICAgICAgaWYgKGUgaW5zdGFuY2VvZiBVbnN1cHBvcnRlZFBhdGhFcnJvcikge1xuICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICB9XG4gICAgICAgIHRocm93IGU7XG4gICAgICB9XG4gICAgICB0aGlzLm1hdGNoID0gcm91dGVyLm1hdGNoLmJpbmQocm91dGVyKTtcbiAgICAgIHRoaXMucm91dGVycyA9IFtyb3V0ZXJdO1xuICAgICAgdGhpcy5yb3V0ZXMgPSB2b2lkIDA7XG4gICAgICBicmVhaztcbiAgICB9XG4gICAgaWYgKGkgPT09IGxlbikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKFwiRmF0YWwgZXJyb3JcIik7XG4gICAgfVxuICAgIHRoaXMubmFtZSA9IGBTbWFydFJvdXRlciArICR7dGhpcy5hY3RpdmVSb3V0ZXIubmFtZX1gO1xuICAgIHJldHVybiByZXM7XG4gIH1cbiAgZ2V0IGFjdGl2ZVJvdXRlcigpIHtcbiAgICBpZiAodGhpcy5yb3V0ZXMgfHwgdGhpcy5yb3V0ZXJzLmxlbmd0aCAhPT0gMSkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKFwiTm8gYWN0aXZlIHJvdXRlciBoYXMgYmVlbiBkZXRlcm1pbmVkIHlldC5cIik7XG4gICAgfVxuICAgIHJldHVybiB0aGlzLnJvdXRlcnNbMF07XG4gIH1cbn07XG5leHBvcnQge1xuICBTbWFydFJvdXRlclxufTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/hono/dist/router/smart-router/router.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/hono/dist/router/trie-router/index.js":
/*!************************************************************!*\
  !*** ./node_modules/hono/dist/router/trie-router/index.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TrieRouter: () => (/* reexport safe */ _router_js__WEBPACK_IMPORTED_MODULE_0__.TrieRouter)\n/* harmony export */ });\n/* harmony import */ var _router_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./router.js */ \"(rsc)/./node_modules/hono/dist/router/trie-router/router.js\");\n// src/router/trie-router/index.ts\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvaG9uby9kaXN0L3JvdXRlci90cmllLXJvdXRlci9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ3lDO0FBR3ZDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGhlLWNhbnZhcy8uL25vZGVfbW9kdWxlcy9ob25vL2Rpc3Qvcm91dGVyL3RyaWUtcm91dGVyL2luZGV4LmpzPzMyMjgiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL3JvdXRlci90cmllLXJvdXRlci9pbmRleC50c1xuaW1wb3J0IHsgVHJpZVJvdXRlciB9IGZyb20gXCIuL3JvdXRlci5qc1wiO1xuZXhwb3J0IHtcbiAgVHJpZVJvdXRlclxufTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/hono/dist/router/trie-router/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/hono/dist/router/trie-router/node.js":
/*!***********************************************************!*\
  !*** ./node_modules/hono/dist/router/trie-router/node.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Node: () => (/* binding */ Node)\n/* harmony export */ });\n/* harmony import */ var _router_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../router.js */ \"(rsc)/./node_modules/hono/dist/router.js\");\n/* harmony import */ var _utils_url_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../utils/url.js */ \"(rsc)/./node_modules/hono/dist/utils/url.js\");\n// src/router/trie-router/node.ts\n\n\nvar Node = class {\n  methods;\n  children;\n  patterns;\n  order = 0;\n  name;\n  params = /* @__PURE__ */ Object.create(null);\n  constructor(method, handler, children) {\n    this.children = children || /* @__PURE__ */ Object.create(null);\n    this.methods = [];\n    this.name = \"\";\n    if (method && handler) {\n      const m = /* @__PURE__ */ Object.create(null);\n      m[method] = { handler, possibleKeys: [], score: 0, name: this.name };\n      this.methods = [m];\n    }\n    this.patterns = [];\n  }\n  insert(method, path, handler) {\n    this.name = `${method} ${path}`;\n    this.order = ++this.order;\n    let curNode = this;\n    const parts = (0,_utils_url_js__WEBPACK_IMPORTED_MODULE_1__.splitRoutingPath)(path);\n    const possibleKeys = [];\n    for (let i = 0, len = parts.length; i < len; i++) {\n      const p = parts[i];\n      if (Object.keys(curNode.children).includes(p)) {\n        curNode = curNode.children[p];\n        const pattern2 = (0,_utils_url_js__WEBPACK_IMPORTED_MODULE_1__.getPattern)(p);\n        if (pattern2) {\n          possibleKeys.push(pattern2[1]);\n        }\n        continue;\n      }\n      curNode.children[p] = new Node();\n      const pattern = (0,_utils_url_js__WEBPACK_IMPORTED_MODULE_1__.getPattern)(p);\n      if (pattern) {\n        curNode.patterns.push(pattern);\n        possibleKeys.push(pattern[1]);\n      }\n      curNode = curNode.children[p];\n    }\n    if (!curNode.methods.length) {\n      curNode.methods = [];\n    }\n    const m = /* @__PURE__ */ Object.create(null);\n    const handlerSet = {\n      handler,\n      possibleKeys: possibleKeys.filter((v, i, a) => a.indexOf(v) === i),\n      name: this.name,\n      score: this.order\n    };\n    m[method] = handlerSet;\n    curNode.methods.push(m);\n    return curNode;\n  }\n  gHSets(node, method, nodeParams, params) {\n    const handlerSets = [];\n    for (let i = 0, len = node.methods.length; i < len; i++) {\n      const m = node.methods[i];\n      const handlerSet = m[method] || m[_router_js__WEBPACK_IMPORTED_MODULE_0__.METHOD_NAME_ALL];\n      const processedSet = /* @__PURE__ */ Object.create(null);\n      if (handlerSet !== void 0) {\n        handlerSet.params = /* @__PURE__ */ Object.create(null);\n        handlerSet.possibleKeys.forEach((key) => {\n          const processed = processedSet[handlerSet.name];\n          handlerSet.params[key] = params[key] && !processed ? params[key] : nodeParams[key] ?? params[key];\n          processedSet[handlerSet.name] = true;\n        });\n        handlerSets.push(handlerSet);\n      }\n    }\n    return handlerSets;\n  }\n  search(method, path) {\n    const handlerSets = [];\n    this.params = /* @__PURE__ */ Object.create(null);\n    const curNode = this;\n    let curNodes = [curNode];\n    const parts = (0,_utils_url_js__WEBPACK_IMPORTED_MODULE_1__.splitPath)(path);\n    for (let i = 0, len = parts.length; i < len; i++) {\n      const part = parts[i];\n      const isLast = i === len - 1;\n      const tempNodes = [];\n      for (let j = 0, len2 = curNodes.length; j < len2; j++) {\n        const node = curNodes[j];\n        const nextNode = node.children[part];\n        if (nextNode) {\n          nextNode.params = node.params;\n          if (isLast === true) {\n            if (nextNode.children[\"*\"]) {\n              handlerSets.push(\n                ...this.gHSets(nextNode.children[\"*\"], method, node.params, /* @__PURE__ */ Object.create(null))\n              );\n            }\n            handlerSets.push(...this.gHSets(nextNode, method, node.params, /* @__PURE__ */ Object.create(null)));\n          } else {\n            tempNodes.push(nextNode);\n          }\n        }\n        for (let k = 0, len3 = node.patterns.length; k < len3; k++) {\n          const pattern = node.patterns[k];\n          const params = { ...node.params };\n          if (pattern === \"*\") {\n            const astNode = node.children[\"*\"];\n            if (astNode) {\n              handlerSets.push(...this.gHSets(astNode, method, node.params, /* @__PURE__ */ Object.create(null)));\n              tempNodes.push(astNode);\n            }\n            continue;\n          }\n          if (part === \"\") {\n            continue;\n          }\n          const [key, name, matcher] = pattern;\n          const child = node.children[key];\n          const restPathString = parts.slice(i).join(\"/\");\n          if (matcher instanceof RegExp && matcher.test(restPathString)) {\n            params[name] = restPathString;\n            handlerSets.push(...this.gHSets(child, method, node.params, params));\n            continue;\n          }\n          if (matcher === true || matcher instanceof RegExp && matcher.test(part)) {\n            if (typeof key === \"string\") {\n              params[name] = part;\n              if (isLast === true) {\n                handlerSets.push(...this.gHSets(child, method, params, node.params));\n                if (child.children[\"*\"]) {\n                  handlerSets.push(...this.gHSets(child.children[\"*\"], method, params, node.params));\n                }\n              } else {\n                child.params = params;\n                tempNodes.push(child);\n              }\n            }\n          }\n        }\n      }\n      curNodes = tempNodes;\n    }\n    const results = handlerSets.sort((a, b) => {\n      return a.score - b.score;\n    });\n    return [results.map(({ handler, params }) => [handler, params])];\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/hono/dist/router/trie-router/node.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/hono/dist/router/trie-router/router.js":
/*!*************************************************************!*\
  !*** ./node_modules/hono/dist/router/trie-router/router.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TrieRouter: () => (/* binding */ TrieRouter)\n/* harmony export */ });\n/* harmony import */ var _utils_url_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../utils/url.js */ \"(rsc)/./node_modules/hono/dist/utils/url.js\");\n/* harmony import */ var _node_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node.js */ \"(rsc)/./node_modules/hono/dist/router/trie-router/node.js\");\n// src/router/trie-router/router.ts\n\n\nvar TrieRouter = class {\n  name = \"TrieRouter\";\n  node;\n  constructor() {\n    this.node = new _node_js__WEBPACK_IMPORTED_MODULE_1__.Node();\n  }\n  add(method, path, handler) {\n    const results = (0,_utils_url_js__WEBPACK_IMPORTED_MODULE_0__.checkOptionalParameter)(path);\n    if (results) {\n      for (const p of results) {\n        this.node.insert(method, p, handler);\n      }\n      return;\n    }\n    this.node.insert(method, path, handler);\n  }\n  match(method, path) {\n    return this.node.search(method, path);\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvaG9uby9kaXN0L3JvdXRlci90cmllLXJvdXRlci9yb3V0ZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUE7QUFDNEQ7QUFDM0I7QUFDakM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsMENBQUk7QUFDeEI7QUFDQTtBQUNBLG9CQUFvQixxRUFBc0I7QUFDMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBR0UiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90aGUtY2FudmFzLy4vbm9kZV9tb2R1bGVzL2hvbm8vZGlzdC9yb3V0ZXIvdHJpZS1yb3V0ZXIvcm91dGVyLmpzPzAzOWEiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL3JvdXRlci90cmllLXJvdXRlci9yb3V0ZXIudHNcbmltcG9ydCB7IGNoZWNrT3B0aW9uYWxQYXJhbWV0ZXIgfSBmcm9tIFwiLi4vLi4vdXRpbHMvdXJsLmpzXCI7XG5pbXBvcnQgeyBOb2RlIH0gZnJvbSBcIi4vbm9kZS5qc1wiO1xudmFyIFRyaWVSb3V0ZXIgPSBjbGFzcyB7XG4gIG5hbWUgPSBcIlRyaWVSb3V0ZXJcIjtcbiAgbm9kZTtcbiAgY29uc3RydWN0b3IoKSB7XG4gICAgdGhpcy5ub2RlID0gbmV3IE5vZGUoKTtcbiAgfVxuICBhZGQobWV0aG9kLCBwYXRoLCBoYW5kbGVyKSB7XG4gICAgY29uc3QgcmVzdWx0cyA9IGNoZWNrT3B0aW9uYWxQYXJhbWV0ZXIocGF0aCk7XG4gICAgaWYgKHJlc3VsdHMpIHtcbiAgICAgIGZvciAoY29uc3QgcCBvZiByZXN1bHRzKSB7XG4gICAgICAgIHRoaXMubm9kZS5pbnNlcnQobWV0aG9kLCBwLCBoYW5kbGVyKTtcbiAgICAgIH1cbiAgICAgIHJldHVybjtcbiAgICB9XG4gICAgdGhpcy5ub2RlLmluc2VydChtZXRob2QsIHBhdGgsIGhhbmRsZXIpO1xuICB9XG4gIG1hdGNoKG1ldGhvZCwgcGF0aCkge1xuICAgIHJldHVybiB0aGlzLm5vZGUuc2VhcmNoKG1ldGhvZCwgcGF0aCk7XG4gIH1cbn07XG5leHBvcnQge1xuICBUcmllUm91dGVyXG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/hono/dist/router/trie-router/router.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/hono/dist/utils/body.js":
/*!**********************************************!*\
  !*** ./node_modules/hono/dist/utils/body.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseBody: () => (/* binding */ parseBody)\n/* harmony export */ });\n/* harmony import */ var _request_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../request.js */ \"(rsc)/./node_modules/hono/dist/request.js\");\n// src/utils/body.ts\n\nvar parseBody = async (request, options = /* @__PURE__ */ Object.create(null)) => {\n  const { all = false, dot = false } = options;\n  const headers = request instanceof _request_js__WEBPACK_IMPORTED_MODULE_0__.HonoRequest ? request.raw.headers : request.headers;\n  const contentType = headers.get(\"Content-Type\");\n  if (contentType !== null && contentType.startsWith(\"multipart/form-data\") || contentType !== null && contentType.startsWith(\"application/x-www-form-urlencoded\")) {\n    return parseFormData(request, { all, dot });\n  }\n  return {};\n};\nasync function parseFormData(request, options) {\n  const formData = await request.formData();\n  if (formData) {\n    return convertFormDataToBodyData(formData, options);\n  }\n  return {};\n}\nfunction convertFormDataToBodyData(formData, options) {\n  const form = /* @__PURE__ */ Object.create(null);\n  formData.forEach((value, key) => {\n    const shouldParseAllValues = options.all || key.endsWith(\"[]\");\n    if (!shouldParseAllValues) {\n      form[key] = value;\n    } else {\n      handleParsingAllValues(form, key, value);\n    }\n  });\n  if (options.dot) {\n    Object.entries(form).forEach(([key, value]) => {\n      const shouldParseDotValues = key.includes(\".\");\n      if (shouldParseDotValues) {\n        handleParsingNestedValues(form, key, value);\n        delete form[key];\n      }\n    });\n  }\n  return form;\n}\nvar handleParsingAllValues = (form, key, value) => {\n  if (form[key] !== void 0) {\n    if (Array.isArray(form[key])) {\n      ;\n      form[key].push(value);\n    } else {\n      form[key] = [form[key], value];\n    }\n  } else {\n    form[key] = value;\n  }\n};\nvar handleParsingNestedValues = (form, key, value) => {\n  let nestedForm = form;\n  const keys = key.split(\".\");\n  keys.forEach((key2, index) => {\n    if (index === keys.length - 1) {\n      nestedForm[key2] = value;\n    } else {\n      if (!nestedForm[key2] || typeof nestedForm[key2] !== \"object\" || Array.isArray(nestedForm[key2]) || nestedForm[key2] instanceof File) {\n        nestedForm[key2] = /* @__PURE__ */ Object.create(null);\n      }\n      nestedForm = nestedForm[key2];\n    }\n  });\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/hono/dist/utils/body.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/hono/dist/utils/buffer.js":
/*!************************************************!*\
  !*** ./node_modules/hono/dist/utils/buffer.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bufferToFormData: () => (/* binding */ bufferToFormData),\n/* harmony export */   bufferToString: () => (/* binding */ bufferToString),\n/* harmony export */   equal: () => (/* binding */ equal),\n/* harmony export */   timingSafeEqual: () => (/* binding */ timingSafeEqual)\n/* harmony export */ });\n/* harmony import */ var _crypto_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./crypto.js */ \"(rsc)/./node_modules/hono/dist/utils/crypto.js\");\n// src/utils/buffer.ts\n\nvar equal = (a, b) => {\n  if (a === b) {\n    return true;\n  }\n  if (a.byteLength !== b.byteLength) {\n    return false;\n  }\n  const va = new DataView(a);\n  const vb = new DataView(b);\n  let i = va.byteLength;\n  while (i--) {\n    if (va.getUint8(i) !== vb.getUint8(i)) {\n      return false;\n    }\n  }\n  return true;\n};\nvar timingSafeEqual = async (a, b, hashFunction) => {\n  if (!hashFunction) {\n    hashFunction = _crypto_js__WEBPACK_IMPORTED_MODULE_0__.sha256;\n  }\n  const [sa, sb] = await Promise.all([hashFunction(a), hashFunction(b)]);\n  if (!sa || !sb) {\n    return false;\n  }\n  return sa === sb && a === b;\n};\nvar bufferToString = (buffer) => {\n  if (buffer instanceof ArrayBuffer) {\n    const enc = new TextDecoder(\"utf-8\");\n    return enc.decode(buffer);\n  }\n  return buffer;\n};\nvar bufferToFormData = (arrayBuffer, contentType) => {\n  const response = new Response(arrayBuffer, {\n    headers: {\n      \"Content-Type\": contentType\n    }\n  });\n  return response.formData();\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/hono/dist/utils/buffer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/hono/dist/utils/cookie.js":
/*!************************************************!*\
  !*** ./node_modules/hono/dist/utils/cookie.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parse: () => (/* binding */ parse),\n/* harmony export */   parseSigned: () => (/* binding */ parseSigned),\n/* harmony export */   serialize: () => (/* binding */ serialize),\n/* harmony export */   serializeSigned: () => (/* binding */ serializeSigned)\n/* harmony export */ });\n/* harmony import */ var _url_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./url.js */ \"(rsc)/./node_modules/hono/dist/utils/url.js\");\n// src/utils/cookie.ts\n\nvar algorithm = { name: \"HMAC\", hash: \"SHA-256\" };\nvar getCryptoKey = async (secret) => {\n  const secretBuf = typeof secret === \"string\" ? new TextEncoder().encode(secret) : secret;\n  return await crypto.subtle.importKey(\"raw\", secretBuf, algorithm, false, [\"sign\", \"verify\"]);\n};\nvar makeSignature = async (value, secret) => {\n  const key = await getCryptoKey(secret);\n  const signature = await crypto.subtle.sign(algorithm.name, key, new TextEncoder().encode(value));\n  return btoa(String.fromCharCode(...new Uint8Array(signature)));\n};\nvar verifySignature = async (base64Signature, value, secret) => {\n  try {\n    const signatureBinStr = atob(base64Signature);\n    const signature = new Uint8Array(signatureBinStr.length);\n    for (let i = 0, len = signatureBinStr.length; i < len; i++) {\n      signature[i] = signatureBinStr.charCodeAt(i);\n    }\n    return await crypto.subtle.verify(algorithm, secret, signature, new TextEncoder().encode(value));\n  } catch (e) {\n    return false;\n  }\n};\nvar validCookieNameRegEx = /^[\\w!#$%&'*.^`|~+-]+$/;\nvar validCookieValueRegEx = /^[ !#-:<-[\\]-~]*$/;\nvar parse = (cookie, name) => {\n  const pairs = cookie.trim().split(\";\");\n  return pairs.reduce((parsedCookie, pairStr) => {\n    pairStr = pairStr.trim();\n    const valueStartPos = pairStr.indexOf(\"=\");\n    if (valueStartPos === -1) {\n      return parsedCookie;\n    }\n    const cookieName = pairStr.substring(0, valueStartPos).trim();\n    if (name && name !== cookieName || !validCookieNameRegEx.test(cookieName)) {\n      return parsedCookie;\n    }\n    let cookieValue = pairStr.substring(valueStartPos + 1).trim();\n    if (cookieValue.startsWith('\"') && cookieValue.endsWith('\"')) {\n      cookieValue = cookieValue.slice(1, -1);\n    }\n    if (validCookieValueRegEx.test(cookieValue)) {\n      parsedCookie[cookieName] = (0,_url_js__WEBPACK_IMPORTED_MODULE_0__.decodeURIComponent_)(cookieValue);\n    }\n    return parsedCookie;\n  }, {});\n};\nvar parseSigned = async (cookie, secret, name) => {\n  const parsedCookie = {};\n  const secretKey = await getCryptoKey(secret);\n  for (const [key, value] of Object.entries(parse(cookie, name))) {\n    const signatureStartPos = value.lastIndexOf(\".\");\n    if (signatureStartPos < 1) {\n      continue;\n    }\n    const signedValue = value.substring(0, signatureStartPos);\n    const signature = value.substring(signatureStartPos + 1);\n    if (signature.length !== 44 || !signature.endsWith(\"=\")) {\n      continue;\n    }\n    const isVerified = await verifySignature(signature, signedValue, secretKey);\n    parsedCookie[key] = isVerified ? signedValue : false;\n  }\n  return parsedCookie;\n};\nvar _serialize = (name, value, opt = {}) => {\n  let cookie = `${name}=${value}`;\n  if (name.startsWith(\"__Secure-\") && !opt.secure) {\n    throw new Error(\"__Secure- Cookie must have Secure attributes\");\n  }\n  if (name.startsWith(\"__Host-\")) {\n    if (!opt.secure) {\n      throw new Error(\"__Host- Cookie must have Secure attributes\");\n    }\n    if (opt.path !== \"/\") {\n      throw new Error('__Host- Cookie must have Path attributes with \"/\"');\n    }\n    if (opt.domain) {\n      throw new Error(\"__Host- Cookie must not have Domain attributes\");\n    }\n  }\n  if (opt && typeof opt.maxAge === \"number\" && opt.maxAge >= 0) {\n    if (opt.maxAge > 3456e4) {\n      throw new Error(\n        \"Cookies Max-Age SHOULD NOT be greater than 400 days (34560000 seconds) in duration.\"\n      );\n    }\n    cookie += `; Max-Age=${Math.floor(opt.maxAge)}`;\n  }\n  if (opt.domain && opt.prefix !== \"host\") {\n    cookie += `; Domain=${opt.domain}`;\n  }\n  if (opt.path) {\n    cookie += `; Path=${opt.path}`;\n  }\n  if (opt.expires) {\n    if (opt.expires.getTime() - Date.now() > 3456e7) {\n      throw new Error(\n        \"Cookies Expires SHOULD NOT be greater than 400 days (34560000 seconds) in the future.\"\n      );\n    }\n    cookie += `; Expires=${opt.expires.toUTCString()}`;\n  }\n  if (opt.httpOnly) {\n    cookie += \"; HttpOnly\";\n  }\n  if (opt.secure) {\n    cookie += \"; Secure\";\n  }\n  if (opt.sameSite) {\n    cookie += `; SameSite=${opt.sameSite.charAt(0).toUpperCase() + opt.sameSite.slice(1)}`;\n  }\n  if (opt.partitioned) {\n    if (!opt.secure) {\n      throw new Error(\"Partitioned Cookie must have Secure attributes\");\n    }\n    cookie += \"; Partitioned\";\n  }\n  return cookie;\n};\nvar serialize = (name, value, opt) => {\n  value = encodeURIComponent(value);\n  return _serialize(name, value, opt);\n};\nvar serializeSigned = async (name, value, secret, opt = {}) => {\n  const signature = await makeSignature(value, secret);\n  value = `${value}.${signature}`;\n  value = encodeURIComponent(value);\n  return _serialize(name, value, opt);\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/hono/dist/utils/cookie.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/hono/dist/utils/crypto.js":
/*!************************************************!*\
  !*** ./node_modules/hono/dist/utils/crypto.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createHash: () => (/* binding */ createHash),\n/* harmony export */   md5: () => (/* binding */ md5),\n/* harmony export */   sha1: () => (/* binding */ sha1),\n/* harmony export */   sha256: () => (/* binding */ sha256)\n/* harmony export */ });\n// src/utils/crypto.ts\nvar sha256 = async (data) => {\n  const algorithm = { name: \"SHA-256\", alias: \"sha256\" };\n  const hash = await createHash(data, algorithm);\n  return hash;\n};\nvar sha1 = async (data) => {\n  const algorithm = { name: \"SHA-1\", alias: \"sha1\" };\n  const hash = await createHash(data, algorithm);\n  return hash;\n};\nvar md5 = async (data) => {\n  const algorithm = { name: \"MD5\", alias: \"md5\" };\n  const hash = await createHash(data, algorithm);\n  return hash;\n};\nvar createHash = async (data, algorithm) => {\n  let sourceBuffer;\n  if (data instanceof ReadableStream) {\n    let body = \"\";\n    const reader = data.getReader();\n    await reader?.read().then(async (chuck) => {\n      const value = await createHash(chuck.value || \"\", algorithm);\n      body += value;\n    });\n    return body;\n  }\n  if (ArrayBuffer.isView(data) || data instanceof ArrayBuffer) {\n    sourceBuffer = data;\n  } else {\n    if (typeof data === \"object\") {\n      data = JSON.stringify(data);\n    }\n    sourceBuffer = new TextEncoder().encode(String(data));\n  }\n  if (crypto && crypto.subtle) {\n    const buffer = await crypto.subtle.digest(\n      {\n        name: algorithm.name\n      },\n      sourceBuffer\n    );\n    const hash = Array.prototype.map.call(new Uint8Array(buffer), (x) => (\"00\" + x.toString(16)).slice(-2)).join(\"\");\n    return hash;\n  }\n  return null;\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/hono/dist/utils/crypto.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/hono/dist/utils/html.js":
/*!**********************************************!*\
  !*** ./node_modules/hono/dist/utils/html.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HtmlEscapedCallbackPhase: () => (/* binding */ HtmlEscapedCallbackPhase),\n/* harmony export */   escapeToBuffer: () => (/* binding */ escapeToBuffer),\n/* harmony export */   raw: () => (/* binding */ raw),\n/* harmony export */   resolveCallback: () => (/* binding */ resolveCallback),\n/* harmony export */   stringBufferToString: () => (/* binding */ stringBufferToString)\n/* harmony export */ });\n// src/utils/html.ts\nvar HtmlEscapedCallbackPhase = {\n  Stringify: 1,\n  BeforeStream: 2,\n  Stream: 3\n};\nvar raw = (value, callbacks) => {\n  const escapedString = new String(value);\n  escapedString.isEscaped = true;\n  escapedString.callbacks = callbacks;\n  return escapedString;\n};\nvar escapeRe = /[&<>'\"]/;\nvar stringBufferToString = async (buffer) => {\n  let str = \"\";\n  const callbacks = [];\n  for (let i = buffer.length - 1; ; i--) {\n    str += buffer[i];\n    i--;\n    if (i < 0) {\n      break;\n    }\n    let r = await buffer[i];\n    if (typeof r === \"object\") {\n      callbacks.push(...r.callbacks || []);\n    }\n    const isEscaped = r.isEscaped;\n    r = await (typeof r === \"object\" ? r.toString() : r);\n    if (typeof r === \"object\") {\n      callbacks.push(...r.callbacks || []);\n    }\n    if (r.isEscaped ?? isEscaped) {\n      str += r;\n    } else {\n      const buf = [str];\n      escapeToBuffer(r, buf);\n      str = buf[0];\n    }\n  }\n  return raw(str, callbacks);\n};\nvar escapeToBuffer = (str, buffer) => {\n  const match = str.search(escapeRe);\n  if (match === -1) {\n    buffer[0] += str;\n    return;\n  }\n  let escape;\n  let index;\n  let lastIndex = 0;\n  for (index = match; index < str.length; index++) {\n    switch (str.charCodeAt(index)) {\n      case 34:\n        escape = \"&quot;\";\n        break;\n      case 39:\n        escape = \"&#39;\";\n        break;\n      case 38:\n        escape = \"&amp;\";\n        break;\n      case 60:\n        escape = \"&lt;\";\n        break;\n      case 62:\n        escape = \"&gt;\";\n        break;\n      default:\n        continue;\n    }\n    buffer[0] += str.substring(lastIndex, index) + escape;\n    lastIndex = index + 1;\n  }\n  buffer[0] += str.substring(lastIndex, index);\n};\nvar resolveCallback = async (str, phase, preserveCallbacks, context, buffer) => {\n  const callbacks = str.callbacks;\n  if (!callbacks?.length) {\n    return Promise.resolve(str);\n  }\n  if (buffer) {\n    buffer[0] += str;\n  } else {\n    buffer = [str];\n  }\n  const resStr = Promise.all(callbacks.map((c) => c({ phase, buffer, context }))).then(\n    (res) => Promise.all(\n      res.filter(Boolean).map((str2) => resolveCallback(str2, phase, false, context, buffer))\n    ).then(() => buffer[0])\n  );\n  if (preserveCallbacks) {\n    return raw(await resStr, callbacks);\n  } else {\n    return resStr;\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/hono/dist/utils/html.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/hono/dist/utils/url.js":
/*!*********************************************!*\
  !*** ./node_modules/hono/dist/utils/url.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkOptionalParameter: () => (/* binding */ checkOptionalParameter),\n/* harmony export */   decodeURIComponent_: () => (/* binding */ decodeURIComponent_),\n/* harmony export */   getPath: () => (/* binding */ getPath),\n/* harmony export */   getPathNoStrict: () => (/* binding */ getPathNoStrict),\n/* harmony export */   getPattern: () => (/* binding */ getPattern),\n/* harmony export */   getQueryParam: () => (/* binding */ getQueryParam),\n/* harmony export */   getQueryParams: () => (/* binding */ getQueryParams),\n/* harmony export */   getQueryStrings: () => (/* binding */ getQueryStrings),\n/* harmony export */   mergePath: () => (/* binding */ mergePath),\n/* harmony export */   splitPath: () => (/* binding */ splitPath),\n/* harmony export */   splitRoutingPath: () => (/* binding */ splitRoutingPath)\n/* harmony export */ });\n// src/utils/url.ts\nvar splitPath = (path) => {\n  const paths = path.split(\"/\");\n  if (paths[0] === \"\") {\n    paths.shift();\n  }\n  return paths;\n};\nvar splitRoutingPath = (routePath) => {\n  const { groups, path } = extractGroupsFromPath(routePath);\n  const paths = splitPath(path);\n  return replaceGroupMarks(paths, groups);\n};\nvar extractGroupsFromPath = (path) => {\n  const groups = [];\n  path = path.replace(/\\{[^}]+\\}/g, (match, index) => {\n    const mark = `@${index}`;\n    groups.push([mark, match]);\n    return mark;\n  });\n  return { groups, path };\n};\nvar replaceGroupMarks = (paths, groups) => {\n  for (let i = groups.length - 1; i >= 0; i--) {\n    const [mark] = groups[i];\n    for (let j = paths.length - 1; j >= 0; j--) {\n      if (paths[j].includes(mark)) {\n        paths[j] = paths[j].replace(mark, groups[i][1]);\n        break;\n      }\n    }\n  }\n  return paths;\n};\nvar patternCache = {};\nvar getPattern = (label) => {\n  if (label === \"*\") {\n    return \"*\";\n  }\n  const match = label.match(/^\\:([^\\{\\}]+)(?:\\{(.+)\\})?$/);\n  if (match) {\n    if (!patternCache[label]) {\n      if (match[2]) {\n        patternCache[label] = [label, match[1], new RegExp(\"^\" + match[2] + \"$\")];\n      } else {\n        patternCache[label] = [label, match[1], true];\n      }\n    }\n    return patternCache[label];\n  }\n  return null;\n};\nvar tryDecodeURI = (str) => {\n  try {\n    return decodeURI(str);\n  } catch {\n    return str.replace(/(?:%[0-9A-Fa-f]{2})+/g, (match) => {\n      try {\n        return decodeURI(match);\n      } catch {\n        return match;\n      }\n    });\n  }\n};\nvar getPath = (request) => {\n  const url = request.url;\n  const start = url.indexOf(\"/\", 8);\n  let i = start;\n  for (; i < url.length; i++) {\n    const charCode = url.charCodeAt(i);\n    if (charCode === 37) {\n      const queryIndex = url.indexOf(\"?\", i);\n      const path = url.slice(start, queryIndex === -1 ? void 0 : queryIndex);\n      return tryDecodeURI(path.includes(\"%25\") ? path.replace(/%25/g, \"%2525\") : path);\n    } else if (charCode === 63) {\n      break;\n    }\n  }\n  return url.slice(start, i);\n};\nvar getQueryStrings = (url) => {\n  const queryIndex = url.indexOf(\"?\", 8);\n  return queryIndex === -1 ? \"\" : \"?\" + url.slice(queryIndex + 1);\n};\nvar getPathNoStrict = (request) => {\n  const result = getPath(request);\n  return result.length > 1 && result[result.length - 1] === \"/\" ? result.slice(0, -1) : result;\n};\nvar mergePath = (...paths) => {\n  let p = \"\";\n  let endsWithSlash = false;\n  for (let path of paths) {\n    if (p[p.length - 1] === \"/\") {\n      p = p.slice(0, -1);\n      endsWithSlash = true;\n    }\n    if (path[0] !== \"/\") {\n      path = `/${path}`;\n    }\n    if (path === \"/\" && endsWithSlash) {\n      p = `${p}/`;\n    } else if (path !== \"/\") {\n      p = `${p}${path}`;\n    }\n    if (path === \"/\" && p === \"\") {\n      p = \"/\";\n    }\n  }\n  return p;\n};\nvar checkOptionalParameter = (path) => {\n  if (!path.match(/\\:.+\\?$/)) {\n    return null;\n  }\n  const segments = path.split(\"/\");\n  const results = [];\n  let basePath = \"\";\n  segments.forEach((segment) => {\n    if (segment !== \"\" && !/\\:/.test(segment)) {\n      basePath += \"/\" + segment;\n    } else if (/\\:/.test(segment)) {\n      if (/\\?/.test(segment)) {\n        if (results.length === 0 && basePath === \"\") {\n          results.push(\"/\");\n        } else {\n          results.push(basePath);\n        }\n        const optionalSegment = segment.replace(\"?\", \"\");\n        basePath += \"/\" + optionalSegment;\n        results.push(basePath);\n      } else {\n        basePath += \"/\" + segment;\n      }\n    }\n  });\n  return results.filter((v, i, a) => a.indexOf(v) === i);\n};\nvar _decodeURI = (value) => {\n  if (!/[%+]/.test(value)) {\n    return value;\n  }\n  if (value.indexOf(\"+\") !== -1) {\n    value = value.replace(/\\+/g, \" \");\n  }\n  return /%/.test(value) ? decodeURIComponent_(value) : value;\n};\nvar _getQueryParam = (url, key, multiple) => {\n  let encoded;\n  if (!multiple && key && !/[%+]/.test(key)) {\n    let keyIndex2 = url.indexOf(`?${key}`, 8);\n    if (keyIndex2 === -1) {\n      keyIndex2 = url.indexOf(`&${key}`, 8);\n    }\n    while (keyIndex2 !== -1) {\n      const trailingKeyCode = url.charCodeAt(keyIndex2 + key.length + 1);\n      if (trailingKeyCode === 61) {\n        const valueIndex = keyIndex2 + key.length + 2;\n        const endIndex = url.indexOf(\"&\", valueIndex);\n        return _decodeURI(url.slice(valueIndex, endIndex === -1 ? void 0 : endIndex));\n      } else if (trailingKeyCode == 38 || isNaN(trailingKeyCode)) {\n        return \"\";\n      }\n      keyIndex2 = url.indexOf(`&${key}`, keyIndex2 + 1);\n    }\n    encoded = /[%+]/.test(url);\n    if (!encoded) {\n      return void 0;\n    }\n  }\n  const results = {};\n  encoded ??= /[%+]/.test(url);\n  let keyIndex = url.indexOf(\"?\", 8);\n  while (keyIndex !== -1) {\n    const nextKeyIndex = url.indexOf(\"&\", keyIndex + 1);\n    let valueIndex = url.indexOf(\"=\", keyIndex);\n    if (valueIndex > nextKeyIndex && nextKeyIndex !== -1) {\n      valueIndex = -1;\n    }\n    let name = url.slice(\n      keyIndex + 1,\n      valueIndex === -1 ? nextKeyIndex === -1 ? void 0 : nextKeyIndex : valueIndex\n    );\n    if (encoded) {\n      name = _decodeURI(name);\n    }\n    keyIndex = nextKeyIndex;\n    if (name === \"\") {\n      continue;\n    }\n    let value;\n    if (valueIndex === -1) {\n      value = \"\";\n    } else {\n      value = url.slice(valueIndex + 1, nextKeyIndex === -1 ? void 0 : nextKeyIndex);\n      if (encoded) {\n        value = _decodeURI(value);\n      }\n    }\n    if (multiple) {\n      if (!(results[name] && Array.isArray(results[name]))) {\n        results[name] = [];\n      }\n      ;\n      results[name].push(value);\n    } else {\n      results[name] ??= value;\n    }\n  }\n  return key ? results[key] : results;\n};\nvar getQueryParam = _getQueryParam;\nvar getQueryParams = (url, key) => {\n  return _getQueryParam(url, key, true);\n};\nvar decodeURIComponent_ = decodeURIComponent;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvaG9uby9kaXN0L3V0aWxzL3VybC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVUsZUFBZTtBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCLEdBQUcsSUFBSTtBQUNoQyxxQkFBcUIsTUFBTTtBQUMzQjtBQUNBO0FBQ0EsR0FBRztBQUNILFdBQVc7QUFDWDtBQUNBO0FBQ0Esa0NBQWtDLFFBQVE7QUFDMUM7QUFDQSxtQ0FBbUMsUUFBUTtBQUMzQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFDQUFxQyxFQUFFLFFBQVEsTUFBTTtBQUNyRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSix3Q0FBd0MsRUFBRTtBQUMxQztBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUyxnQkFBZ0I7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQixLQUFLO0FBQ3RCO0FBQ0E7QUFDQSxhQUFhLEVBQUU7QUFDZixNQUFNO0FBQ04sYUFBYSxFQUFFLEVBQUUsS0FBSztBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0NBQW9DLElBQUk7QUFDeEM7QUFDQSxrQ0FBa0MsSUFBSTtBQUN0QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0Esa0NBQWtDLElBQUk7QUFDdEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBYUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90aGUtY2FudmFzLy4vbm9kZV9tb2R1bGVzL2hvbm8vZGlzdC91dGlscy91cmwuanM/YmVjNiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvdXRpbHMvdXJsLnRzXG52YXIgc3BsaXRQYXRoID0gKHBhdGgpID0+IHtcbiAgY29uc3QgcGF0aHMgPSBwYXRoLnNwbGl0KFwiL1wiKTtcbiAgaWYgKHBhdGhzWzBdID09PSBcIlwiKSB7XG4gICAgcGF0aHMuc2hpZnQoKTtcbiAgfVxuICByZXR1cm4gcGF0aHM7XG59O1xudmFyIHNwbGl0Um91dGluZ1BhdGggPSAocm91dGVQYXRoKSA9PiB7XG4gIGNvbnN0IHsgZ3JvdXBzLCBwYXRoIH0gPSBleHRyYWN0R3JvdXBzRnJvbVBhdGgocm91dGVQYXRoKTtcbiAgY29uc3QgcGF0aHMgPSBzcGxpdFBhdGgocGF0aCk7XG4gIHJldHVybiByZXBsYWNlR3JvdXBNYXJrcyhwYXRocywgZ3JvdXBzKTtcbn07XG52YXIgZXh0cmFjdEdyb3Vwc0Zyb21QYXRoID0gKHBhdGgpID0+IHtcbiAgY29uc3QgZ3JvdXBzID0gW107XG4gIHBhdGggPSBwYXRoLnJlcGxhY2UoL1xce1tefV0rXFx9L2csIChtYXRjaCwgaW5kZXgpID0+IHtcbiAgICBjb25zdCBtYXJrID0gYEAke2luZGV4fWA7XG4gICAgZ3JvdXBzLnB1c2goW21hcmssIG1hdGNoXSk7XG4gICAgcmV0dXJuIG1hcms7XG4gIH0pO1xuICByZXR1cm4geyBncm91cHMsIHBhdGggfTtcbn07XG52YXIgcmVwbGFjZUdyb3VwTWFya3MgPSAocGF0aHMsIGdyb3VwcykgPT4ge1xuICBmb3IgKGxldCBpID0gZ3JvdXBzLmxlbmd0aCAtIDE7IGkgPj0gMDsgaS0tKSB7XG4gICAgY29uc3QgW21hcmtdID0gZ3JvdXBzW2ldO1xuICAgIGZvciAobGV0IGogPSBwYXRocy5sZW5ndGggLSAxOyBqID49IDA7IGotLSkge1xuICAgICAgaWYgKHBhdGhzW2pdLmluY2x1ZGVzKG1hcmspKSB7XG4gICAgICAgIHBhdGhzW2pdID0gcGF0aHNbal0ucmVwbGFjZShtYXJrLCBncm91cHNbaV1bMV0pO1xuICAgICAgICBicmVhaztcbiAgICAgIH1cbiAgICB9XG4gIH1cbiAgcmV0dXJuIHBhdGhzO1xufTtcbnZhciBwYXR0ZXJuQ2FjaGUgPSB7fTtcbnZhciBnZXRQYXR0ZXJuID0gKGxhYmVsKSA9PiB7XG4gIGlmIChsYWJlbCA9PT0gXCIqXCIpIHtcbiAgICByZXR1cm4gXCIqXCI7XG4gIH1cbiAgY29uc3QgbWF0Y2ggPSBsYWJlbC5tYXRjaCgvXlxcOihbXlxce1xcfV0rKSg/OlxceyguKylcXH0pPyQvKTtcbiAgaWYgKG1hdGNoKSB7XG4gICAgaWYgKCFwYXR0ZXJuQ2FjaGVbbGFiZWxdKSB7XG4gICAgICBpZiAobWF0Y2hbMl0pIHtcbiAgICAgICAgcGF0dGVybkNhY2hlW2xhYmVsXSA9IFtsYWJlbCwgbWF0Y2hbMV0sIG5ldyBSZWdFeHAoXCJeXCIgKyBtYXRjaFsyXSArIFwiJFwiKV07XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBwYXR0ZXJuQ2FjaGVbbGFiZWxdID0gW2xhYmVsLCBtYXRjaFsxXSwgdHJ1ZV07XG4gICAgICB9XG4gICAgfVxuICAgIHJldHVybiBwYXR0ZXJuQ2FjaGVbbGFiZWxdO1xuICB9XG4gIHJldHVybiBudWxsO1xufTtcbnZhciB0cnlEZWNvZGVVUkkgPSAoc3RyKSA9PiB7XG4gIHRyeSB7XG4gICAgcmV0dXJuIGRlY29kZVVSSShzdHIpO1xuICB9IGNhdGNoIHtcbiAgICByZXR1cm4gc3RyLnJlcGxhY2UoLyg/OiVbMC05QS1GYS1mXXsyfSkrL2csIChtYXRjaCkgPT4ge1xuICAgICAgdHJ5IHtcbiAgICAgICAgcmV0dXJuIGRlY29kZVVSSShtYXRjaCk7XG4gICAgICB9IGNhdGNoIHtcbiAgICAgICAgcmV0dXJuIG1hdGNoO1xuICAgICAgfVxuICAgIH0pO1xuICB9XG59O1xudmFyIGdldFBhdGggPSAocmVxdWVzdCkgPT4ge1xuICBjb25zdCB1cmwgPSByZXF1ZXN0LnVybDtcbiAgY29uc3Qgc3RhcnQgPSB1cmwuaW5kZXhPZihcIi9cIiwgOCk7XG4gIGxldCBpID0gc3RhcnQ7XG4gIGZvciAoOyBpIDwgdXJsLmxlbmd0aDsgaSsrKSB7XG4gICAgY29uc3QgY2hhckNvZGUgPSB1cmwuY2hhckNvZGVBdChpKTtcbiAgICBpZiAoY2hhckNvZGUgPT09IDM3KSB7XG4gICAgICBjb25zdCBxdWVyeUluZGV4ID0gdXJsLmluZGV4T2YoXCI/XCIsIGkpO1xuICAgICAgY29uc3QgcGF0aCA9IHVybC5zbGljZShzdGFydCwgcXVlcnlJbmRleCA9PT0gLTEgPyB2b2lkIDAgOiBxdWVyeUluZGV4KTtcbiAgICAgIHJldHVybiB0cnlEZWNvZGVVUkkocGF0aC5pbmNsdWRlcyhcIiUyNVwiKSA/IHBhdGgucmVwbGFjZSgvJTI1L2csIFwiJTI1MjVcIikgOiBwYXRoKTtcbiAgICB9IGVsc2UgaWYgKGNoYXJDb2RlID09PSA2Mykge1xuICAgICAgYnJlYWs7XG4gICAgfVxuICB9XG4gIHJldHVybiB1cmwuc2xpY2Uoc3RhcnQsIGkpO1xufTtcbnZhciBnZXRRdWVyeVN0cmluZ3MgPSAodXJsKSA9PiB7XG4gIGNvbnN0IHF1ZXJ5SW5kZXggPSB1cmwuaW5kZXhPZihcIj9cIiwgOCk7XG4gIHJldHVybiBxdWVyeUluZGV4ID09PSAtMSA/IFwiXCIgOiBcIj9cIiArIHVybC5zbGljZShxdWVyeUluZGV4ICsgMSk7XG59O1xudmFyIGdldFBhdGhOb1N0cmljdCA9IChyZXF1ZXN0KSA9PiB7XG4gIGNvbnN0IHJlc3VsdCA9IGdldFBhdGgocmVxdWVzdCk7XG4gIHJldHVybiByZXN1bHQubGVuZ3RoID4gMSAmJiByZXN1bHRbcmVzdWx0Lmxlbmd0aCAtIDFdID09PSBcIi9cIiA/IHJlc3VsdC5zbGljZSgwLCAtMSkgOiByZXN1bHQ7XG59O1xudmFyIG1lcmdlUGF0aCA9ICguLi5wYXRocykgPT4ge1xuICBsZXQgcCA9IFwiXCI7XG4gIGxldCBlbmRzV2l0aFNsYXNoID0gZmFsc2U7XG4gIGZvciAobGV0IHBhdGggb2YgcGF0aHMpIHtcbiAgICBpZiAocFtwLmxlbmd0aCAtIDFdID09PSBcIi9cIikge1xuICAgICAgcCA9IHAuc2xpY2UoMCwgLTEpO1xuICAgICAgZW5kc1dpdGhTbGFzaCA9IHRydWU7XG4gICAgfVxuICAgIGlmIChwYXRoWzBdICE9PSBcIi9cIikge1xuICAgICAgcGF0aCA9IGAvJHtwYXRofWA7XG4gICAgfVxuICAgIGlmIChwYXRoID09PSBcIi9cIiAmJiBlbmRzV2l0aFNsYXNoKSB7XG4gICAgICBwID0gYCR7cH0vYDtcbiAgICB9IGVsc2UgaWYgKHBhdGggIT09IFwiL1wiKSB7XG4gICAgICBwID0gYCR7cH0ke3BhdGh9YDtcbiAgICB9XG4gICAgaWYgKHBhdGggPT09IFwiL1wiICYmIHAgPT09IFwiXCIpIHtcbiAgICAgIHAgPSBcIi9cIjtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIHA7XG59O1xudmFyIGNoZWNrT3B0aW9uYWxQYXJhbWV0ZXIgPSAocGF0aCkgPT4ge1xuICBpZiAoIXBhdGgubWF0Y2goL1xcOi4rXFw/JC8pKSB7XG4gICAgcmV0dXJuIG51bGw7XG4gIH1cbiAgY29uc3Qgc2VnbWVudHMgPSBwYXRoLnNwbGl0KFwiL1wiKTtcbiAgY29uc3QgcmVzdWx0cyA9IFtdO1xuICBsZXQgYmFzZVBhdGggPSBcIlwiO1xuICBzZWdtZW50cy5mb3JFYWNoKChzZWdtZW50KSA9PiB7XG4gICAgaWYgKHNlZ21lbnQgIT09IFwiXCIgJiYgIS9cXDovLnRlc3Qoc2VnbWVudCkpIHtcbiAgICAgIGJhc2VQYXRoICs9IFwiL1wiICsgc2VnbWVudDtcbiAgICB9IGVsc2UgaWYgKC9cXDovLnRlc3Qoc2VnbWVudCkpIHtcbiAgICAgIGlmICgvXFw/Ly50ZXN0KHNlZ21lbnQpKSB7XG4gICAgICAgIGlmIChyZXN1bHRzLmxlbmd0aCA9PT0gMCAmJiBiYXNlUGF0aCA9PT0gXCJcIikge1xuICAgICAgICAgIHJlc3VsdHMucHVzaChcIi9cIik7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgcmVzdWx0cy5wdXNoKGJhc2VQYXRoKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBvcHRpb25hbFNlZ21lbnQgPSBzZWdtZW50LnJlcGxhY2UoXCI/XCIsIFwiXCIpO1xuICAgICAgICBiYXNlUGF0aCArPSBcIi9cIiArIG9wdGlvbmFsU2VnbWVudDtcbiAgICAgICAgcmVzdWx0cy5wdXNoKGJhc2VQYXRoKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGJhc2VQYXRoICs9IFwiL1wiICsgc2VnbWVudDtcbiAgICAgIH1cbiAgICB9XG4gIH0pO1xuICByZXR1cm4gcmVzdWx0cy5maWx0ZXIoKHYsIGksIGEpID0+IGEuaW5kZXhPZih2KSA9PT0gaSk7XG59O1xudmFyIF9kZWNvZGVVUkkgPSAodmFsdWUpID0+IHtcbiAgaWYgKCEvWyUrXS8udGVzdCh2YWx1ZSkpIHtcbiAgICByZXR1cm4gdmFsdWU7XG4gIH1cbiAgaWYgKHZhbHVlLmluZGV4T2YoXCIrXCIpICE9PSAtMSkge1xuICAgIHZhbHVlID0gdmFsdWUucmVwbGFjZSgvXFwrL2csIFwiIFwiKTtcbiAgfVxuICByZXR1cm4gLyUvLnRlc3QodmFsdWUpID8gZGVjb2RlVVJJQ29tcG9uZW50Xyh2YWx1ZSkgOiB2YWx1ZTtcbn07XG52YXIgX2dldFF1ZXJ5UGFyYW0gPSAodXJsLCBrZXksIG11bHRpcGxlKSA9PiB7XG4gIGxldCBlbmNvZGVkO1xuICBpZiAoIW11bHRpcGxlICYmIGtleSAmJiAhL1slK10vLnRlc3Qoa2V5KSkge1xuICAgIGxldCBrZXlJbmRleDIgPSB1cmwuaW5kZXhPZihgPyR7a2V5fWAsIDgpO1xuICAgIGlmIChrZXlJbmRleDIgPT09IC0xKSB7XG4gICAgICBrZXlJbmRleDIgPSB1cmwuaW5kZXhPZihgJiR7a2V5fWAsIDgpO1xuICAgIH1cbiAgICB3aGlsZSAoa2V5SW5kZXgyICE9PSAtMSkge1xuICAgICAgY29uc3QgdHJhaWxpbmdLZXlDb2RlID0gdXJsLmNoYXJDb2RlQXQoa2V5SW5kZXgyICsga2V5Lmxlbmd0aCArIDEpO1xuICAgICAgaWYgKHRyYWlsaW5nS2V5Q29kZSA9PT0gNjEpIHtcbiAgICAgICAgY29uc3QgdmFsdWVJbmRleCA9IGtleUluZGV4MiArIGtleS5sZW5ndGggKyAyO1xuICAgICAgICBjb25zdCBlbmRJbmRleCA9IHVybC5pbmRleE9mKFwiJlwiLCB2YWx1ZUluZGV4KTtcbiAgICAgICAgcmV0dXJuIF9kZWNvZGVVUkkodXJsLnNsaWNlKHZhbHVlSW5kZXgsIGVuZEluZGV4ID09PSAtMSA/IHZvaWQgMCA6IGVuZEluZGV4KSk7XG4gICAgICB9IGVsc2UgaWYgKHRyYWlsaW5nS2V5Q29kZSA9PSAzOCB8fCBpc05hTih0cmFpbGluZ0tleUNvZGUpKSB7XG4gICAgICAgIHJldHVybiBcIlwiO1xuICAgICAgfVxuICAgICAga2V5SW5kZXgyID0gdXJsLmluZGV4T2YoYCYke2tleX1gLCBrZXlJbmRleDIgKyAxKTtcbiAgICB9XG4gICAgZW5jb2RlZCA9IC9bJStdLy50ZXN0KHVybCk7XG4gICAgaWYgKCFlbmNvZGVkKSB7XG4gICAgICByZXR1cm4gdm9pZCAwO1xuICAgIH1cbiAgfVxuICBjb25zdCByZXN1bHRzID0ge307XG4gIGVuY29kZWQgPz89IC9bJStdLy50ZXN0KHVybCk7XG4gIGxldCBrZXlJbmRleCA9IHVybC5pbmRleE9mKFwiP1wiLCA4KTtcbiAgd2hpbGUgKGtleUluZGV4ICE9PSAtMSkge1xuICAgIGNvbnN0IG5leHRLZXlJbmRleCA9IHVybC5pbmRleE9mKFwiJlwiLCBrZXlJbmRleCArIDEpO1xuICAgIGxldCB2YWx1ZUluZGV4ID0gdXJsLmluZGV4T2YoXCI9XCIsIGtleUluZGV4KTtcbiAgICBpZiAodmFsdWVJbmRleCA+IG5leHRLZXlJbmRleCAmJiBuZXh0S2V5SW5kZXggIT09IC0xKSB7XG4gICAgICB2YWx1ZUluZGV4ID0gLTE7XG4gICAgfVxuICAgIGxldCBuYW1lID0gdXJsLnNsaWNlKFxuICAgICAga2V5SW5kZXggKyAxLFxuICAgICAgdmFsdWVJbmRleCA9PT0gLTEgPyBuZXh0S2V5SW5kZXggPT09IC0xID8gdm9pZCAwIDogbmV4dEtleUluZGV4IDogdmFsdWVJbmRleFxuICAgICk7XG4gICAgaWYgKGVuY29kZWQpIHtcbiAgICAgIG5hbWUgPSBfZGVjb2RlVVJJKG5hbWUpO1xuICAgIH1cbiAgICBrZXlJbmRleCA9IG5leHRLZXlJbmRleDtcbiAgICBpZiAobmFtZSA9PT0gXCJcIikge1xuICAgICAgY29udGludWU7XG4gICAgfVxuICAgIGxldCB2YWx1ZTtcbiAgICBpZiAodmFsdWVJbmRleCA9PT0gLTEpIHtcbiAgICAgIHZhbHVlID0gXCJcIjtcbiAgICB9IGVsc2Uge1xuICAgICAgdmFsdWUgPSB1cmwuc2xpY2UodmFsdWVJbmRleCArIDEsIG5leHRLZXlJbmRleCA9PT0gLTEgPyB2b2lkIDAgOiBuZXh0S2V5SW5kZXgpO1xuICAgICAgaWYgKGVuY29kZWQpIHtcbiAgICAgICAgdmFsdWUgPSBfZGVjb2RlVVJJKHZhbHVlKTtcbiAgICAgIH1cbiAgICB9XG4gICAgaWYgKG11bHRpcGxlKSB7XG4gICAgICBpZiAoIShyZXN1bHRzW25hbWVdICYmIEFycmF5LmlzQXJyYXkocmVzdWx0c1tuYW1lXSkpKSB7XG4gICAgICAgIHJlc3VsdHNbbmFtZV0gPSBbXTtcbiAgICAgIH1cbiAgICAgIDtcbiAgICAgIHJlc3VsdHNbbmFtZV0ucHVzaCh2YWx1ZSk7XG4gICAgfSBlbHNlIHtcbiAgICAgIHJlc3VsdHNbbmFtZV0gPz89IHZhbHVlO1xuICAgIH1cbiAgfVxuICByZXR1cm4ga2V5ID8gcmVzdWx0c1trZXldIDogcmVzdWx0cztcbn07XG52YXIgZ2V0UXVlcnlQYXJhbSA9IF9nZXRRdWVyeVBhcmFtO1xudmFyIGdldFF1ZXJ5UGFyYW1zID0gKHVybCwga2V5KSA9PiB7XG4gIHJldHVybiBfZ2V0UXVlcnlQYXJhbSh1cmwsIGtleSwgdHJ1ZSk7XG59O1xudmFyIGRlY29kZVVSSUNvbXBvbmVudF8gPSBkZWNvZGVVUklDb21wb25lbnQ7XG5leHBvcnQge1xuICBjaGVja09wdGlvbmFsUGFyYW1ldGVyLFxuICBkZWNvZGVVUklDb21wb25lbnRfLFxuICBnZXRQYXRoLFxuICBnZXRQYXRoTm9TdHJpY3QsXG4gIGdldFBhdHRlcm4sXG4gIGdldFF1ZXJ5UGFyYW0sXG4gIGdldFF1ZXJ5UGFyYW1zLFxuICBnZXRRdWVyeVN0cmluZ3MsXG4gIG1lcmdlUGF0aCxcbiAgc3BsaXRQYXRoLFxuICBzcGxpdFJvdXRpbmdQYXRoXG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/hono/dist/utils/url.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/hono/dist/validator/index.js":
/*!***************************************************!*\
  !*** ./node_modules/hono/dist/validator/index.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   validator: () => (/* reexport safe */ _validator_js__WEBPACK_IMPORTED_MODULE_0__.validator)\n/* harmony export */ });\n/* harmony import */ var _validator_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./validator.js */ \"(rsc)/./node_modules/hono/dist/validator/validator.js\");\n// src/validator/index.ts\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvaG9uby9kaXN0L3ZhbGlkYXRvci9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQzJDO0FBR3pDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGhlLWNhbnZhcy8uL25vZGVfbW9kdWxlcy9ob25vL2Rpc3QvdmFsaWRhdG9yL2luZGV4LmpzPzgwMDgiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL3ZhbGlkYXRvci9pbmRleC50c1xuaW1wb3J0IHsgdmFsaWRhdG9yIH0gZnJvbSBcIi4vdmFsaWRhdG9yLmpzXCI7XG5leHBvcnQge1xuICB2YWxpZGF0b3Jcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/hono/dist/validator/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/hono/dist/validator/validator.js":
/*!*******************************************************!*\
  !*** ./node_modules/hono/dist/validator/validator.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   validator: () => (/* binding */ validator)\n/* harmony export */ });\n/* harmony import */ var _helper_cookie_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../helper/cookie/index.js */ \"(rsc)/./node_modules/hono/dist/helper/cookie/index.js\");\n/* harmony import */ var _http_exception_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../http-exception.js */ \"(rsc)/./node_modules/hono/dist/http-exception.js\");\n/* harmony import */ var _utils_buffer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/buffer.js */ \"(rsc)/./node_modules/hono/dist/utils/buffer.js\");\n// src/validator/validator.ts\n\n\n\nvar validator = (target, validationFunc) => {\n  return async (c, next) => {\n    let value = {};\n    const contentType = c.req.header(\"Content-Type\");\n    switch (target) {\n      case \"json\":\n        if (!contentType || !/^application\\/([a-z-\\.]+\\+)?json/.test(contentType)) {\n          const message = `Invalid HTTP header: Content-Type=${contentType}`;\n          throw new _http_exception_js__WEBPACK_IMPORTED_MODULE_1__.HTTPException(400, { message });\n        }\n        try {\n          value = await c.req.json();\n        } catch {\n          const message = \"Malformed JSON in request body\";\n          throw new _http_exception_js__WEBPACK_IMPORTED_MODULE_1__.HTTPException(400, { message });\n        }\n        break;\n      case \"form\": {\n        if (!contentType) {\n          break;\n        }\n        let formData;\n        if (c.req.bodyCache.formData) {\n          formData = await c.req.bodyCache.formData;\n        } else {\n          try {\n            const arrayBuffer = await c.req.arrayBuffer();\n            formData = await (0,_utils_buffer_js__WEBPACK_IMPORTED_MODULE_2__.bufferToFormData)(arrayBuffer, contentType);\n            c.req.bodyCache.formData = formData;\n          } catch (e) {\n            let message = \"Malformed FormData request.\";\n            message += e instanceof Error ? ` ${e.message}` : ` ${String(e)}`;\n            throw new _http_exception_js__WEBPACK_IMPORTED_MODULE_1__.HTTPException(400, { message });\n          }\n        }\n        const form = {};\n        formData.forEach((value2, key) => {\n          if (key.endsWith(\"[]\")) {\n            if (form[key] === void 0) {\n              form[key] = [value2];\n            } else if (Array.isArray(form[key])) {\n              ;\n              form[key].push(value2);\n            }\n          } else {\n            form[key] = value2;\n          }\n        });\n        value = form;\n        break;\n      }\n      case \"query\":\n        value = Object.fromEntries(\n          Object.entries(c.req.queries()).map(([k, v]) => {\n            return v.length === 1 ? [k, v[0]] : [k, v];\n          })\n        );\n        break;\n      case \"param\":\n        value = c.req.param();\n        break;\n      case \"header\":\n        value = c.req.header();\n        break;\n      case \"cookie\":\n        value = (0,_helper_cookie_index_js__WEBPACK_IMPORTED_MODULE_0__.getCookie)(c);\n        break;\n    }\n    const res = await validationFunc(value, c);\n    if (res instanceof Response) {\n      return res;\n    }\n    c.req.addValidatedData(target, res);\n    await next();\n  };\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/hono/dist/validator/validator.js\n");

/***/ })

};
;