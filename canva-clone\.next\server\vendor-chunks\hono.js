"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hono";
exports.ids = ["vendor-chunks/hono"];
exports.modules = {

/***/ "(ssr)/./node_modules/hono/dist/client/client.js":
/*!*************************************************!*\
  !*** ./node_modules/hono/dist/client/client.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hc: () => (/* binding */ hc)\n/* harmony export */ });\n/* harmony import */ var _utils_cookie_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/cookie.js */ \"(ssr)/./node_modules/hono/dist/utils/cookie.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/hono/dist/client/utils.js\");\n// src/client/client.ts\n\n\nvar createProxy = (callback, path) => {\n  const proxy = new Proxy(() => {\n  }, {\n    get(_obj, key) {\n      if (typeof key !== \"string\" || key === \"then\") {\n        return void 0;\n      }\n      return createProxy(callback, [...path, key]);\n    },\n    apply(_1, _2, args) {\n      return callback({\n        path,\n        args\n      });\n    }\n  });\n  return proxy;\n};\nvar ClientRequestImpl = class {\n  url;\n  method;\n  queryParams = void 0;\n  pathParams = {};\n  rBody;\n  cType = void 0;\n  constructor(url, method) {\n    this.url = url;\n    this.method = method;\n  }\n  fetch = async (args, opt) => {\n    if (args) {\n      if (args.query) {\n        for (const [k, v] of Object.entries(args.query)) {\n          if (v === void 0) {\n            continue;\n          }\n          this.queryParams ||= new URLSearchParams();\n          if (Array.isArray(v)) {\n            for (const v2 of v) {\n              this.queryParams.append(k, v2);\n            }\n          } else {\n            this.queryParams.set(k, v);\n          }\n        }\n      }\n      if (args.form) {\n        const form = new FormData();\n        for (const [k, v] of Object.entries(args.form)) {\n          if (Array.isArray(v)) {\n            for (const v2 of v) {\n              form.append(k, v2);\n            }\n          } else {\n            form.append(k, v);\n          }\n        }\n        this.rBody = form;\n      }\n      if (args.json) {\n        this.rBody = JSON.stringify(args.json);\n        this.cType = \"application/json\";\n      }\n      if (args.param) {\n        this.pathParams = args.param;\n      }\n    }\n    let methodUpperCase = this.method.toUpperCase();\n    const headerValues = {\n      ...args?.header ?? {},\n      ...typeof opt?.headers === \"function\" ? await opt.headers() : opt?.headers ? opt.headers : {}\n    };\n    if (args?.cookie) {\n      const cookies = [];\n      for (const [key, value] of Object.entries(args.cookie)) {\n        cookies.push((0,_utils_cookie_js__WEBPACK_IMPORTED_MODULE_0__.serialize)(key, value, { path: \"/\" }));\n      }\n      headerValues[\"Cookie\"] = cookies.join(\",\");\n    }\n    if (this.cType) {\n      headerValues[\"Content-Type\"] = this.cType;\n    }\n    const headers = new Headers(headerValues ?? void 0);\n    let url = this.url;\n    url = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.removeIndexString)(url);\n    url = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.replaceUrlParam)(url, this.pathParams);\n    if (this.queryParams) {\n      url = url + \"?\" + this.queryParams.toString();\n    }\n    methodUpperCase = this.method.toUpperCase();\n    const setBody = !(methodUpperCase === \"GET\" || methodUpperCase === \"HEAD\");\n    return (opt?.fetch || fetch)(url, {\n      body: setBody ? this.rBody : void 0,\n      method: methodUpperCase,\n      headers,\n      ...opt?.init\n    });\n  };\n};\nvar hc = (baseUrl, options) => createProxy(function proxyCallback(opts) {\n  const parts = [...opts.path];\n  if (parts[parts.length - 1] === \"toString\") {\n    if (parts[parts.length - 2] === \"name\") {\n      return parts[parts.length - 3] || \"\";\n    }\n    return proxyCallback.toString();\n  }\n  if (parts[parts.length - 1] === \"valueOf\") {\n    if (parts[parts.length - 2] === \"name\") {\n      return parts[parts.length - 3] || \"\";\n    }\n    return proxyCallback;\n  }\n  let method = \"\";\n  if (/^\\$/.test(parts[parts.length - 1])) {\n    const last = parts.pop();\n    if (last) {\n      method = last.replace(/^\\$/, \"\");\n    }\n  }\n  const path = parts.join(\"/\");\n  const url = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.mergePath)(baseUrl, path);\n  if (method === \"url\") {\n    if (opts.args[0] && opts.args[0].param) {\n      return new URL((0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.replaceUrlParam)(url, opts.args[0].param));\n    }\n    return new URL(url);\n  }\n  if (method === \"ws\") {\n    const webSocketUrl = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.replaceUrlProtocol)(\n      opts.args[0] && opts.args[0].param ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.replaceUrlParam)(url, opts.args[0].param) : url,\n      \"ws\"\n    );\n    const targetUrl = new URL(webSocketUrl);\n    for (const key in opts.args[0]?.query) {\n      targetUrl.searchParams.set(key, opts.args[0].query[key]);\n    }\n    return new WebSocket(targetUrl.toString());\n  }\n  const req = new ClientRequestImpl(url, method);\n  if (method) {\n    options ??= {};\n    const args = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.deepMerge)(options, { ...opts.args[1] ?? {} });\n    return req.fetch(opts.args[0], args);\n  }\n  return req;\n}, []);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hono/dist/client/client.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hono/dist/client/index.js":
/*!************************************************!*\
  !*** ./node_modules/hono/dist/client/index.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hc: () => (/* reexport safe */ _client_js__WEBPACK_IMPORTED_MODULE_0__.hc)\n/* harmony export */ });\n/* harmony import */ var _client_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client.js */ \"(ssr)/./node_modules/hono/dist/client/client.js\");\n// src/client/index.ts\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaG9uby9kaXN0L2NsaWVudC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ2lDO0FBRy9CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGhlLWNhbnZhcy8uL25vZGVfbW9kdWxlcy9ob25vL2Rpc3QvY2xpZW50L2luZGV4LmpzPzY2ZGEiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL2NsaWVudC9pbmRleC50c1xuaW1wb3J0IHsgaGMgfSBmcm9tIFwiLi9jbGllbnQuanNcIjtcbmV4cG9ydCB7XG4gIGhjXG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hono/dist/client/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hono/dist/client/utils.js":
/*!************************************************!*\
  !*** ./node_modules/hono/dist/client/utils.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deepMerge: () => (/* binding */ deepMerge),\n/* harmony export */   mergePath: () => (/* binding */ mergePath),\n/* harmony export */   removeIndexString: () => (/* binding */ removeIndexString),\n/* harmony export */   replaceUrlParam: () => (/* binding */ replaceUrlParam),\n/* harmony export */   replaceUrlProtocol: () => (/* binding */ replaceUrlProtocol)\n/* harmony export */ });\n// src/client/utils.ts\nvar mergePath = (base, path) => {\n  base = base.replace(/\\/+$/, \"\");\n  base = base + \"/\";\n  path = path.replace(/^\\/+/, \"\");\n  return base + path;\n};\nvar replaceUrlParam = (urlString, params) => {\n  for (const [k, v] of Object.entries(params)) {\n    const reg = new RegExp(\"/:\" + k + \"(?:{[^/]+})?\");\n    urlString = urlString.replace(reg, `/${v}`);\n  }\n  return urlString;\n};\nvar replaceUrlProtocol = (urlString, protocol) => {\n  switch (protocol) {\n    case \"ws\":\n      return urlString.replace(/^http/, \"ws\");\n    case \"http\":\n      return urlString.replace(/^ws/, \"http\");\n  }\n};\nvar removeIndexString = (urlSting) => {\n  if (/^https?:\\/\\/[^\\/]+?\\/index$/.test(urlSting)) {\n    return urlSting.replace(/\\/index$/, \"/\");\n  }\n  return urlSting.replace(/\\/index$/, \"\");\n};\nfunction isObject(item) {\n  return typeof item === \"object\" && item !== null && !Array.isArray(item);\n}\nfunction deepMerge(target, source) {\n  if (!isObject(target) && !isObject(source)) {\n    return source;\n  }\n  const merged = { ...target };\n  for (const key in source) {\n    const value = source[key];\n    if (isObject(merged[key]) && isObject(value)) {\n      merged[key] = deepMerge(merged[key], value);\n    } else {\n      merged[key] = value;\n    }\n  }\n  return merged;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hono/dist/client/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hono/dist/utils/cookie.js":
/*!************************************************!*\
  !*** ./node_modules/hono/dist/utils/cookie.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parse: () => (/* binding */ parse),\n/* harmony export */   parseSigned: () => (/* binding */ parseSigned),\n/* harmony export */   serialize: () => (/* binding */ serialize),\n/* harmony export */   serializeSigned: () => (/* binding */ serializeSigned)\n/* harmony export */ });\n/* harmony import */ var _url_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./url.js */ \"(ssr)/./node_modules/hono/dist/utils/url.js\");\n// src/utils/cookie.ts\n\nvar algorithm = { name: \"HMAC\", hash: \"SHA-256\" };\nvar getCryptoKey = async (secret) => {\n  const secretBuf = typeof secret === \"string\" ? new TextEncoder().encode(secret) : secret;\n  return await crypto.subtle.importKey(\"raw\", secretBuf, algorithm, false, [\"sign\", \"verify\"]);\n};\nvar makeSignature = async (value, secret) => {\n  const key = await getCryptoKey(secret);\n  const signature = await crypto.subtle.sign(algorithm.name, key, new TextEncoder().encode(value));\n  return btoa(String.fromCharCode(...new Uint8Array(signature)));\n};\nvar verifySignature = async (base64Signature, value, secret) => {\n  try {\n    const signatureBinStr = atob(base64Signature);\n    const signature = new Uint8Array(signatureBinStr.length);\n    for (let i = 0, len = signatureBinStr.length; i < len; i++) {\n      signature[i] = signatureBinStr.charCodeAt(i);\n    }\n    return await crypto.subtle.verify(algorithm, secret, signature, new TextEncoder().encode(value));\n  } catch (e) {\n    return false;\n  }\n};\nvar validCookieNameRegEx = /^[\\w!#$%&'*.^`|~+-]+$/;\nvar validCookieValueRegEx = /^[ !#-:<-[\\]-~]*$/;\nvar parse = (cookie, name) => {\n  const pairs = cookie.trim().split(\";\");\n  return pairs.reduce((parsedCookie, pairStr) => {\n    pairStr = pairStr.trim();\n    const valueStartPos = pairStr.indexOf(\"=\");\n    if (valueStartPos === -1) {\n      return parsedCookie;\n    }\n    const cookieName = pairStr.substring(0, valueStartPos).trim();\n    if (name && name !== cookieName || !validCookieNameRegEx.test(cookieName)) {\n      return parsedCookie;\n    }\n    let cookieValue = pairStr.substring(valueStartPos + 1).trim();\n    if (cookieValue.startsWith('\"') && cookieValue.endsWith('\"')) {\n      cookieValue = cookieValue.slice(1, -1);\n    }\n    if (validCookieValueRegEx.test(cookieValue)) {\n      parsedCookie[cookieName] = (0,_url_js__WEBPACK_IMPORTED_MODULE_0__.decodeURIComponent_)(cookieValue);\n    }\n    return parsedCookie;\n  }, {});\n};\nvar parseSigned = async (cookie, secret, name) => {\n  const parsedCookie = {};\n  const secretKey = await getCryptoKey(secret);\n  for (const [key, value] of Object.entries(parse(cookie, name))) {\n    const signatureStartPos = value.lastIndexOf(\".\");\n    if (signatureStartPos < 1) {\n      continue;\n    }\n    const signedValue = value.substring(0, signatureStartPos);\n    const signature = value.substring(signatureStartPos + 1);\n    if (signature.length !== 44 || !signature.endsWith(\"=\")) {\n      continue;\n    }\n    const isVerified = await verifySignature(signature, signedValue, secretKey);\n    parsedCookie[key] = isVerified ? signedValue : false;\n  }\n  return parsedCookie;\n};\nvar _serialize = (name, value, opt = {}) => {\n  let cookie = `${name}=${value}`;\n  if (name.startsWith(\"__Secure-\") && !opt.secure) {\n    throw new Error(\"__Secure- Cookie must have Secure attributes\");\n  }\n  if (name.startsWith(\"__Host-\")) {\n    if (!opt.secure) {\n      throw new Error(\"__Host- Cookie must have Secure attributes\");\n    }\n    if (opt.path !== \"/\") {\n      throw new Error('__Host- Cookie must have Path attributes with \"/\"');\n    }\n    if (opt.domain) {\n      throw new Error(\"__Host- Cookie must not have Domain attributes\");\n    }\n  }\n  if (opt && typeof opt.maxAge === \"number\" && opt.maxAge >= 0) {\n    if (opt.maxAge > 3456e4) {\n      throw new Error(\n        \"Cookies Max-Age SHOULD NOT be greater than 400 days (34560000 seconds) in duration.\"\n      );\n    }\n    cookie += `; Max-Age=${Math.floor(opt.maxAge)}`;\n  }\n  if (opt.domain && opt.prefix !== \"host\") {\n    cookie += `; Domain=${opt.domain}`;\n  }\n  if (opt.path) {\n    cookie += `; Path=${opt.path}`;\n  }\n  if (opt.expires) {\n    if (opt.expires.getTime() - Date.now() > 3456e7) {\n      throw new Error(\n        \"Cookies Expires SHOULD NOT be greater than 400 days (34560000 seconds) in the future.\"\n      );\n    }\n    cookie += `; Expires=${opt.expires.toUTCString()}`;\n  }\n  if (opt.httpOnly) {\n    cookie += \"; HttpOnly\";\n  }\n  if (opt.secure) {\n    cookie += \"; Secure\";\n  }\n  if (opt.sameSite) {\n    cookie += `; SameSite=${opt.sameSite.charAt(0).toUpperCase() + opt.sameSite.slice(1)}`;\n  }\n  if (opt.partitioned) {\n    if (!opt.secure) {\n      throw new Error(\"Partitioned Cookie must have Secure attributes\");\n    }\n    cookie += \"; Partitioned\";\n  }\n  return cookie;\n};\nvar serialize = (name, value, opt) => {\n  value = encodeURIComponent(value);\n  return _serialize(name, value, opt);\n};\nvar serializeSigned = async (name, value, secret, opt = {}) => {\n  const signature = await makeSignature(value, secret);\n  value = `${value}.${signature}`;\n  value = encodeURIComponent(value);\n  return _serialize(name, value, opt);\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hono/dist/utils/cookie.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hono/dist/utils/url.js":
/*!*********************************************!*\
  !*** ./node_modules/hono/dist/utils/url.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkOptionalParameter: () => (/* binding */ checkOptionalParameter),\n/* harmony export */   decodeURIComponent_: () => (/* binding */ decodeURIComponent_),\n/* harmony export */   getPath: () => (/* binding */ getPath),\n/* harmony export */   getPathNoStrict: () => (/* binding */ getPathNoStrict),\n/* harmony export */   getPattern: () => (/* binding */ getPattern),\n/* harmony export */   getQueryParam: () => (/* binding */ getQueryParam),\n/* harmony export */   getQueryParams: () => (/* binding */ getQueryParams),\n/* harmony export */   getQueryStrings: () => (/* binding */ getQueryStrings),\n/* harmony export */   mergePath: () => (/* binding */ mergePath),\n/* harmony export */   splitPath: () => (/* binding */ splitPath),\n/* harmony export */   splitRoutingPath: () => (/* binding */ splitRoutingPath)\n/* harmony export */ });\n// src/utils/url.ts\nvar splitPath = (path) => {\n  const paths = path.split(\"/\");\n  if (paths[0] === \"\") {\n    paths.shift();\n  }\n  return paths;\n};\nvar splitRoutingPath = (routePath) => {\n  const { groups, path } = extractGroupsFromPath(routePath);\n  const paths = splitPath(path);\n  return replaceGroupMarks(paths, groups);\n};\nvar extractGroupsFromPath = (path) => {\n  const groups = [];\n  path = path.replace(/\\{[^}]+\\}/g, (match, index) => {\n    const mark = `@${index}`;\n    groups.push([mark, match]);\n    return mark;\n  });\n  return { groups, path };\n};\nvar replaceGroupMarks = (paths, groups) => {\n  for (let i = groups.length - 1; i >= 0; i--) {\n    const [mark] = groups[i];\n    for (let j = paths.length - 1; j >= 0; j--) {\n      if (paths[j].includes(mark)) {\n        paths[j] = paths[j].replace(mark, groups[i][1]);\n        break;\n      }\n    }\n  }\n  return paths;\n};\nvar patternCache = {};\nvar getPattern = (label) => {\n  if (label === \"*\") {\n    return \"*\";\n  }\n  const match = label.match(/^\\:([^\\{\\}]+)(?:\\{(.+)\\})?$/);\n  if (match) {\n    if (!patternCache[label]) {\n      if (match[2]) {\n        patternCache[label] = [label, match[1], new RegExp(\"^\" + match[2] + \"$\")];\n      } else {\n        patternCache[label] = [label, match[1], true];\n      }\n    }\n    return patternCache[label];\n  }\n  return null;\n};\nvar tryDecodeURI = (str) => {\n  try {\n    return decodeURI(str);\n  } catch {\n    return str.replace(/(?:%[0-9A-Fa-f]{2})+/g, (match) => {\n      try {\n        return decodeURI(match);\n      } catch {\n        return match;\n      }\n    });\n  }\n};\nvar getPath = (request) => {\n  const url = request.url;\n  const start = url.indexOf(\"/\", 8);\n  let i = start;\n  for (; i < url.length; i++) {\n    const charCode = url.charCodeAt(i);\n    if (charCode === 37) {\n      const queryIndex = url.indexOf(\"?\", i);\n      const path = url.slice(start, queryIndex === -1 ? void 0 : queryIndex);\n      return tryDecodeURI(path.includes(\"%25\") ? path.replace(/%25/g, \"%2525\") : path);\n    } else if (charCode === 63) {\n      break;\n    }\n  }\n  return url.slice(start, i);\n};\nvar getQueryStrings = (url) => {\n  const queryIndex = url.indexOf(\"?\", 8);\n  return queryIndex === -1 ? \"\" : \"?\" + url.slice(queryIndex + 1);\n};\nvar getPathNoStrict = (request) => {\n  const result = getPath(request);\n  return result.length > 1 && result[result.length - 1] === \"/\" ? result.slice(0, -1) : result;\n};\nvar mergePath = (...paths) => {\n  let p = \"\";\n  let endsWithSlash = false;\n  for (let path of paths) {\n    if (p[p.length - 1] === \"/\") {\n      p = p.slice(0, -1);\n      endsWithSlash = true;\n    }\n    if (path[0] !== \"/\") {\n      path = `/${path}`;\n    }\n    if (path === \"/\" && endsWithSlash) {\n      p = `${p}/`;\n    } else if (path !== \"/\") {\n      p = `${p}${path}`;\n    }\n    if (path === \"/\" && p === \"\") {\n      p = \"/\";\n    }\n  }\n  return p;\n};\nvar checkOptionalParameter = (path) => {\n  if (!path.match(/\\:.+\\?$/)) {\n    return null;\n  }\n  const segments = path.split(\"/\");\n  const results = [];\n  let basePath = \"\";\n  segments.forEach((segment) => {\n    if (segment !== \"\" && !/\\:/.test(segment)) {\n      basePath += \"/\" + segment;\n    } else if (/\\:/.test(segment)) {\n      if (/\\?/.test(segment)) {\n        if (results.length === 0 && basePath === \"\") {\n          results.push(\"/\");\n        } else {\n          results.push(basePath);\n        }\n        const optionalSegment = segment.replace(\"?\", \"\");\n        basePath += \"/\" + optionalSegment;\n        results.push(basePath);\n      } else {\n        basePath += \"/\" + segment;\n      }\n    }\n  });\n  return results.filter((v, i, a) => a.indexOf(v) === i);\n};\nvar _decodeURI = (value) => {\n  if (!/[%+]/.test(value)) {\n    return value;\n  }\n  if (value.indexOf(\"+\") !== -1) {\n    value = value.replace(/\\+/g, \" \");\n  }\n  return /%/.test(value) ? decodeURIComponent_(value) : value;\n};\nvar _getQueryParam = (url, key, multiple) => {\n  let encoded;\n  if (!multiple && key && !/[%+]/.test(key)) {\n    let keyIndex2 = url.indexOf(`?${key}`, 8);\n    if (keyIndex2 === -1) {\n      keyIndex2 = url.indexOf(`&${key}`, 8);\n    }\n    while (keyIndex2 !== -1) {\n      const trailingKeyCode = url.charCodeAt(keyIndex2 + key.length + 1);\n      if (trailingKeyCode === 61) {\n        const valueIndex = keyIndex2 + key.length + 2;\n        const endIndex = url.indexOf(\"&\", valueIndex);\n        return _decodeURI(url.slice(valueIndex, endIndex === -1 ? void 0 : endIndex));\n      } else if (trailingKeyCode == 38 || isNaN(trailingKeyCode)) {\n        return \"\";\n      }\n      keyIndex2 = url.indexOf(`&${key}`, keyIndex2 + 1);\n    }\n    encoded = /[%+]/.test(url);\n    if (!encoded) {\n      return void 0;\n    }\n  }\n  const results = {};\n  encoded ??= /[%+]/.test(url);\n  let keyIndex = url.indexOf(\"?\", 8);\n  while (keyIndex !== -1) {\n    const nextKeyIndex = url.indexOf(\"&\", keyIndex + 1);\n    let valueIndex = url.indexOf(\"=\", keyIndex);\n    if (valueIndex > nextKeyIndex && nextKeyIndex !== -1) {\n      valueIndex = -1;\n    }\n    let name = url.slice(\n      keyIndex + 1,\n      valueIndex === -1 ? nextKeyIndex === -1 ? void 0 : nextKeyIndex : valueIndex\n    );\n    if (encoded) {\n      name = _decodeURI(name);\n    }\n    keyIndex = nextKeyIndex;\n    if (name === \"\") {\n      continue;\n    }\n    let value;\n    if (valueIndex === -1) {\n      value = \"\";\n    } else {\n      value = url.slice(valueIndex + 1, nextKeyIndex === -1 ? void 0 : nextKeyIndex);\n      if (encoded) {\n        value = _decodeURI(value);\n      }\n    }\n    if (multiple) {\n      if (!(results[name] && Array.isArray(results[name]))) {\n        results[name] = [];\n      }\n      ;\n      results[name].push(value);\n    } else {\n      results[name] ??= value;\n    }\n  }\n  return key ? results[key] : results;\n};\nvar getQueryParam = _getQueryParam;\nvar getQueryParams = (url, key) => {\n  return _getQueryParam(url, key, true);\n};\nvar decodeURIComponent_ = decodeURIComponent;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hono/dist/utils/url.js\n");

/***/ })

};
;