"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/uuidv4";
exports.ids = ["vendor-chunks/uuidv4"];
exports.modules = {

/***/ "(ssr)/./node_modules/uuidv4/build/lib/uuidv4.js":
/*!*************************************************!*\
  !*** ./node_modules/uuidv4/build/lib/uuidv4.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.jsonSchema = exports.fromString = exports.empty = exports.isUuid = exports.regex = exports.uuid = void 0;\nconst util_1 = __webpack_require__(/*! util */ \"util\");\nconst uuid_1 = __webpack_require__(/*! uuid */ \"(ssr)/./node_modules/uuid/dist/esm-node/index.js\");\nconst regex = {\n    v4: /(?:^[a-f0-9]{8}-[a-f0-9]{4}-4[a-f0-9]{3}-[a-f0-9]{4}-[a-f0-9]{12}$)|(?:^0{8}-0{4}-0{4}-0{4}-0{12}$)/u,\n    v5: /(?:^[a-f0-9]{8}-[a-f0-9]{4}-5[a-f0-9]{3}-[a-f0-9]{4}-[a-f0-9]{12}$)|(?:^0{8}-0{4}-0{4}-0{4}-0{12}$)/u\n};\nexports.regex = regex;\nconst jsonSchema = {\n    v4: { type: 'string', pattern: regex.v4.toString().slice(1, -2) },\n    v5: { type: 'string', pattern: regex.v5.toString().slice(1, -2) }\n};\nexports.jsonSchema = jsonSchema;\nconst uuidv4 = (0, util_1.deprecate)(() => (0, uuid_1.v4)(), 'uuidv4() is deprecated. Use v4() from the uuid module instead.');\nexports.uuid = uuidv4;\nconst isUuid = (0, util_1.deprecate)((value) => (0, uuid_1.validate)(value) && ((0, uuid_1.version)(value) === 4 || (0, uuid_1.version)(value) === 5), 'isUuid() is deprecated. Use validate() from the uuid module instead.');\nexports.isUuid = isUuid;\nconst empty = (0, util_1.deprecate)(() => uuid_1.NIL, 'empty() is deprecated. Use NIL from the uuid module instead.');\nexports.empty = empty;\nconst fromString = (0, util_1.deprecate)((text, namespace = 'bb5d0ffa-9a4c-4d7c-8fc2-0a7d2220ba45') => (0, uuid_1.v5)(text, namespace), 'fromString() is deprecated. Use v5() from the uuid module instead.');\nexports.fromString = fromString;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/uuidv4/build/lib/uuidv4.js\n");

/***/ })

};
;