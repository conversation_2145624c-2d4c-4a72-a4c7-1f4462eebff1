"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/consola";
exports.ids = ["vendor-chunks/consola"];
exports.modules = {

/***/ "(rsc)/./node_modules/consola/dist/core.mjs":
/*!********************************************!*\
  !*** ./node_modules/consola/dist/core.mjs ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Consola: () => (/* binding */ Consola),\n/* harmony export */   LogLevels: () => (/* binding */ LogLevels),\n/* harmony export */   LogTypes: () => (/* binding */ LogTypes),\n/* harmony export */   createConsola: () => (/* binding */ createConsola)\n/* harmony export */ });\nconst LogLevels = {\n  silent: Number.NEGATIVE_INFINITY,\n  fatal: 0,\n  error: 0,\n  warn: 1,\n  log: 2,\n  info: 3,\n  success: 3,\n  fail: 3,\n  ready: 3,\n  start: 3,\n  box: 3,\n  debug: 4,\n  trace: 5,\n  verbose: Number.POSITIVE_INFINITY\n};\nconst LogTypes = {\n  // Silent\n  silent: {\n    level: -1\n  },\n  // Level 0\n  fatal: {\n    level: LogLevels.fatal\n  },\n  error: {\n    level: LogLevels.error\n  },\n  // Level 1\n  warn: {\n    level: LogLevels.warn\n  },\n  // Level 2\n  log: {\n    level: LogLevels.log\n  },\n  // Level 3\n  info: {\n    level: LogLevels.info\n  },\n  success: {\n    level: LogLevels.success\n  },\n  fail: {\n    level: LogLevels.fail\n  },\n  ready: {\n    level: LogLevels.info\n  },\n  start: {\n    level: LogLevels.info\n  },\n  box: {\n    level: LogLevels.info\n  },\n  // Level 4\n  debug: {\n    level: LogLevels.debug\n  },\n  // Level 5\n  trace: {\n    level: LogLevels.trace\n  },\n  // Verbose\n  verbose: {\n    level: LogLevels.verbose\n  }\n};\n\nfunction isObject(value) {\n  return value !== null && typeof value === \"object\";\n}\nfunction _defu(baseObject, defaults, namespace = \".\", merger) {\n  if (!isObject(defaults)) {\n    return _defu(baseObject, {}, namespace, merger);\n  }\n  const object = Object.assign({}, defaults);\n  for (const key in baseObject) {\n    if (key === \"__proto__\" || key === \"constructor\") {\n      continue;\n    }\n    const value = baseObject[key];\n    if (value === null || value === void 0) {\n      continue;\n    }\n    if (merger && merger(object, key, value, namespace)) {\n      continue;\n    }\n    if (Array.isArray(value) && Array.isArray(object[key])) {\n      object[key] = [...value, ...object[key]];\n    } else if (isObject(value) && isObject(object[key])) {\n      object[key] = _defu(\n        value,\n        object[key],\n        (namespace ? `${namespace}.` : \"\") + key.toString(),\n        merger\n      );\n    } else {\n      object[key] = value;\n    }\n  }\n  return object;\n}\nfunction createDefu(merger) {\n  return (...arguments_) => (\n    // eslint-disable-next-line unicorn/no-array-reduce\n    arguments_.reduce((p, c) => _defu(p, c, \"\", merger), {})\n  );\n}\nconst defu = createDefu();\n\nfunction isPlainObject(obj) {\n  return Object.prototype.toString.call(obj) === \"[object Object]\";\n}\nfunction isLogObj(arg) {\n  if (!isPlainObject(arg)) {\n    return false;\n  }\n  if (!arg.message && !arg.args) {\n    return false;\n  }\n  if (arg.stack) {\n    return false;\n  }\n  return true;\n}\n\nlet paused = false;\nconst queue = [];\nclass Consola {\n  constructor(options = {}) {\n    const types = options.types || LogTypes;\n    this.options = defu(\n      {\n        ...options,\n        defaults: { ...options.defaults },\n        level: _normalizeLogLevel(options.level, types),\n        reporters: [...options.reporters || []]\n      },\n      {\n        types: LogTypes,\n        throttle: 1e3,\n        throttleMin: 5,\n        formatOptions: {\n          date: true,\n          colors: false,\n          compact: true\n        }\n      }\n    );\n    for (const type in types) {\n      const defaults = {\n        type,\n        ...this.options.defaults,\n        ...types[type]\n      };\n      this[type] = this._wrapLogFn(defaults);\n      this[type].raw = this._wrapLogFn(\n        defaults,\n        true\n      );\n    }\n    if (this.options.mockFn) {\n      this.mockTypes();\n    }\n    this._lastLog = {};\n  }\n  get level() {\n    return this.options.level;\n  }\n  set level(level) {\n    this.options.level = _normalizeLogLevel(\n      level,\n      this.options.types,\n      this.options.level\n    );\n  }\n  prompt(message, opts) {\n    if (!this.options.prompt) {\n      throw new Error(\"prompt is not supported!\");\n    }\n    return this.options.prompt(message, opts);\n  }\n  create(options) {\n    const instance = new Consola({\n      ...this.options,\n      ...options\n    });\n    if (this._mockFn) {\n      instance.mockTypes(this._mockFn);\n    }\n    return instance;\n  }\n  withDefaults(defaults) {\n    return this.create({\n      ...this.options,\n      defaults: {\n        ...this.options.defaults,\n        ...defaults\n      }\n    });\n  }\n  withTag(tag) {\n    return this.withDefaults({\n      tag: this.options.defaults.tag ? this.options.defaults.tag + \":\" + tag : tag\n    });\n  }\n  addReporter(reporter) {\n    this.options.reporters.push(reporter);\n    return this;\n  }\n  removeReporter(reporter) {\n    if (reporter) {\n      const i = this.options.reporters.indexOf(reporter);\n      if (i >= 0) {\n        return this.options.reporters.splice(i, 1);\n      }\n    } else {\n      this.options.reporters.splice(0);\n    }\n    return this;\n  }\n  setReporters(reporters) {\n    this.options.reporters = Array.isArray(reporters) ? reporters : [reporters];\n    return this;\n  }\n  wrapAll() {\n    this.wrapConsole();\n    this.wrapStd();\n  }\n  restoreAll() {\n    this.restoreConsole();\n    this.restoreStd();\n  }\n  wrapConsole() {\n    for (const type in this.options.types) {\n      if (!console[\"__\" + type]) {\n        console[\"__\" + type] = console[type];\n      }\n      console[type] = this[type].raw;\n    }\n  }\n  restoreConsole() {\n    for (const type in this.options.types) {\n      if (console[\"__\" + type]) {\n        console[type] = console[\"__\" + type];\n        delete console[\"__\" + type];\n      }\n    }\n  }\n  wrapStd() {\n    this._wrapStream(this.options.stdout, \"log\");\n    this._wrapStream(this.options.stderr, \"log\");\n  }\n  _wrapStream(stream, type) {\n    if (!stream) {\n      return;\n    }\n    if (!stream.__write) {\n      stream.__write = stream.write;\n    }\n    stream.write = (data) => {\n      this[type].raw(String(data).trim());\n    };\n  }\n  restoreStd() {\n    this._restoreStream(this.options.stdout);\n    this._restoreStream(this.options.stderr);\n  }\n  _restoreStream(stream) {\n    if (!stream) {\n      return;\n    }\n    if (stream.__write) {\n      stream.write = stream.__write;\n      delete stream.__write;\n    }\n  }\n  pauseLogs() {\n    paused = true;\n  }\n  resumeLogs() {\n    paused = false;\n    const _queue = queue.splice(0);\n    for (const item of _queue) {\n      item[0]._logFn(item[1], item[2]);\n    }\n  }\n  mockTypes(mockFn) {\n    const _mockFn = mockFn || this.options.mockFn;\n    this._mockFn = _mockFn;\n    if (typeof _mockFn !== \"function\") {\n      return;\n    }\n    for (const type in this.options.types) {\n      this[type] = _mockFn(type, this.options.types[type]) || this[type];\n      this[type].raw = this[type];\n    }\n  }\n  _wrapLogFn(defaults, isRaw) {\n    return (...args) => {\n      if (paused) {\n        queue.push([this, defaults, args, isRaw]);\n        return;\n      }\n      return this._logFn(defaults, args, isRaw);\n    };\n  }\n  _logFn(defaults, args, isRaw) {\n    if ((defaults.level || 0) > this.level) {\n      return false;\n    }\n    const logObj = {\n      date: /* @__PURE__ */ new Date(),\n      args: [],\n      ...defaults,\n      level: _normalizeLogLevel(defaults.level, this.options.types)\n    };\n    if (!isRaw && args.length === 1 && isLogObj(args[0])) {\n      Object.assign(logObj, args[0]);\n    } else {\n      logObj.args = [...args];\n    }\n    if (logObj.message) {\n      logObj.args.unshift(logObj.message);\n      delete logObj.message;\n    }\n    if (logObj.additional) {\n      if (!Array.isArray(logObj.additional)) {\n        logObj.additional = logObj.additional.split(\"\\n\");\n      }\n      logObj.args.push(\"\\n\" + logObj.additional.join(\"\\n\"));\n      delete logObj.additional;\n    }\n    logObj.type = typeof logObj.type === \"string\" ? logObj.type.toLowerCase() : \"log\";\n    logObj.tag = typeof logObj.tag === \"string\" ? logObj.tag : \"\";\n    const resolveLog = (newLog = false) => {\n      const repeated = (this._lastLog.count || 0) - this.options.throttleMin;\n      if (this._lastLog.object && repeated > 0) {\n        const args2 = [...this._lastLog.object.args];\n        if (repeated > 1) {\n          args2.push(`(repeated ${repeated} times)`);\n        }\n        this._log({ ...this._lastLog.object, args: args2 });\n        this._lastLog.count = 1;\n      }\n      if (newLog) {\n        this._lastLog.object = logObj;\n        this._log(logObj);\n      }\n    };\n    clearTimeout(this._lastLog.timeout);\n    const diffTime = this._lastLog.time && logObj.date ? logObj.date.getTime() - this._lastLog.time.getTime() : 0;\n    this._lastLog.time = logObj.date;\n    if (diffTime < this.options.throttle) {\n      try {\n        const serializedLog = JSON.stringify([\n          logObj.type,\n          logObj.tag,\n          logObj.args\n        ]);\n        const isSameLog = this._lastLog.serialized === serializedLog;\n        this._lastLog.serialized = serializedLog;\n        if (isSameLog) {\n          this._lastLog.count = (this._lastLog.count || 0) + 1;\n          if (this._lastLog.count > this.options.throttleMin) {\n            this._lastLog.timeout = setTimeout(\n              resolveLog,\n              this.options.throttle\n            );\n            return;\n          }\n        }\n      } catch {\n      }\n    }\n    resolveLog(true);\n  }\n  _log(logObj) {\n    for (const reporter of this.options.reporters) {\n      reporter.log(logObj, {\n        options: this.options\n      });\n    }\n  }\n}\nfunction _normalizeLogLevel(input, types = {}, defaultLevel = 3) {\n  if (input === void 0) {\n    return defaultLevel;\n  }\n  if (typeof input === \"number\") {\n    return input;\n  }\n  if (types[input] && types[input].level !== void 0) {\n    return types[input].level;\n  }\n  return defaultLevel;\n}\nConsola.prototype.add = Consola.prototype.addReporter;\nConsola.prototype.remove = Consola.prototype.removeReporter;\nConsola.prototype.clear = Consola.prototype.removeReporter;\nConsola.prototype.withScope = Consola.prototype.withTag;\nConsola.prototype.mock = Consola.prototype.mockTypes;\nConsola.prototype.pause = Consola.prototype.pauseLogs;\nConsola.prototype.resume = Consola.prototype.resumeLogs;\nfunction createConsola(options = {}) {\n  return new Consola(options);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/consola/dist/core.mjs\n");

/***/ })

};
;