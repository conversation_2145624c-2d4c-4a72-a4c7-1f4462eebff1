/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/bcryptjs";
exports.ids = ["vendor-chunks/bcryptjs"];
exports.modules = {

/***/ "(rsc)/./node_modules/bcryptjs/dist/bcrypt.js":
/*!**********************************************!*\
  !*** ./node_modules/bcryptjs/dist/bcrypt.js ***!
  \**********************************************/
/***/ (function(module, exports, __webpack_require__) {

eval("/* module decorator */ module = __webpack_require__.nmd(module);\nvar __WEBPACK_AMD_DEFINE_FACTORY__, __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_RESULT__;/*\r\n Copyright (c) 2012 Nevins Bartolomeo <<EMAIL>>\r\n Copyright (c) 2012 Shane Girish <<EMAIL>>\r\n Copyright (c) 2014 Daniel Wirtz <<EMAIL>>\r\n\r\n Redistribution and use in source and binary forms, with or without\r\n modification, are permitted provided that the following conditions\r\n are met:\r\n 1. Redistributions of source code must retain the above copyright\r\n notice, this list of conditions and the following disclaimer.\r\n 2. Redistributions in binary form must reproduce the above copyright\r\n notice, this list of conditions and the following disclaimer in the\r\n documentation and/or other materials provided with the distribution.\r\n 3. The name of the author may not be used to endorse or promote products\r\n derived from this software without specific prior written permission.\r\n\r\n THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR\r\n IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES\r\n OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\r\n IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT,\r\n INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\r\n NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,\r\n DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY\r\n THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\r\n (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF\r\n THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\r\n */\r\n\r\n/**\r\n * @license bcrypt.js (c) 2013 Daniel Wirtz <<EMAIL>>\r\n * Released under the Apache License, Version 2.0\r\n * see: https://github.com/dcodeIO/bcrypt.js for details\r\n */\r\n(function(global, factory) {\r\n\r\n    /* AMD */ if (true)\r\n        !(__WEBPACK_AMD_DEFINE_ARRAY__ = [], __WEBPACK_AMD_DEFINE_FACTORY__ = (factory),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ = (typeof __WEBPACK_AMD_DEFINE_FACTORY__ === 'function' ?\n\t\t(__WEBPACK_AMD_DEFINE_FACTORY__.apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__)) : __WEBPACK_AMD_DEFINE_FACTORY__),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\r\n    /* CommonJS */ else {}\r\n\r\n}(this, function() {\r\n    \"use strict\";\r\n\r\n    /**\r\n     * bcrypt namespace.\r\n     * @type {Object.<string,*>}\r\n     */\r\n    var bcrypt = {};\r\n\r\n    /**\r\n     * The random implementation to use as a fallback.\r\n     * @type {?function(number):!Array.<number>}\r\n     * @inner\r\n     */\r\n    var randomFallback = null;\r\n\r\n    /**\r\n     * Generates cryptographically secure random bytes.\r\n     * @function\r\n     * @param {number} len Bytes length\r\n     * @returns {!Array.<number>} Random bytes\r\n     * @throws {Error} If no random implementation is available\r\n     * @inner\r\n     */\r\n    function random(len) {\r\n        /* node */ if ( true && module && module['exports'])\r\n            try {\r\n                return (__webpack_require__(/*! crypto */ \"crypto\").randomBytes)(len);\r\n            } catch (e) {}\r\n        /* WCA */ try {\r\n            var a; (self['crypto']||self['msCrypto'])['getRandomValues'](a = new Uint32Array(len));\r\n            return Array.prototype.slice.call(a);\r\n        } catch (e) {}\r\n        /* fallback */ if (!randomFallback)\r\n            throw Error(\"Neither WebCryptoAPI nor a crypto module is available. Use bcrypt.setRandomFallback to set an alternative\");\r\n        return randomFallback(len);\r\n    }\r\n\r\n    // Test if any secure randomness source is available\r\n    var randomAvailable = false;\r\n    try {\r\n        random(1);\r\n        randomAvailable = true;\r\n    } catch (e) {}\r\n\r\n    // Default fallback, if any\r\n    randomFallback = null;\r\n    /**\r\n     * Sets the pseudo random number generator to use as a fallback if neither node's `crypto` module nor the Web Crypto\r\n     *  API is available. Please note: It is highly important that the PRNG used is cryptographically secure and that it\r\n     *  is seeded properly!\r\n     * @param {?function(number):!Array.<number>} random Function taking the number of bytes to generate as its\r\n     *  sole argument, returning the corresponding array of cryptographically secure random byte values.\r\n     * @see http://nodejs.org/api/crypto.html\r\n     * @see http://www.w3.org/TR/WebCryptoAPI/\r\n     */\r\n    bcrypt.setRandomFallback = function(random) {\r\n        randomFallback = random;\r\n    };\r\n\r\n    /**\r\n     * Synchronously generates a salt.\r\n     * @param {number=} rounds Number of rounds to use, defaults to 10 if omitted\r\n     * @param {number=} seed_length Not supported.\r\n     * @returns {string} Resulting salt\r\n     * @throws {Error} If a random fallback is required but not set\r\n     * @expose\r\n     */\r\n    bcrypt.genSaltSync = function(rounds, seed_length) {\r\n        rounds = rounds || GENSALT_DEFAULT_LOG2_ROUNDS;\r\n        if (typeof rounds !== 'number')\r\n            throw Error(\"Illegal arguments: \"+(typeof rounds)+\", \"+(typeof seed_length));\r\n        if (rounds < 4)\r\n            rounds = 4;\r\n        else if (rounds > 31)\r\n            rounds = 31;\r\n        var salt = [];\r\n        salt.push(\"$2a$\");\r\n        if (rounds < 10)\r\n            salt.push(\"0\");\r\n        salt.push(rounds.toString());\r\n        salt.push('$');\r\n        salt.push(base64_encode(random(BCRYPT_SALT_LEN), BCRYPT_SALT_LEN)); // May throw\r\n        return salt.join('');\r\n    };\r\n\r\n    /**\r\n     * Asynchronously generates a salt.\r\n     * @param {(number|function(Error, string=))=} rounds Number of rounds to use, defaults to 10 if omitted\r\n     * @param {(number|function(Error, string=))=} seed_length Not supported.\r\n     * @param {function(Error, string=)=} callback Callback receiving the error, if any, and the resulting salt\r\n     * @returns {!Promise} If `callback` has been omitted\r\n     * @throws {Error} If `callback` is present but not a function\r\n     * @expose\r\n     */\r\n    bcrypt.genSalt = function(rounds, seed_length, callback) {\r\n        if (typeof seed_length === 'function')\r\n            callback = seed_length,\r\n            seed_length = undefined; // Not supported.\r\n        if (typeof rounds === 'function')\r\n            callback = rounds,\r\n            rounds = undefined;\r\n        if (typeof rounds === 'undefined')\r\n            rounds = GENSALT_DEFAULT_LOG2_ROUNDS;\r\n        else if (typeof rounds !== 'number')\r\n            throw Error(\"illegal arguments: \"+(typeof rounds));\r\n\r\n        function _async(callback) {\r\n            nextTick(function() { // Pretty thin, but salting is fast enough\r\n                try {\r\n                    callback(null, bcrypt.genSaltSync(rounds));\r\n                } catch (err) {\r\n                    callback(err);\r\n                }\r\n            });\r\n        }\r\n\r\n        if (callback) {\r\n            if (typeof callback !== 'function')\r\n                throw Error(\"Illegal callback: \"+typeof(callback));\r\n            _async(callback);\r\n        } else\r\n            return new Promise(function(resolve, reject) {\r\n                _async(function(err, res) {\r\n                    if (err) {\r\n                        reject(err);\r\n                        return;\r\n                    }\r\n                    resolve(res);\r\n                });\r\n            });\r\n    };\r\n\r\n    /**\r\n     * Synchronously generates a hash for the given string.\r\n     * @param {string} s String to hash\r\n     * @param {(number|string)=} salt Salt length to generate or salt to use, default to 10\r\n     * @returns {string} Resulting hash\r\n     * @expose\r\n     */\r\n    bcrypt.hashSync = function(s, salt) {\r\n        if (typeof salt === 'undefined')\r\n            salt = GENSALT_DEFAULT_LOG2_ROUNDS;\r\n        if (typeof salt === 'number')\r\n            salt = bcrypt.genSaltSync(salt);\r\n        if (typeof s !== 'string' || typeof salt !== 'string')\r\n            throw Error(\"Illegal arguments: \"+(typeof s)+', '+(typeof salt));\r\n        return _hash(s, salt);\r\n    };\r\n\r\n    /**\r\n     * Asynchronously generates a hash for the given string.\r\n     * @param {string} s String to hash\r\n     * @param {number|string} salt Salt length to generate or salt to use\r\n     * @param {function(Error, string=)=} callback Callback receiving the error, if any, and the resulting hash\r\n     * @param {function(number)=} progressCallback Callback successively called with the percentage of rounds completed\r\n     *  (0.0 - 1.0), maximally once per `MAX_EXECUTION_TIME = 100` ms.\r\n     * @returns {!Promise} If `callback` has been omitted\r\n     * @throws {Error} If `callback` is present but not a function\r\n     * @expose\r\n     */\r\n    bcrypt.hash = function(s, salt, callback, progressCallback) {\r\n\r\n        function _async(callback) {\r\n            if (typeof s === 'string' && typeof salt === 'number')\r\n                bcrypt.genSalt(salt, function(err, salt) {\r\n                    _hash(s, salt, callback, progressCallback);\r\n                });\r\n            else if (typeof s === 'string' && typeof salt === 'string')\r\n                _hash(s, salt, callback, progressCallback);\r\n            else\r\n                nextTick(callback.bind(this, Error(\"Illegal arguments: \"+(typeof s)+', '+(typeof salt))));\r\n        }\r\n\r\n        if (callback) {\r\n            if (typeof callback !== 'function')\r\n                throw Error(\"Illegal callback: \"+typeof(callback));\r\n            _async(callback);\r\n        } else\r\n            return new Promise(function(resolve, reject) {\r\n                _async(function(err, res) {\r\n                    if (err) {\r\n                        reject(err);\r\n                        return;\r\n                    }\r\n                    resolve(res);\r\n                });\r\n            });\r\n    };\r\n\r\n    /**\r\n     * Compares two strings of the same length in constant time.\r\n     * @param {string} known Must be of the correct length\r\n     * @param {string} unknown Must be the same length as `known`\r\n     * @returns {boolean}\r\n     * @inner\r\n     */\r\n    function safeStringCompare(known, unknown) {\r\n        var right = 0,\r\n            wrong = 0;\r\n        for (var i=0, k=known.length; i<k; ++i) {\r\n            if (known.charCodeAt(i) === unknown.charCodeAt(i))\r\n                ++right;\r\n            else\r\n                ++wrong;\r\n        }\r\n        // Prevent removal of unused variables (never true, actually)\r\n        if (right < 0)\r\n            return false;\r\n        return wrong === 0;\r\n    }\r\n\r\n    /**\r\n     * Synchronously tests a string against a hash.\r\n     * @param {string} s String to compare\r\n     * @param {string} hash Hash to test against\r\n     * @returns {boolean} true if matching, otherwise false\r\n     * @throws {Error} If an argument is illegal\r\n     * @expose\r\n     */\r\n    bcrypt.compareSync = function(s, hash) {\r\n        if (typeof s !== \"string\" || typeof hash !== \"string\")\r\n            throw Error(\"Illegal arguments: \"+(typeof s)+', '+(typeof hash));\r\n        if (hash.length !== 60)\r\n            return false;\r\n        return safeStringCompare(bcrypt.hashSync(s, hash.substr(0, hash.length-31)), hash);\r\n    };\r\n\r\n    /**\r\n     * Asynchronously compares the given data against the given hash.\r\n     * @param {string} s Data to compare\r\n     * @param {string} hash Data to be compared to\r\n     * @param {function(Error, boolean)=} callback Callback receiving the error, if any, otherwise the result\r\n     * @param {function(number)=} progressCallback Callback successively called with the percentage of rounds completed\r\n     *  (0.0 - 1.0), maximally once per `MAX_EXECUTION_TIME = 100` ms.\r\n     * @returns {!Promise} If `callback` has been omitted\r\n     * @throws {Error} If `callback` is present but not a function\r\n     * @expose\r\n     */\r\n    bcrypt.compare = function(s, hash, callback, progressCallback) {\r\n\r\n        function _async(callback) {\r\n            if (typeof s !== \"string\" || typeof hash !== \"string\") {\r\n                nextTick(callback.bind(this, Error(\"Illegal arguments: \"+(typeof s)+', '+(typeof hash))));\r\n                return;\r\n            }\r\n            if (hash.length !== 60) {\r\n                nextTick(callback.bind(this, null, false));\r\n                return;\r\n            }\r\n            bcrypt.hash(s, hash.substr(0, 29), function(err, comp) {\r\n                if (err)\r\n                    callback(err);\r\n                else\r\n                    callback(null, safeStringCompare(comp, hash));\r\n            }, progressCallback);\r\n        }\r\n\r\n        if (callback) {\r\n            if (typeof callback !== 'function')\r\n                throw Error(\"Illegal callback: \"+typeof(callback));\r\n            _async(callback);\r\n        } else\r\n            return new Promise(function(resolve, reject) {\r\n                _async(function(err, res) {\r\n                    if (err) {\r\n                        reject(err);\r\n                        return;\r\n                    }\r\n                    resolve(res);\r\n                });\r\n            });\r\n    };\r\n\r\n    /**\r\n     * Gets the number of rounds used to encrypt the specified hash.\r\n     * @param {string} hash Hash to extract the used number of rounds from\r\n     * @returns {number} Number of rounds used\r\n     * @throws {Error} If `hash` is not a string\r\n     * @expose\r\n     */\r\n    bcrypt.getRounds = function(hash) {\r\n        if (typeof hash !== \"string\")\r\n            throw Error(\"Illegal arguments: \"+(typeof hash));\r\n        return parseInt(hash.split(\"$\")[2], 10);\r\n    };\r\n\r\n    /**\r\n     * Gets the salt portion from a hash. Does not validate the hash.\r\n     * @param {string} hash Hash to extract the salt from\r\n     * @returns {string} Extracted salt part\r\n     * @throws {Error} If `hash` is not a string or otherwise invalid\r\n     * @expose\r\n     */\r\n    bcrypt.getSalt = function(hash) {\r\n        if (typeof hash !== 'string')\r\n            throw Error(\"Illegal arguments: \"+(typeof hash));\r\n        if (hash.length !== 60)\r\n            throw Error(\"Illegal hash length: \"+hash.length+\" != 60\");\r\n        return hash.substring(0, 29);\r\n    };\r\n\r\n    /**\r\n     * Continues with the callback on the next tick.\r\n     * @function\r\n     * @param {function(...[*])} callback Callback to execute\r\n     * @inner\r\n     */\r\n    var nextTick = typeof process !== 'undefined' && process && typeof process.nextTick === 'function'\r\n        ? (typeof setImmediate === 'function' ? setImmediate : process.nextTick)\r\n        : setTimeout;\r\n\r\n    /**\r\n     * Converts a JavaScript string to UTF8 bytes.\r\n     * @param {string} str String\r\n     * @returns {!Array.<number>} UTF8 bytes\r\n     * @inner\r\n     */\r\n    function stringToBytes(str) {\r\n        var out = [],\r\n            i = 0;\r\n        utfx.encodeUTF16toUTF8(function() {\r\n            if (i >= str.length) return null;\r\n            return str.charCodeAt(i++);\r\n        }, function(b) {\r\n            out.push(b);\r\n        });\r\n        return out;\r\n    }\r\n\r\n    // A base64 implementation for the bcrypt algorithm. This is partly non-standard.\r\n\r\n    /**\r\n     * bcrypt's own non-standard base64 dictionary.\r\n     * @type {!Array.<string>}\r\n     * @const\r\n     * @inner\r\n     **/\r\n    var BASE64_CODE = \"./ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\".split('');\r\n\r\n    /**\r\n     * @type {!Array.<number>}\r\n     * @const\r\n     * @inner\r\n     **/\r\n    var BASE64_INDEX = [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\r\n        -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\r\n        -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0,\r\n        1, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, -1, -1, -1, -1, -1, -1,\r\n        -1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19,\r\n        20, 21, 22, 23, 24, 25, 26, 27, -1, -1, -1, -1, -1, -1, 28, 29, 30,\r\n        31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47,\r\n        48, 49, 50, 51, 52, 53, -1, -1, -1, -1, -1];\r\n\r\n    /**\r\n     * @type {!function(...number):string}\r\n     * @inner\r\n     */\r\n    var stringFromCharCode = String.fromCharCode;\r\n\r\n    /**\r\n     * Encodes a byte array to base64 with up to len bytes of input.\r\n     * @param {!Array.<number>} b Byte array\r\n     * @param {number} len Maximum input length\r\n     * @returns {string}\r\n     * @inner\r\n     */\r\n    function base64_encode(b, len) {\r\n        var off = 0,\r\n            rs = [],\r\n            c1, c2;\r\n        if (len <= 0 || len > b.length)\r\n            throw Error(\"Illegal len: \"+len);\r\n        while (off < len) {\r\n            c1 = b[off++] & 0xff;\r\n            rs.push(BASE64_CODE[(c1 >> 2) & 0x3f]);\r\n            c1 = (c1 & 0x03) << 4;\r\n            if (off >= len) {\r\n                rs.push(BASE64_CODE[c1 & 0x3f]);\r\n                break;\r\n            }\r\n            c2 = b[off++] & 0xff;\r\n            c1 |= (c2 >> 4) & 0x0f;\r\n            rs.push(BASE64_CODE[c1 & 0x3f]);\r\n            c1 = (c2 & 0x0f) << 2;\r\n            if (off >= len) {\r\n                rs.push(BASE64_CODE[c1 & 0x3f]);\r\n                break;\r\n            }\r\n            c2 = b[off++] & 0xff;\r\n            c1 |= (c2 >> 6) & 0x03;\r\n            rs.push(BASE64_CODE[c1 & 0x3f]);\r\n            rs.push(BASE64_CODE[c2 & 0x3f]);\r\n        }\r\n        return rs.join('');\r\n    }\r\n\r\n    /**\r\n     * Decodes a base64 encoded string to up to len bytes of output.\r\n     * @param {string} s String to decode\r\n     * @param {number} len Maximum output length\r\n     * @returns {!Array.<number>}\r\n     * @inner\r\n     */\r\n    function base64_decode(s, len) {\r\n        var off = 0,\r\n            slen = s.length,\r\n            olen = 0,\r\n            rs = [],\r\n            c1, c2, c3, c4, o, code;\r\n        if (len <= 0)\r\n            throw Error(\"Illegal len: \"+len);\r\n        while (off < slen - 1 && olen < len) {\r\n            code = s.charCodeAt(off++);\r\n            c1 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\r\n            code = s.charCodeAt(off++);\r\n            c2 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\r\n            if (c1 == -1 || c2 == -1)\r\n                break;\r\n            o = (c1 << 2) >>> 0;\r\n            o |= (c2 & 0x30) >> 4;\r\n            rs.push(stringFromCharCode(o));\r\n            if (++olen >= len || off >= slen)\r\n                break;\r\n            code = s.charCodeAt(off++);\r\n            c3 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\r\n            if (c3 == -1)\r\n                break;\r\n            o = ((c2 & 0x0f) << 4) >>> 0;\r\n            o |= (c3 & 0x3c) >> 2;\r\n            rs.push(stringFromCharCode(o));\r\n            if (++olen >= len || off >= slen)\r\n                break;\r\n            code = s.charCodeAt(off++);\r\n            c4 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\r\n            o = ((c3 & 0x03) << 6) >>> 0;\r\n            o |= c4;\r\n            rs.push(stringFromCharCode(o));\r\n            ++olen;\r\n        }\r\n        var res = [];\r\n        for (off = 0; off<olen; off++)\r\n            res.push(rs[off].charCodeAt(0));\r\n        return res;\r\n    }\r\n\r\n    /**\r\n     * utfx-embeddable (c) 2014 Daniel Wirtz <<EMAIL>>\r\n     * Released under the Apache License, Version 2.0\r\n     * see: https://github.com/dcodeIO/utfx for details\r\n     */\r\n    var utfx = function() {\r\n        \"use strict\";\r\n\r\n        /**\r\n         * utfx namespace.\r\n         * @inner\r\n         * @type {!Object.<string,*>}\r\n         */\r\n        var utfx = {};\r\n\r\n        /**\r\n         * Maximum valid code point.\r\n         * @type {number}\r\n         * @const\r\n         */\r\n        utfx.MAX_CODEPOINT = 0x10FFFF;\r\n\r\n        /**\r\n         * Encodes UTF8 code points to UTF8 bytes.\r\n         * @param {(!function():number|null) | number} src Code points source, either as a function returning the next code point\r\n         *  respectively `null` if there are no more code points left or a single numeric code point.\r\n         * @param {!function(number)} dst Bytes destination as a function successively called with the next byte\r\n         */\r\n        utfx.encodeUTF8 = function(src, dst) {\r\n            var cp = null;\r\n            if (typeof src === 'number')\r\n                cp = src,\r\n                src = function() { return null; };\r\n            while (cp !== null || (cp = src()) !== null) {\r\n                if (cp < 0x80)\r\n                    dst(cp&0x7F);\r\n                else if (cp < 0x800)\r\n                    dst(((cp>>6)&0x1F)|0xC0),\r\n                    dst((cp&0x3F)|0x80);\r\n                else if (cp < 0x10000)\r\n                    dst(((cp>>12)&0x0F)|0xE0),\r\n                    dst(((cp>>6)&0x3F)|0x80),\r\n                    dst((cp&0x3F)|0x80);\r\n                else\r\n                    dst(((cp>>18)&0x07)|0xF0),\r\n                    dst(((cp>>12)&0x3F)|0x80),\r\n                    dst(((cp>>6)&0x3F)|0x80),\r\n                    dst((cp&0x3F)|0x80);\r\n                cp = null;\r\n            }\r\n        };\r\n\r\n        /**\r\n         * Decodes UTF8 bytes to UTF8 code points.\r\n         * @param {!function():number|null} src Bytes source as a function returning the next byte respectively `null` if there\r\n         *  are no more bytes left.\r\n         * @param {!function(number)} dst Code points destination as a function successively called with each decoded code point.\r\n         * @throws {RangeError} If a starting byte is invalid in UTF8\r\n         * @throws {Error} If the last sequence is truncated. Has an array property `bytes` holding the\r\n         *  remaining bytes.\r\n         */\r\n        utfx.decodeUTF8 = function(src, dst) {\r\n            var a, b, c, d, fail = function(b) {\r\n                b = b.slice(0, b.indexOf(null));\r\n                var err = Error(b.toString());\r\n                err.name = \"TruncatedError\";\r\n                err['bytes'] = b;\r\n                throw err;\r\n            };\r\n            while ((a = src()) !== null) {\r\n                if ((a&0x80) === 0)\r\n                    dst(a);\r\n                else if ((a&0xE0) === 0xC0)\r\n                    ((b = src()) === null) && fail([a, b]),\r\n                    dst(((a&0x1F)<<6) | (b&0x3F));\r\n                else if ((a&0xF0) === 0xE0)\r\n                    ((b=src()) === null || (c=src()) === null) && fail([a, b, c]),\r\n                    dst(((a&0x0F)<<12) | ((b&0x3F)<<6) | (c&0x3F));\r\n                else if ((a&0xF8) === 0xF0)\r\n                    ((b=src()) === null || (c=src()) === null || (d=src()) === null) && fail([a, b, c ,d]),\r\n                    dst(((a&0x07)<<18) | ((b&0x3F)<<12) | ((c&0x3F)<<6) | (d&0x3F));\r\n                else throw RangeError(\"Illegal starting byte: \"+a);\r\n            }\r\n        };\r\n\r\n        /**\r\n         * Converts UTF16 characters to UTF8 code points.\r\n         * @param {!function():number|null} src Characters source as a function returning the next char code respectively\r\n         *  `null` if there are no more characters left.\r\n         * @param {!function(number)} dst Code points destination as a function successively called with each converted code\r\n         *  point.\r\n         */\r\n        utfx.UTF16toUTF8 = function(src, dst) {\r\n            var c1, c2 = null;\r\n            while (true) {\r\n                if ((c1 = c2 !== null ? c2 : src()) === null)\r\n                    break;\r\n                if (c1 >= 0xD800 && c1 <= 0xDFFF) {\r\n                    if ((c2 = src()) !== null) {\r\n                        if (c2 >= 0xDC00 && c2 <= 0xDFFF) {\r\n                            dst((c1-0xD800)*0x400+c2-0xDC00+0x10000);\r\n                            c2 = null; continue;\r\n                        }\r\n                    }\r\n                }\r\n                dst(c1);\r\n            }\r\n            if (c2 !== null) dst(c2);\r\n        };\r\n\r\n        /**\r\n         * Converts UTF8 code points to UTF16 characters.\r\n         * @param {(!function():number|null) | number} src Code points source, either as a function returning the next code point\r\n         *  respectively `null` if there are no more code points left or a single numeric code point.\r\n         * @param {!function(number)} dst Characters destination as a function successively called with each converted char code.\r\n         * @throws {RangeError} If a code point is out of range\r\n         */\r\n        utfx.UTF8toUTF16 = function(src, dst) {\r\n            var cp = null;\r\n            if (typeof src === 'number')\r\n                cp = src, src = function() { return null; };\r\n            while (cp !== null || (cp = src()) !== null) {\r\n                if (cp <= 0xFFFF)\r\n                    dst(cp);\r\n                else\r\n                    cp -= 0x10000,\r\n                    dst((cp>>10)+0xD800),\r\n                    dst((cp%0x400)+0xDC00);\r\n                cp = null;\r\n            }\r\n        };\r\n\r\n        /**\r\n         * Converts and encodes UTF16 characters to UTF8 bytes.\r\n         * @param {!function():number|null} src Characters source as a function returning the next char code respectively `null`\r\n         *  if there are no more characters left.\r\n         * @param {!function(number)} dst Bytes destination as a function successively called with the next byte.\r\n         */\r\n        utfx.encodeUTF16toUTF8 = function(src, dst) {\r\n            utfx.UTF16toUTF8(src, function(cp) {\r\n                utfx.encodeUTF8(cp, dst);\r\n            });\r\n        };\r\n\r\n        /**\r\n         * Decodes and converts UTF8 bytes to UTF16 characters.\r\n         * @param {!function():number|null} src Bytes source as a function returning the next byte respectively `null` if there\r\n         *  are no more bytes left.\r\n         * @param {!function(number)} dst Characters destination as a function successively called with each converted char code.\r\n         * @throws {RangeError} If a starting byte is invalid in UTF8\r\n         * @throws {Error} If the last sequence is truncated. Has an array property `bytes` holding the remaining bytes.\r\n         */\r\n        utfx.decodeUTF8toUTF16 = function(src, dst) {\r\n            utfx.decodeUTF8(src, function(cp) {\r\n                utfx.UTF8toUTF16(cp, dst);\r\n            });\r\n        };\r\n\r\n        /**\r\n         * Calculates the byte length of an UTF8 code point.\r\n         * @param {number} cp UTF8 code point\r\n         * @returns {number} Byte length\r\n         */\r\n        utfx.calculateCodePoint = function(cp) {\r\n            return (cp < 0x80) ? 1 : (cp < 0x800) ? 2 : (cp < 0x10000) ? 3 : 4;\r\n        };\r\n\r\n        /**\r\n         * Calculates the number of UTF8 bytes required to store UTF8 code points.\r\n         * @param {(!function():number|null)} src Code points source as a function returning the next code point respectively\r\n         *  `null` if there are no more code points left.\r\n         * @returns {number} The number of UTF8 bytes required\r\n         */\r\n        utfx.calculateUTF8 = function(src) {\r\n            var cp, l=0;\r\n            while ((cp = src()) !== null)\r\n                l += utfx.calculateCodePoint(cp);\r\n            return l;\r\n        };\r\n\r\n        /**\r\n         * Calculates the number of UTF8 code points respectively UTF8 bytes required to store UTF16 char codes.\r\n         * @param {(!function():number|null)} src Characters source as a function returning the next char code respectively\r\n         *  `null` if there are no more characters left.\r\n         * @returns {!Array.<number>} The number of UTF8 code points at index 0 and the number of UTF8 bytes required at index 1.\r\n         */\r\n        utfx.calculateUTF16asUTF8 = function(src) {\r\n            var n=0, l=0;\r\n            utfx.UTF16toUTF8(src, function(cp) {\r\n                ++n; l += utfx.calculateCodePoint(cp);\r\n            });\r\n            return [n,l];\r\n        };\r\n\r\n        return utfx;\r\n    }();\r\n\r\n    Date.now = Date.now || function() { return +new Date; };\r\n\r\n    /**\r\n     * @type {number}\r\n     * @const\r\n     * @inner\r\n     */\r\n    var BCRYPT_SALT_LEN = 16;\r\n\r\n    /**\r\n     * @type {number}\r\n     * @const\r\n     * @inner\r\n     */\r\n    var GENSALT_DEFAULT_LOG2_ROUNDS = 10;\r\n\r\n    /**\r\n     * @type {number}\r\n     * @const\r\n     * @inner\r\n     */\r\n    var BLOWFISH_NUM_ROUNDS = 16;\r\n\r\n    /**\r\n     * @type {number}\r\n     * @const\r\n     * @inner\r\n     */\r\n    var MAX_EXECUTION_TIME = 100;\r\n\r\n    /**\r\n     * @type {Array.<number>}\r\n     * @const\r\n     * @inner\r\n     */\r\n    var P_ORIG = [\r\n        0x243f6a88, 0x85a308d3, 0x13198a2e, 0x03707344, 0xa4093822,\r\n        0x299f31d0, 0x082efa98, 0xec4e6c89, 0x452821e6, 0x38d01377,\r\n        0xbe5466cf, 0x34e90c6c, 0xc0ac29b7, 0xc97c50dd, 0x3f84d5b5,\r\n        0xb5470917, 0x9216d5d9, 0x8979fb1b\r\n    ];\r\n\r\n    /**\r\n     * @type {Array.<number>}\r\n     * @const\r\n     * @inner\r\n     */\r\n    var S_ORIG = [\r\n        0xd1310ba6, 0x98dfb5ac, 0x2ffd72db, 0xd01adfb7, 0xb8e1afed,\r\n        0x6a267e96, 0xba7c9045, 0xf12c7f99, 0x24a19947, 0xb3916cf7,\r\n        0x0801f2e2, 0x858efc16, 0x636920d8, 0x71574e69, 0xa458fea3,\r\n        0xf4933d7e, 0x0d95748f, 0x728eb658, 0x718bcd58, 0x82154aee,\r\n        0x7b54a41d, 0xc25a59b5, 0x9c30d539, 0x2af26013, 0xc5d1b023,\r\n        0x286085f0, 0xca417918, 0xb8db38ef, 0x8e79dcb0, 0x603a180e,\r\n        0x6c9e0e8b, 0xb01e8a3e, 0xd71577c1, 0xbd314b27, 0x78af2fda,\r\n        0x55605c60, 0xe65525f3, 0xaa55ab94, 0x57489862, 0x63e81440,\r\n        0x55ca396a, 0x2aab10b6, 0xb4cc5c34, 0x1141e8ce, 0xa15486af,\r\n        0x7c72e993, 0xb3ee1411, 0x636fbc2a, 0x2ba9c55d, 0x741831f6,\r\n        0xce5c3e16, 0x9b87931e, 0xafd6ba33, 0x6c24cf5c, 0x7a325381,\r\n        0x28958677, 0x3b8f4898, 0x6b4bb9af, 0xc4bfe81b, 0x66282193,\r\n        0x61d809cc, 0xfb21a991, 0x487cac60, 0x5dec8032, 0xef845d5d,\r\n        0xe98575b1, 0xdc262302, 0xeb651b88, 0x23893e81, 0xd396acc5,\r\n        0x0f6d6ff3, 0x83f44239, 0x2e0b4482, 0xa4842004, 0x69c8f04a,\r\n        0x9e1f9b5e, 0x21c66842, 0xf6e96c9a, 0x670c9c61, 0xabd388f0,\r\n        0x6a51a0d2, 0xd8542f68, 0x960fa728, 0xab5133a3, 0x6eef0b6c,\r\n        0x137a3be4, 0xba3bf050, 0x7efb2a98, 0xa1f1651d, 0x39af0176,\r\n        0x66ca593e, 0x82430e88, 0x8cee8619, 0x456f9fb4, 0x7d84a5c3,\r\n        0x3b8b5ebe, 0xe06f75d8, 0x85c12073, 0x401a449f, 0x56c16aa6,\r\n        0x4ed3aa62, 0x363f7706, 0x1bfedf72, 0x429b023d, 0x37d0d724,\r\n        0xd00a1248, 0xdb0fead3, 0x49f1c09b, 0x075372c9, 0x80991b7b,\r\n        0x25d479d8, 0xf6e8def7, 0xe3fe501a, 0xb6794c3b, 0x976ce0bd,\r\n        0x04c006ba, 0xc1a94fb6, 0x409f60c4, 0x5e5c9ec2, 0x196a2463,\r\n        0x68fb6faf, 0x3e6c53b5, 0x1339b2eb, 0x3b52ec6f, 0x6dfc511f,\r\n        0x9b30952c, 0xcc814544, 0xaf5ebd09, 0xbee3d004, 0xde334afd,\r\n        0x660f2807, 0x192e4bb3, 0xc0cba857, 0x45c8740f, 0xd20b5f39,\r\n        0xb9d3fbdb, 0x5579c0bd, 0x1a60320a, 0xd6a100c6, 0x402c7279,\r\n        0x679f25fe, 0xfb1fa3cc, 0x8ea5e9f8, 0xdb3222f8, 0x3c7516df,\r\n        0xfd616b15, 0x2f501ec8, 0xad0552ab, 0x323db5fa, 0xfd238760,\r\n        0x53317b48, 0x3e00df82, 0x9e5c57bb, 0xca6f8ca0, 0x1a87562e,\r\n        0xdf1769db, 0xd542a8f6, 0x287effc3, 0xac6732c6, 0x8c4f5573,\r\n        0x695b27b0, 0xbbca58c8, 0xe1ffa35d, 0xb8f011a0, 0x10fa3d98,\r\n        0xfd2183b8, 0x4afcb56c, 0x2dd1d35b, 0x9a53e479, 0xb6f84565,\r\n        0xd28e49bc, 0x4bfb9790, 0xe1ddf2da, 0xa4cb7e33, 0x62fb1341,\r\n        0xcee4c6e8, 0xef20cada, 0x36774c01, 0xd07e9efe, 0x2bf11fb4,\r\n        0x95dbda4d, 0xae909198, 0xeaad8e71, 0x6b93d5a0, 0xd08ed1d0,\r\n        0xafc725e0, 0x8e3c5b2f, 0x8e7594b7, 0x8ff6e2fb, 0xf2122b64,\r\n        0x8888b812, 0x900df01c, 0x4fad5ea0, 0x688fc31c, 0xd1cff191,\r\n        0xb3a8c1ad, 0x2f2f2218, 0xbe0e1777, 0xea752dfe, 0x8b021fa1,\r\n        0xe5a0cc0f, 0xb56f74e8, 0x18acf3d6, 0xce89e299, 0xb4a84fe0,\r\n        0xfd13e0b7, 0x7cc43b81, 0xd2ada8d9, 0x165fa266, 0x80957705,\r\n        0x93cc7314, 0x211a1477, 0xe6ad2065, 0x77b5fa86, 0xc75442f5,\r\n        0xfb9d35cf, 0xebcdaf0c, 0x7b3e89a0, 0xd6411bd3, 0xae1e7e49,\r\n        0x00250e2d, 0x2071b35e, 0x226800bb, 0x57b8e0af, 0x2464369b,\r\n        0xf009b91e, 0x5563911d, 0x59dfa6aa, 0x78c14389, 0xd95a537f,\r\n        0x207d5ba2, 0x02e5b9c5, 0x83260376, 0x6295cfa9, 0x11c81968,\r\n        0x4e734a41, 0xb3472dca, 0x7b14a94a, 0x1b510052, 0x9a532915,\r\n        0xd60f573f, 0xbc9bc6e4, 0x2b60a476, 0x81e67400, 0x08ba6fb5,\r\n        0x571be91f, 0xf296ec6b, 0x2a0dd915, 0xb6636521, 0xe7b9f9b6,\r\n        0xff34052e, 0xc5855664, 0x53b02d5d, 0xa99f8fa1, 0x08ba4799,\r\n        0x6e85076a, 0x4b7a70e9, 0xb5b32944, 0xdb75092e, 0xc4192623,\r\n        0xad6ea6b0, 0x49a7df7d, 0x9cee60b8, 0x8fedb266, 0xecaa8c71,\r\n        0x699a17ff, 0x5664526c, 0xc2b19ee1, 0x193602a5, 0x75094c29,\r\n        0xa0591340, 0xe4183a3e, 0x3f54989a, 0x5b429d65, 0x6b8fe4d6,\r\n        0x99f73fd6, 0xa1d29c07, 0xefe830f5, 0x4d2d38e6, 0xf0255dc1,\r\n        0x4cdd2086, 0x8470eb26, 0x6382e9c6, 0x021ecc5e, 0x09686b3f,\r\n        0x3ebaefc9, 0x3c971814, 0x6b6a70a1, 0x687f3584, 0x52a0e286,\r\n        0xb79c5305, 0xaa500737, 0x3e07841c, 0x7fdeae5c, 0x8e7d44ec,\r\n        0x5716f2b8, 0xb03ada37, 0xf0500c0d, 0xf01c1f04, 0x0200b3ff,\r\n        0xae0cf51a, 0x3cb574b2, 0x25837a58, 0xdc0921bd, 0xd19113f9,\r\n        0x7ca92ff6, 0x94324773, 0x22f54701, 0x3ae5e581, 0x37c2dadc,\r\n        0xc8b57634, 0x9af3dda7, 0xa9446146, 0x0fd0030e, 0xecc8c73e,\r\n        0xa4751e41, 0xe238cd99, 0x3bea0e2f, 0x3280bba1, 0x183eb331,\r\n        0x4e548b38, 0x4f6db908, 0x6f420d03, 0xf60a04bf, 0x2cb81290,\r\n        0x24977c79, 0x5679b072, 0xbcaf89af, 0xde9a771f, 0xd9930810,\r\n        0xb38bae12, 0xdccf3f2e, 0x5512721f, 0x2e6b7124, 0x501adde6,\r\n        0x9f84cd87, 0x7a584718, 0x7408da17, 0xbc9f9abc, 0xe94b7d8c,\r\n        0xec7aec3a, 0xdb851dfa, 0x63094366, 0xc464c3d2, 0xef1c1847,\r\n        0x3215d908, 0xdd433b37, 0x24c2ba16, 0x12a14d43, 0x2a65c451,\r\n        0x50940002, 0x133ae4dd, 0x71dff89e, 0x10314e55, 0x81ac77d6,\r\n        0x5f11199b, 0x043556f1, 0xd7a3c76b, 0x3c11183b, 0x5924a509,\r\n        0xf28fe6ed, 0x97f1fbfa, 0x9ebabf2c, 0x1e153c6e, 0x86e34570,\r\n        0xeae96fb1, 0x860e5e0a, 0x5a3e2ab3, 0x771fe71c, 0x4e3d06fa,\r\n        0x2965dcb9, 0x99e71d0f, 0x803e89d6, 0x5266c825, 0x2e4cc978,\r\n        0x9c10b36a, 0xc6150eba, 0x94e2ea78, 0xa5fc3c53, 0x1e0a2df4,\r\n        0xf2f74ea7, 0x361d2b3d, 0x1939260f, 0x19c27960, 0x5223a708,\r\n        0xf71312b6, 0xebadfe6e, 0xeac31f66, 0xe3bc4595, 0xa67bc883,\r\n        0xb17f37d1, 0x018cff28, 0xc332ddef, 0xbe6c5aa5, 0x65582185,\r\n        0x68ab9802, 0xeecea50f, 0xdb2f953b, 0x2aef7dad, 0x5b6e2f84,\r\n        0x1521b628, 0x29076170, 0xecdd4775, 0x619f1510, 0x13cca830,\r\n        0xeb61bd96, 0x0334fe1e, 0xaa0363cf, 0xb5735c90, 0x4c70a239,\r\n        0xd59e9e0b, 0xcbaade14, 0xeecc86bc, 0x60622ca7, 0x9cab5cab,\r\n        0xb2f3846e, 0x648b1eaf, 0x19bdf0ca, 0xa02369b9, 0x655abb50,\r\n        0x40685a32, 0x3c2ab4b3, 0x319ee9d5, 0xc021b8f7, 0x9b540b19,\r\n        0x875fa099, 0x95f7997e, 0x623d7da8, 0xf837889a, 0x97e32d77,\r\n        0x11ed935f, 0x16681281, 0x0e358829, 0xc7e61fd6, 0x96dedfa1,\r\n        0x7858ba99, 0x57f584a5, 0x1b227263, 0x9b83c3ff, 0x1ac24696,\r\n        0xcdb30aeb, 0x532e3054, 0x8fd948e4, 0x6dbc3128, 0x58ebf2ef,\r\n        0x34c6ffea, 0xfe28ed61, 0xee7c3c73, 0x5d4a14d9, 0xe864b7e3,\r\n        0x42105d14, 0x203e13e0, 0x45eee2b6, 0xa3aaabea, 0xdb6c4f15,\r\n        0xfacb4fd0, 0xc742f442, 0xef6abbb5, 0x654f3b1d, 0x41cd2105,\r\n        0xd81e799e, 0x86854dc7, 0xe44b476a, 0x3d816250, 0xcf62a1f2,\r\n        0x5b8d2646, 0xfc8883a0, 0xc1c7b6a3, 0x7f1524c3, 0x69cb7492,\r\n        0x47848a0b, 0x5692b285, 0x095bbf00, 0xad19489d, 0x1462b174,\r\n        0x23820e00, 0x58428d2a, 0x0c55f5ea, 0x1dadf43e, 0x233f7061,\r\n        0x3372f092, 0x8d937e41, 0xd65fecf1, 0x6c223bdb, 0x7cde3759,\r\n        0xcbee7460, 0x4085f2a7, 0xce77326e, 0xa6078084, 0x19f8509e,\r\n        0xe8efd855, 0x61d99735, 0xa969a7aa, 0xc50c06c2, 0x5a04abfc,\r\n        0x800bcadc, 0x9e447a2e, 0xc3453484, 0xfdd56705, 0x0e1e9ec9,\r\n        0xdb73dbd3, 0x105588cd, 0x675fda79, 0xe3674340, 0xc5c43465,\r\n        0x713e38d8, 0x3d28f89e, 0xf16dff20, 0x153e21e7, 0x8fb03d4a,\r\n        0xe6e39f2b, 0xdb83adf7, 0xe93d5a68, 0x948140f7, 0xf64c261c,\r\n        0x94692934, 0x411520f7, 0x7602d4f7, 0xbcf46b2e, 0xd4a20068,\r\n        0xd4082471, 0x3320f46a, 0x43b7d4b7, 0x500061af, 0x1e39f62e,\r\n        0x97244546, 0x14214f74, 0xbf8b8840, 0x4d95fc1d, 0x96b591af,\r\n        0x70f4ddd3, 0x66a02f45, 0xbfbc09ec, 0x03bd9785, 0x7fac6dd0,\r\n        0x31cb8504, 0x96eb27b3, 0x55fd3941, 0xda2547e6, 0xabca0a9a,\r\n        0x28507825, 0x530429f4, 0x0a2c86da, 0xe9b66dfb, 0x68dc1462,\r\n        0xd7486900, 0x680ec0a4, 0x27a18dee, 0x4f3ffea2, 0xe887ad8c,\r\n        0xb58ce006, 0x7af4d6b6, 0xaace1e7c, 0xd3375fec, 0xce78a399,\r\n        0x406b2a42, 0x20fe9e35, 0xd9f385b9, 0xee39d7ab, 0x3b124e8b,\r\n        0x1dc9faf7, 0x4b6d1856, 0x26a36631, 0xeae397b2, 0x3a6efa74,\r\n        0xdd5b4332, 0x6841e7f7, 0xca7820fb, 0xfb0af54e, 0xd8feb397,\r\n        0x454056ac, 0xba489527, 0x55533a3a, 0x20838d87, 0xfe6ba9b7,\r\n        0xd096954b, 0x55a867bc, 0xa1159a58, 0xcca92963, 0x99e1db33,\r\n        0xa62a4a56, 0x3f3125f9, 0x5ef47e1c, 0x9029317c, 0xfdf8e802,\r\n        0x04272f70, 0x80bb155c, 0x05282ce3, 0x95c11548, 0xe4c66d22,\r\n        0x48c1133f, 0xc70f86dc, 0x07f9c9ee, 0x41041f0f, 0x404779a4,\r\n        0x5d886e17, 0x325f51eb, 0xd59bc0d1, 0xf2bcc18f, 0x41113564,\r\n        0x257b7834, 0x602a9c60, 0xdff8e8a3, 0x1f636c1b, 0x0e12b4c2,\r\n        0x02e1329e, 0xaf664fd1, 0xcad18115, 0x6b2395e0, 0x333e92e1,\r\n        0x3b240b62, 0xeebeb922, 0x85b2a20e, 0xe6ba0d99, 0xde720c8c,\r\n        0x2da2f728, 0xd0127845, 0x95b794fd, 0x647d0862, 0xe7ccf5f0,\r\n        0x5449a36f, 0x877d48fa, 0xc39dfd27, 0xf33e8d1e, 0x0a476341,\r\n        0x992eff74, 0x3a6f6eab, 0xf4f8fd37, 0xa812dc60, 0xa1ebddf8,\r\n        0x991be14c, 0xdb6e6b0d, 0xc67b5510, 0x6d672c37, 0x2765d43b,\r\n        0xdcd0e804, 0xf1290dc7, 0xcc00ffa3, 0xb5390f92, 0x690fed0b,\r\n        0x667b9ffb, 0xcedb7d9c, 0xa091cf0b, 0xd9155ea3, 0xbb132f88,\r\n        0x515bad24, 0x7b9479bf, 0x763bd6eb, 0x37392eb3, 0xcc115979,\r\n        0x8026e297, 0xf42e312d, 0x6842ada7, 0xc66a2b3b, 0x12754ccc,\r\n        0x782ef11c, 0x6a124237, 0xb79251e7, 0x06a1bbe6, 0x4bfb6350,\r\n        0x1a6b1018, 0x11caedfa, 0x3d25bdd8, 0xe2e1c3c9, 0x44421659,\r\n        0x0a121386, 0xd90cec6e, 0xd5abea2a, 0x64af674e, 0xda86a85f,\r\n        0xbebfe988, 0x64e4c3fe, 0x9dbc8057, 0xf0f7c086, 0x60787bf8,\r\n        0x6003604d, 0xd1fd8346, 0xf6381fb0, 0x7745ae04, 0xd736fccc,\r\n        0x83426b33, 0xf01eab71, 0xb0804187, 0x3c005e5f, 0x77a057be,\r\n        0xbde8ae24, 0x55464299, 0xbf582e61, 0x4e58f48f, 0xf2ddfda2,\r\n        0xf474ef38, 0x8789bdc2, 0x5366f9c3, 0xc8b38e74, 0xb475f255,\r\n        0x46fcd9b9, 0x7aeb2661, 0x8b1ddf84, 0x846a0e79, 0x915f95e2,\r\n        0x466e598e, 0x20b45770, 0x8cd55591, 0xc902de4c, 0xb90bace1,\r\n        0xbb8205d0, 0x11a86248, 0x7574a99e, 0xb77f19b6, 0xe0a9dc09,\r\n        0x662d09a1, 0xc4324633, 0xe85a1f02, 0x09f0be8c, 0x4a99a025,\r\n        0x1d6efe10, 0x1ab93d1d, 0x0ba5a4df, 0xa186f20f, 0x2868f169,\r\n        0xdcb7da83, 0x573906fe, 0xa1e2ce9b, 0x4fcd7f52, 0x50115e01,\r\n        0xa70683fa, 0xa002b5c4, 0x0de6d027, 0x9af88c27, 0x773f8641,\r\n        0xc3604c06, 0x61a806b5, 0xf0177a28, 0xc0f586e0, 0x006058aa,\r\n        0x30dc7d62, 0x11e69ed7, 0x2338ea63, 0x53c2dd94, 0xc2c21634,\r\n        0xbbcbee56, 0x90bcb6de, 0xebfc7da1, 0xce591d76, 0x6f05e409,\r\n        0x4b7c0188, 0x39720a3d, 0x7c927c24, 0x86e3725f, 0x724d9db9,\r\n        0x1ac15bb4, 0xd39eb8fc, 0xed545578, 0x08fca5b5, 0xd83d7cd3,\r\n        0x4dad0fc4, 0x1e50ef5e, 0xb161e6f8, 0xa28514d9, 0x6c51133c,\r\n        0x6fd5c7e7, 0x56e14ec4, 0x362abfce, 0xddc6c837, 0xd79a3234,\r\n        0x92638212, 0x670efa8e, 0x406000e0, 0x3a39ce37, 0xd3faf5cf,\r\n        0xabc27737, 0x5ac52d1b, 0x5cb0679e, 0x4fa33742, 0xd3822740,\r\n        0x99bc9bbe, 0xd5118e9d, 0xbf0f7315, 0xd62d1c7e, 0xc700c47b,\r\n        0xb78c1b6b, 0x21a19045, 0xb26eb1be, 0x6a366eb4, 0x5748ab2f,\r\n        0xbc946e79, 0xc6a376d2, 0x6549c2c8, 0x530ff8ee, 0x468dde7d,\r\n        0xd5730a1d, 0x4cd04dc6, 0x2939bbdb, 0xa9ba4650, 0xac9526e8,\r\n        0xbe5ee304, 0xa1fad5f0, 0x6a2d519a, 0x63ef8ce2, 0x9a86ee22,\r\n        0xc089c2b8, 0x43242ef6, 0xa51e03aa, 0x9cf2d0a4, 0x83c061ba,\r\n        0x9be96a4d, 0x8fe51550, 0xba645bd6, 0x2826a2f9, 0xa73a3ae1,\r\n        0x4ba99586, 0xef5562e9, 0xc72fefd3, 0xf752f7da, 0x3f046f69,\r\n        0x77fa0a59, 0x80e4a915, 0x87b08601, 0x9b09e6ad, 0x3b3ee593,\r\n        0xe990fd5a, 0x9e34d797, 0x2cf0b7d9, 0x022b8b51, 0x96d5ac3a,\r\n        0x017da67d, 0xd1cf3ed6, 0x7c7d2d28, 0x1f9f25cf, 0xadf2b89b,\r\n        0x5ad6b472, 0x5a88f54c, 0xe029ac71, 0xe019a5e6, 0x47b0acfd,\r\n        0xed93fa9b, 0xe8d3c48d, 0x283b57cc, 0xf8d56629, 0x79132e28,\r\n        0x785f0191, 0xed756055, 0xf7960e44, 0xe3d35e8c, 0x15056dd4,\r\n        0x88f46dba, 0x03a16125, 0x0564f0bd, 0xc3eb9e15, 0x3c9057a2,\r\n        0x97271aec, 0xa93a072a, 0x1b3f6d9b, 0x1e6321f5, 0xf59c66fb,\r\n        0x26dcf319, 0x7533d928, 0xb155fdf5, 0x03563482, 0x8aba3cbb,\r\n        0x28517711, 0xc20ad9f8, 0xabcc5167, 0xccad925f, 0x4de81751,\r\n        0x3830dc8e, 0x379d5862, 0x9320f991, 0xea7a90c2, 0xfb3e7bce,\r\n        0x5121ce64, 0x774fbe32, 0xa8b6e37e, 0xc3293d46, 0x48de5369,\r\n        0x6413e680, 0xa2ae0810, 0xdd6db224, 0x69852dfd, 0x09072166,\r\n        0xb39a460a, 0x6445c0dd, 0x586cdecf, 0x1c20c8ae, 0x5bbef7dd,\r\n        0x1b588d40, 0xccd2017f, 0x6bb4e3bb, 0xdda26a7e, 0x3a59ff45,\r\n        0x3e350a44, 0xbcb4cdd5, 0x72eacea8, 0xfa6484bb, 0x8d6612ae,\r\n        0xbf3c6f47, 0xd29be463, 0x542f5d9e, 0xaec2771b, 0xf64e6370,\r\n        0x740e0d8d, 0xe75b1357, 0xf8721671, 0xaf537d5d, 0x4040cb08,\r\n        0x4eb4e2cc, 0x34d2466a, 0x0115af84, 0xe1b00428, 0x95983a1d,\r\n        0x06b89fb4, 0xce6ea048, 0x6f3f3b82, 0x3520ab82, 0x011a1d4b,\r\n        0x277227f8, 0x611560b1, 0xe7933fdc, 0xbb3a792b, 0x344525bd,\r\n        0xa08839e1, 0x51ce794b, 0x2f32c9b7, 0xa01fbac9, 0xe01cc87e,\r\n        0xbcc7d1f6, 0xcf0111c3, 0xa1e8aac7, 0x1a908749, 0xd44fbd9a,\r\n        0xd0dadecb, 0xd50ada38, 0x0339c32a, 0xc6913667, 0x8df9317c,\r\n        0xe0b12b4f, 0xf79e59b7, 0x43f5bb3a, 0xf2d519ff, 0x27d9459c,\r\n        0xbf97222c, 0x15e6fc2a, 0x0f91fc71, 0x9b941525, 0xfae59361,\r\n        0xceb69ceb, 0xc2a86459, 0x12baa8d1, 0xb6c1075e, 0xe3056a0c,\r\n        0x10d25065, 0xcb03a442, 0xe0ec6e0e, 0x1698db3b, 0x4c98a0be,\r\n        0x3278e964, 0x9f1f9532, 0xe0d392df, 0xd3a0342b, 0x8971f21e,\r\n        0x1b0a7441, 0x4ba3348c, 0xc5be7120, 0xc37632d8, 0xdf359f8d,\r\n        0x9b992f2e, 0xe60b6f47, 0x0fe3f11d, 0xe54cda54, 0x1edad891,\r\n        0xce6279cf, 0xcd3e7e6f, 0x1618b166, 0xfd2c1d05, 0x848fd2c5,\r\n        0xf6fb2299, 0xf523f357, 0xa6327623, 0x93a83531, 0x56cccd02,\r\n        0xacf08162, 0x5a75ebb5, 0x6e163697, 0x88d273cc, 0xde966292,\r\n        0x81b949d0, 0x4c50901b, 0x71c65614, 0xe6c6c7bd, 0x327a140a,\r\n        0x45e1d006, 0xc3f27b9a, 0xc9aa53fd, 0x62a80f00, 0xbb25bfe2,\r\n        0x35bdd2f6, 0x71126905, 0xb2040222, 0xb6cbcf7c, 0xcd769c2b,\r\n        0x53113ec0, 0x1640e3d3, 0x38abbd60, 0x2547adf0, 0xba38209c,\r\n        0xf746ce76, 0x77afa1c5, 0x20756060, 0x85cbfe4e, 0x8ae88dd8,\r\n        0x7aaaf9b0, 0x4cf9aa7e, 0x1948c25c, 0x02fb8a8c, 0x01c36ae4,\r\n        0xd6ebe1f9, 0x90d4f869, 0xa65cdea0, 0x3f09252d, 0xc208e69f,\r\n        0xb74e6132, 0xce77e25b, 0x578fdfe3, 0x3ac372e6\r\n    ];\r\n\r\n    /**\r\n     * @type {Array.<number>}\r\n     * @const\r\n     * @inner\r\n     */\r\n    var C_ORIG = [\r\n        0x4f727068, 0x65616e42, 0x65686f6c, 0x64657253, 0x63727944,\r\n        0x6f756274\r\n    ];\r\n\r\n    /**\r\n     * @param {Array.<number>} lr\r\n     * @param {number} off\r\n     * @param {Array.<number>} P\r\n     * @param {Array.<number>} S\r\n     * @returns {Array.<number>}\r\n     * @inner\r\n     */\r\n    function _encipher(lr, off, P, S) { // This is our bottleneck: 1714/1905 ticks / 90% - see profile.txt\r\n        var n,\r\n            l = lr[off],\r\n            r = lr[off + 1];\r\n\r\n        l ^= P[0];\r\n\r\n        /*\r\n        for (var i=0, k=BLOWFISH_NUM_ROUNDS-2; i<=k;)\r\n            // Feistel substitution on left word\r\n            n  = S[l >>> 24],\r\n            n += S[0x100 | ((l >> 16) & 0xff)],\r\n            n ^= S[0x200 | ((l >> 8) & 0xff)],\r\n            n += S[0x300 | (l & 0xff)],\r\n            r ^= n ^ P[++i],\r\n            // Feistel substitution on right word\r\n            n  = S[r >>> 24],\r\n            n += S[0x100 | ((r >> 16) & 0xff)],\r\n            n ^= S[0x200 | ((r >> 8) & 0xff)],\r\n            n += S[0x300 | (r & 0xff)],\r\n            l ^= n ^ P[++i];\r\n        */\r\n\r\n        //The following is an unrolled version of the above loop.\r\n        //Iteration 0\r\n        n  = S[l >>> 24];\r\n        n += S[0x100 | ((l >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((l >> 8) & 0xff)];\r\n        n += S[0x300 | (l & 0xff)];\r\n        r ^= n ^ P[1];\r\n        n  = S[r >>> 24];\r\n        n += S[0x100 | ((r >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((r >> 8) & 0xff)];\r\n        n += S[0x300 | (r & 0xff)];\r\n        l ^= n ^ P[2];\r\n        //Iteration 1\r\n        n  = S[l >>> 24];\r\n        n += S[0x100 | ((l >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((l >> 8) & 0xff)];\r\n        n += S[0x300 | (l & 0xff)];\r\n        r ^= n ^ P[3];\r\n        n  = S[r >>> 24];\r\n        n += S[0x100 | ((r >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((r >> 8) & 0xff)];\r\n        n += S[0x300 | (r & 0xff)];\r\n        l ^= n ^ P[4];\r\n        //Iteration 2\r\n        n  = S[l >>> 24];\r\n        n += S[0x100 | ((l >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((l >> 8) & 0xff)];\r\n        n += S[0x300 | (l & 0xff)];\r\n        r ^= n ^ P[5];\r\n        n  = S[r >>> 24];\r\n        n += S[0x100 | ((r >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((r >> 8) & 0xff)];\r\n        n += S[0x300 | (r & 0xff)];\r\n        l ^= n ^ P[6];\r\n        //Iteration 3\r\n        n  = S[l >>> 24];\r\n        n += S[0x100 | ((l >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((l >> 8) & 0xff)];\r\n        n += S[0x300 | (l & 0xff)];\r\n        r ^= n ^ P[7];\r\n        n  = S[r >>> 24];\r\n        n += S[0x100 | ((r >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((r >> 8) & 0xff)];\r\n        n += S[0x300 | (r & 0xff)];\r\n        l ^= n ^ P[8];\r\n        //Iteration 4\r\n        n  = S[l >>> 24];\r\n        n += S[0x100 | ((l >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((l >> 8) & 0xff)];\r\n        n += S[0x300 | (l & 0xff)];\r\n        r ^= n ^ P[9];\r\n        n  = S[r >>> 24];\r\n        n += S[0x100 | ((r >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((r >> 8) & 0xff)];\r\n        n += S[0x300 | (r & 0xff)];\r\n        l ^= n ^ P[10];\r\n        //Iteration 5\r\n        n  = S[l >>> 24];\r\n        n += S[0x100 | ((l >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((l >> 8) & 0xff)];\r\n        n += S[0x300 | (l & 0xff)];\r\n        r ^= n ^ P[11];\r\n        n  = S[r >>> 24];\r\n        n += S[0x100 | ((r >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((r >> 8) & 0xff)];\r\n        n += S[0x300 | (r & 0xff)];\r\n        l ^= n ^ P[12];\r\n        //Iteration 6\r\n        n  = S[l >>> 24];\r\n        n += S[0x100 | ((l >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((l >> 8) & 0xff)];\r\n        n += S[0x300 | (l & 0xff)];\r\n        r ^= n ^ P[13];\r\n        n  = S[r >>> 24];\r\n        n += S[0x100 | ((r >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((r >> 8) & 0xff)];\r\n        n += S[0x300 | (r & 0xff)];\r\n        l ^= n ^ P[14];\r\n        //Iteration 7\r\n        n  = S[l >>> 24];\r\n        n += S[0x100 | ((l >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((l >> 8) & 0xff)];\r\n        n += S[0x300 | (l & 0xff)];\r\n        r ^= n ^ P[15];\r\n        n  = S[r >>> 24];\r\n        n += S[0x100 | ((r >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((r >> 8) & 0xff)];\r\n        n += S[0x300 | (r & 0xff)];\r\n        l ^= n ^ P[16];\r\n\r\n        lr[off] = r ^ P[BLOWFISH_NUM_ROUNDS + 1];\r\n        lr[off + 1] = l;\r\n        return lr;\r\n    }\r\n\r\n    /**\r\n     * @param {Array.<number>} data\r\n     * @param {number} offp\r\n     * @returns {{key: number, offp: number}}\r\n     * @inner\r\n     */\r\n    function _streamtoword(data, offp) {\r\n        for (var i = 0, word = 0; i < 4; ++i)\r\n            word = (word << 8) | (data[offp] & 0xff),\r\n            offp = (offp + 1) % data.length;\r\n        return { key: word, offp: offp };\r\n    }\r\n\r\n    /**\r\n     * @param {Array.<number>} key\r\n     * @param {Array.<number>} P\r\n     * @param {Array.<number>} S\r\n     * @inner\r\n     */\r\n    function _key(key, P, S) {\r\n        var offset = 0,\r\n            lr = [0, 0],\r\n            plen = P.length,\r\n            slen = S.length,\r\n            sw;\r\n        for (var i = 0; i < plen; i++)\r\n            sw = _streamtoword(key, offset),\r\n            offset = sw.offp,\r\n            P[i] = P[i] ^ sw.key;\r\n        for (i = 0; i < plen; i += 2)\r\n            lr = _encipher(lr, 0, P, S),\r\n            P[i] = lr[0],\r\n            P[i + 1] = lr[1];\r\n        for (i = 0; i < slen; i += 2)\r\n            lr = _encipher(lr, 0, P, S),\r\n            S[i] = lr[0],\r\n            S[i + 1] = lr[1];\r\n    }\r\n\r\n    /**\r\n     * Expensive key schedule Blowfish.\r\n     * @param {Array.<number>} data\r\n     * @param {Array.<number>} key\r\n     * @param {Array.<number>} P\r\n     * @param {Array.<number>} S\r\n     * @inner\r\n     */\r\n    function _ekskey(data, key, P, S) {\r\n        var offp = 0,\r\n            lr = [0, 0],\r\n            plen = P.length,\r\n            slen = S.length,\r\n            sw;\r\n        for (var i = 0; i < plen; i++)\r\n            sw = _streamtoword(key, offp),\r\n            offp = sw.offp,\r\n            P[i] = P[i] ^ sw.key;\r\n        offp = 0;\r\n        for (i = 0; i < plen; i += 2)\r\n            sw = _streamtoword(data, offp),\r\n            offp = sw.offp,\r\n            lr[0] ^= sw.key,\r\n            sw = _streamtoword(data, offp),\r\n            offp = sw.offp,\r\n            lr[1] ^= sw.key,\r\n            lr = _encipher(lr, 0, P, S),\r\n            P[i] = lr[0],\r\n            P[i + 1] = lr[1];\r\n        for (i = 0; i < slen; i += 2)\r\n            sw = _streamtoword(data, offp),\r\n            offp = sw.offp,\r\n            lr[0] ^= sw.key,\r\n            sw = _streamtoword(data, offp),\r\n            offp = sw.offp,\r\n            lr[1] ^= sw.key,\r\n            lr = _encipher(lr, 0, P, S),\r\n            S[i] = lr[0],\r\n            S[i + 1] = lr[1];\r\n    }\r\n\r\n    /**\r\n     * Internaly crypts a string.\r\n     * @param {Array.<number>} b Bytes to crypt\r\n     * @param {Array.<number>} salt Salt bytes to use\r\n     * @param {number} rounds Number of rounds\r\n     * @param {function(Error, Array.<number>=)=} callback Callback receiving the error, if any, and the resulting bytes. If\r\n     *  omitted, the operation will be performed synchronously.\r\n     *  @param {function(number)=} progressCallback Callback called with the current progress\r\n     * @returns {!Array.<number>|undefined} Resulting bytes if callback has been omitted, otherwise `undefined`\r\n     * @inner\r\n     */\r\n    function _crypt(b, salt, rounds, callback, progressCallback) {\r\n        var cdata = C_ORIG.slice(),\r\n            clen = cdata.length,\r\n            err;\r\n\r\n        // Validate\r\n        if (rounds < 4 || rounds > 31) {\r\n            err = Error(\"Illegal number of rounds (4-31): \"+rounds);\r\n            if (callback) {\r\n                nextTick(callback.bind(this, err));\r\n                return;\r\n            } else\r\n                throw err;\r\n        }\r\n        if (salt.length !== BCRYPT_SALT_LEN) {\r\n            err =Error(\"Illegal salt length: \"+salt.length+\" != \"+BCRYPT_SALT_LEN);\r\n            if (callback) {\r\n                nextTick(callback.bind(this, err));\r\n                return;\r\n            } else\r\n                throw err;\r\n        }\r\n        rounds = (1 << rounds) >>> 0;\r\n\r\n        var P, S, i = 0, j;\r\n\r\n        //Use typed arrays when available - huge speedup!\r\n        if (Int32Array) {\r\n            P = new Int32Array(P_ORIG);\r\n            S = new Int32Array(S_ORIG);\r\n        } else {\r\n            P = P_ORIG.slice();\r\n            S = S_ORIG.slice();\r\n        }\r\n\r\n        _ekskey(salt, b, P, S);\r\n\r\n        /**\r\n         * Calcualtes the next round.\r\n         * @returns {Array.<number>|undefined} Resulting array if callback has been omitted, otherwise `undefined`\r\n         * @inner\r\n         */\r\n        function next() {\r\n            if (progressCallback)\r\n                progressCallback(i / rounds);\r\n            if (i < rounds) {\r\n                var start = Date.now();\r\n                for (; i < rounds;) {\r\n                    i = i + 1;\r\n                    _key(b, P, S);\r\n                    _key(salt, P, S);\r\n                    if (Date.now() - start > MAX_EXECUTION_TIME)\r\n                        break;\r\n                }\r\n            } else {\r\n                for (i = 0; i < 64; i++)\r\n                    for (j = 0; j < (clen >> 1); j++)\r\n                        _encipher(cdata, j << 1, P, S);\r\n                var ret = [];\r\n                for (i = 0; i < clen; i++)\r\n                    ret.push(((cdata[i] >> 24) & 0xff) >>> 0),\r\n                    ret.push(((cdata[i] >> 16) & 0xff) >>> 0),\r\n                    ret.push(((cdata[i] >> 8) & 0xff) >>> 0),\r\n                    ret.push((cdata[i] & 0xff) >>> 0);\r\n                if (callback) {\r\n                    callback(null, ret);\r\n                    return;\r\n                } else\r\n                    return ret;\r\n            }\r\n            if (callback)\r\n                nextTick(next);\r\n        }\r\n\r\n        // Async\r\n        if (typeof callback !== 'undefined') {\r\n            next();\r\n\r\n            // Sync\r\n        } else {\r\n            var res;\r\n            while (true)\r\n                if (typeof(res = next()) !== 'undefined')\r\n                    return res || [];\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Internally hashes a string.\r\n     * @param {string} s String to hash\r\n     * @param {?string} salt Salt to use, actually never null\r\n     * @param {function(Error, string=)=} callback Callback receiving the error, if any, and the resulting hash. If omitted,\r\n     *  hashing is perormed synchronously.\r\n     *  @param {function(number)=} progressCallback Callback called with the current progress\r\n     * @returns {string|undefined} Resulting hash if callback has been omitted, otherwise `undefined`\r\n     * @inner\r\n     */\r\n    function _hash(s, salt, callback, progressCallback) {\r\n        var err;\r\n        if (typeof s !== 'string' || typeof salt !== 'string') {\r\n            err = Error(\"Invalid string / salt: Not a string\");\r\n            if (callback) {\r\n                nextTick(callback.bind(this, err));\r\n                return;\r\n            }\r\n            else\r\n                throw err;\r\n        }\r\n\r\n        // Validate the salt\r\n        var minor, offset;\r\n        if (salt.charAt(0) !== '$' || salt.charAt(1) !== '2') {\r\n            err = Error(\"Invalid salt version: \"+salt.substring(0,2));\r\n            if (callback) {\r\n                nextTick(callback.bind(this, err));\r\n                return;\r\n            }\r\n            else\r\n                throw err;\r\n        }\r\n        if (salt.charAt(2) === '$')\r\n            minor = String.fromCharCode(0),\r\n            offset = 3;\r\n        else {\r\n            minor = salt.charAt(2);\r\n            if ((minor !== 'a' && minor !== 'b' && minor !== 'y') || salt.charAt(3) !== '$') {\r\n                err = Error(\"Invalid salt revision: \"+salt.substring(2,4));\r\n                if (callback) {\r\n                    nextTick(callback.bind(this, err));\r\n                    return;\r\n                } else\r\n                    throw err;\r\n            }\r\n            offset = 4;\r\n        }\r\n\r\n        // Extract number of rounds\r\n        if (salt.charAt(offset + 2) > '$') {\r\n            err = Error(\"Missing salt rounds\");\r\n            if (callback) {\r\n                nextTick(callback.bind(this, err));\r\n                return;\r\n            } else\r\n                throw err;\r\n        }\r\n        var r1 = parseInt(salt.substring(offset, offset + 1), 10) * 10,\r\n            r2 = parseInt(salt.substring(offset + 1, offset + 2), 10),\r\n            rounds = r1 + r2,\r\n            real_salt = salt.substring(offset + 3, offset + 25);\r\n        s += minor >= 'a' ? \"\\x00\" : \"\";\r\n\r\n        var passwordb = stringToBytes(s),\r\n            saltb = base64_decode(real_salt, BCRYPT_SALT_LEN);\r\n\r\n        /**\r\n         * Finishes hashing.\r\n         * @param {Array.<number>} bytes Byte array\r\n         * @returns {string}\r\n         * @inner\r\n         */\r\n        function finish(bytes) {\r\n            var res = [];\r\n            res.push(\"$2\");\r\n            if (minor >= 'a')\r\n                res.push(minor);\r\n            res.push(\"$\");\r\n            if (rounds < 10)\r\n                res.push(\"0\");\r\n            res.push(rounds.toString());\r\n            res.push(\"$\");\r\n            res.push(base64_encode(saltb, saltb.length));\r\n            res.push(base64_encode(bytes, C_ORIG.length * 4 - 1));\r\n            return res.join('');\r\n        }\r\n\r\n        // Sync\r\n        if (typeof callback == 'undefined')\r\n            return finish(_crypt(passwordb, saltb, rounds));\r\n\r\n        // Async\r\n        else {\r\n            _crypt(passwordb, saltb, rounds, function(err, bytes) {\r\n                if (err)\r\n                    callback(err, null);\r\n                else\r\n                    callback(null, finish(bytes));\r\n            }, progressCallback);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Encodes a byte array to base64 with up to len bytes of input, using the custom bcrypt alphabet.\r\n     * @function\r\n     * @param {!Array.<number>} b Byte array\r\n     * @param {number} len Maximum input length\r\n     * @returns {string}\r\n     * @expose\r\n     */\r\n    bcrypt.encodeBase64 = base64_encode;\r\n\r\n    /**\r\n     * Decodes a base64 encoded string to up to len bytes of output, using the custom bcrypt alphabet.\r\n     * @function\r\n     * @param {string} s String to decode\r\n     * @param {number} len Maximum output length\r\n     * @returns {!Array.<number>}\r\n     * @expose\r\n     */\r\n    bcrypt.decodeBase64 = base64_decode;\r\n\r\n    return bcrypt;\r\n}));\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/bcryptjs/dist/bcrypt.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/bcryptjs/index.js":
/*!****************************************!*\
  !*** ./node_modules/bcryptjs/index.js ***!
  \****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/*\r\n Copyright (c) 2012 Nevins Bartolomeo <<EMAIL>>\r\n Copyright (c) 2012 Shane Girish <<EMAIL>>\r\n Copyright (c) 2013 Daniel Wirtz <<EMAIL>>\r\n\r\n Redistribution and use in source and binary forms, with or without\r\n modification, are permitted provided that the following conditions\r\n are met:\r\n 1. Redistributions of source code must retain the above copyright\r\n notice, this list of conditions and the following disclaimer.\r\n 2. Redistributions in binary form must reproduce the above copyright\r\n notice, this list of conditions and the following disclaimer in the\r\n documentation and/or other materials provided with the distribution.\r\n 3. The name of the author may not be used to endorse or promote products\r\n derived from this software without specific prior written permission.\r\n\r\n THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR\r\n IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES\r\n OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\r\n IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT,\r\n INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\r\n NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,\r\n DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY\r\n THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\r\n (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF\r\n THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\r\n */\r\n\r\nmodule.exports = __webpack_require__(/*! ./dist/bcrypt.js */ \"(rsc)/./node_modules/bcryptjs/dist/bcrypt.js\");\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/bcryptjs/index.js\n");

/***/ })

};
;