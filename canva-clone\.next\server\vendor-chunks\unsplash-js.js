"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/unsplash-js";
exports.ids = ["vendor-chunks/unsplash-js"];
exports.modules = {

/***/ "(rsc)/./node_modules/unsplash-js/dist/unsplash-js.esm.js":
/*!**********************************************************!*\
  !*** ./node_modules/unsplash-js/dist/unsplash-js.esm.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Language: () => (/* binding */ Language),\n/* harmony export */   OrderBy: () => (/* binding */ OrderBy),\n/* harmony export */   _internals: () => (/* binding */ internals),\n/* harmony export */   createApi: () => (/* binding */ createApi)\n/* harmony export */ });\nfunction _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n\n    return target;\n  };\n\n  return _extends.apply(this, arguments);\n}\n\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n\n  return target;\n}\n\nvar checkIsString = /*#__PURE__*/getRefinement(function (value) {\n  return typeof value === 'string' ? value : null;\n});\nvar isDefined = function isDefined(x) {\n  return x !== null && x !== undefined;\n};\nfunction getRefinement(getB) {\n  return function (a) {\n    return isDefined(getB(a));\n  };\n}\nvar checkIsNonEmptyArray = function checkIsNonEmptyArray(a) {\n  return a.length > 0;\n};\n\n/** Takes a dictionary containing nullish values and returns a dictionary of all the defined\r\n * (non-nullish) values.\r\n */\n\nvar compactDefined = function compactDefined(obj) {\n  return Object.keys(obj).reduce(function (acc, key) {\n    var _ref;\n\n    var value = obj[key];\n    return _extends({}, acc, isDefined(value) ? (_ref = {}, _ref[key] = value, _ref) : {});\n  }, {});\n};\nfunction flow() {\n  for (var _len = arguments.length, fns = new Array(_len), _key = 0; _key < _len; _key++) {\n    fns[_key] = arguments[_key];\n  }\n\n  var len = fns.length - 1;\n  return function () {\n    for (var _len2 = arguments.length, x = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      x[_key2] = arguments[_key2];\n    }\n\n    var y = fns[0].apply(this, x);\n\n    for (var i = 1; i <= len; i++) {\n      y = fns[i].call(this, y);\n    }\n\n    return y;\n  };\n}\n\nvar checkIsObject = /*#__PURE__*/getRefinement(function (response) {\n  return isDefined(response) && typeof response === 'object' && !Array.isArray(response) ? response : null;\n});\nvar checkIsErrors = /*#__PURE__*/getRefinement(function (errors) {\n  return Array.isArray(errors) && errors.every(checkIsString) && checkIsNonEmptyArray(errors) ? errors : null;\n});\nvar checkIsApiError = /*#__PURE__*/getRefinement(function (response) {\n  return checkIsObject(response) && 'errors' in response && checkIsErrors(response.errors) ? {\n    errors: response.errors\n  } : null;\n});\nvar getErrorForBadStatusCode = function getErrorForBadStatusCode(jsonResponse) {\n  if (checkIsApiError(jsonResponse)) {\n    return {\n      errors: jsonResponse.errors,\n      source: 'api'\n    };\n  } else {\n    return {\n      errors: ['Responded with a status code outside the 2xx range, and the response body is not recognisable.'],\n      source: 'decoding'\n    };\n  }\n};\nvar DecodingError = function DecodingError(message) {\n  this.message = message;\n};\n\nvar isJSON = function isJSON(contentType) {\n  return /application\\/[^+]*[+]?(json);?.*/.test(contentType);\n};\n\nvar checkIsJsonResponse = function checkIsJsonResponse(response) {\n  var contentTypeHeader = response.headers.get('content-type');\n  return isDefined(contentTypeHeader) && isJSON(contentTypeHeader);\n};\n/**\r\n * Note: restrict the type of JSON to `AnyJson` so that `any` doesn't leak downstream.\r\n */\n\n\nvar getJsonResponse = function getJsonResponse(response) {\n  if (checkIsJsonResponse(response)) {\n    return response.json()[\"catch\"](function (_err) {\n      throw new DecodingError('unable to parse JSON response.');\n    });\n  } else {\n    throw new DecodingError('expected JSON response from server.');\n  }\n};\n\nvar handleFetchResponse = function handleFetchResponse(handleResponse) {\n  return function (response) {\n    return (response.ok ? handleResponse({\n      response: response\n    }).then(function (handledResponse) {\n      return {\n        type: 'success',\n        status: response.status,\n        response: handledResponse,\n        originalResponse: response\n      };\n    }) : getJsonResponse(response).then(function (jsonResponse) {\n      return _extends({\n        type: 'error',\n        status: response.status\n      }, getErrorForBadStatusCode(jsonResponse), {\n        originalResponse: response\n      });\n    }))[\"catch\"](function (error) {\n      /**\r\n       * We want to separate expected decoding errors from unknown ones. We do so by throwing a custom\r\n       * `DecodingError` whenever we encounter one within `handleFetchResponse` and catch them all\r\n       * here. This allows us to easily handle all of these errors at once. Unexpected errors are not\r\n       * caught, so that they bubble up and fail loudly.\r\n       *\r\n       * Note: Ideally we'd use an Either type, but this does the job without introducing dependencies\r\n       * like `fp-ts`.\r\n       */\n      if (error instanceof DecodingError) {\n        return {\n          type: 'error',\n          source: 'decoding',\n          status: response.status,\n          originalResponse: response,\n          errors: [error.message]\n        };\n      } else {\n        throw error;\n      }\n    });\n  };\n};\nvar castResponse = function castResponse() {\n  return function (_ref) {\n    var response = _ref.response;\n    return getJsonResponse(response);\n  };\n};\n\nvar addQueryToUrl = function addQueryToUrl(query) {\n  return function (url) {\n    Object.keys(query).forEach(function (queryKey) {\n      return url.searchParams.set(queryKey, query[queryKey].toString());\n    });\n  };\n};\n\nvar addPathnameToUrl = function addPathnameToUrl(pathname) {\n  return function (url) {\n    // When there is no existing pathname, the value is `/`. Appending would give us a URL with two\n    // forward slashes. This is why we replace the value in that scenario.\n    if (url.pathname === '/') {\n      url.pathname = pathname;\n    } else {\n      url.pathname += pathname;\n    }\n  };\n};\n\nvar buildUrl = function buildUrl(_ref) {\n  var pathname = _ref.pathname,\n      query = _ref.query;\n  return function (apiUrl) {\n    var url = new URL(apiUrl);\n    addPathnameToUrl(pathname)(url);\n    addQueryToUrl(query)(url);\n    return url.toString();\n  };\n};\n\nvar getQueryFromSearchParams = function getQueryFromSearchParams(searchParams) {\n  var query = {};\n  searchParams.forEach(function (value, key) {\n    query[key] = value;\n  });\n  return query;\n};\n\nvar parseQueryAndPathname = function parseQueryAndPathname(url) {\n  var _URL = new URL(url),\n      pathname = _URL.pathname,\n      searchParams = _URL.searchParams;\n\n  var query = getQueryFromSearchParams(searchParams);\n  return {\n    query: query,\n    pathname: pathname === '/' ? undefined : pathname\n  };\n};\n\n/**\r\n * helper used to type-check the arguments, and add default params for all requests\r\n */\n\nvar createRequestHandler = function createRequestHandler(fn) {\n  return function (a, additionalFetchOptions) {\n    if (additionalFetchOptions === void 0) {\n      additionalFetchOptions = {};\n    }\n\n    var _fn = fn(a),\n        headers = _fn.headers,\n        query = _fn.query,\n        baseReqParams = _objectWithoutPropertiesLoose(_fn, [\"headers\", \"query\"]);\n\n    return _extends({}, baseReqParams, additionalFetchOptions, {\n      query: query,\n      headers: _extends({}, headers, additionalFetchOptions.headers)\n    });\n  };\n};\nvar makeEndpoint = function makeEndpoint(endpoint) {\n  return endpoint;\n};\nvar initMakeRequest = function initMakeRequest(_ref) {\n  var accessKey = _ref.accessKey,\n      _ref$apiVersion = _ref.apiVersion,\n      apiVersion = _ref$apiVersion === void 0 ? 'v1' : _ref$apiVersion,\n      _ref$apiUrl = _ref.apiUrl,\n      apiUrl = _ref$apiUrl === void 0 ? 'https://api.unsplash.com' : _ref$apiUrl,\n      generalHeaders = _ref.headers,\n      providedFetch = _ref.fetch,\n      generalFetchOptions = _objectWithoutPropertiesLoose(_ref, [\"accessKey\", \"apiVersion\", \"apiUrl\", \"headers\", \"fetch\"]);\n\n  return function (_ref2) {\n    var handleResponse = _ref2.handleResponse,\n        handleRequest = _ref2.handleRequest;\n    return flow(handleRequest, function (_ref3) {\n      var pathname = _ref3.pathname,\n          query = _ref3.query,\n          _ref3$method = _ref3.method,\n          method = _ref3$method === void 0 ? 'GET' : _ref3$method,\n          endpointHeaders = _ref3.headers,\n          body = _ref3.body,\n          signal = _ref3.signal;\n      var url = buildUrl({\n        pathname: pathname,\n        query: query\n      })(apiUrl);\n\n      var fetchOptions = _extends({\n        method: method,\n        headers: _extends({}, generalHeaders, endpointHeaders, {\n          'Accept-Version': apiVersion\n        }, isDefined(accessKey) ? {\n          Authorization: \"Client-ID \" + accessKey\n        } : {}),\n        body: body,\n        signal: signal\n      }, generalFetchOptions);\n\n      var fetchToUse = providedFetch != null ? providedFetch : fetch;\n      return fetchToUse(url, fetchOptions).then(handleFetchResponse(handleResponse));\n    });\n  };\n};\n\nvar TOTAL_RESPONSE_HEADER = 'x-total';\n\nvar getTotalFromApiFeedResponse = function getTotalFromApiFeedResponse(response) {\n  var totalsStr = response.headers.get(TOTAL_RESPONSE_HEADER);\n\n  if (isDefined(totalsStr)) {\n    var total = parseInt(totalsStr);\n\n    if (Number.isInteger(total)) {\n      return total;\n    } else {\n      throw new DecodingError(\"expected \" + TOTAL_RESPONSE_HEADER + \" header to be valid integer.\");\n    }\n  } else {\n    throw new DecodingError(\"expected \" + TOTAL_RESPONSE_HEADER + \" header to exist.\");\n  }\n};\n\nvar handleFeedResponse = function handleFeedResponse() {\n  return function (_ref) {\n    var response = _ref.response;\n    return castResponse()({\n      response: response\n    }).then(function (results) {\n      return {\n        results: results,\n        total: getTotalFromApiFeedResponse(response)\n      };\n    });\n  };\n};\n\nvar getCollections = function getCollections(collectionIds) {\n  return isDefined(collectionIds) ? {\n    collections: collectionIds.join()\n  } : {};\n};\nvar getTopics = function getTopics(topicIds) {\n  return isDefined(topicIds) ? {\n    topics: topicIds.join()\n  } : {};\n};\nvar getFeedParams = function getFeedParams(_ref) {\n  var page = _ref.page,\n      perPage = _ref.perPage,\n      orderBy = _ref.orderBy;\n  return compactDefined({\n    per_page: perPage,\n    order_by: orderBy,\n    page: page\n  });\n};\n\nvar COLLECTIONS_PATH_PREFIX = '/collections';\nvar getPhotos = /*#__PURE__*/function () {\n  var getPathname = function getPathname(_ref) {\n    var collectionId = _ref.collectionId;\n    return COLLECTIONS_PATH_PREFIX + \"/\" + collectionId + \"/photos\";\n  };\n\n  return makeEndpoint({\n    getPathname: getPathname,\n    handleRequest: createRequestHandler(function (_ref2) {\n      var collectionId = _ref2.collectionId,\n          orientation = _ref2.orientation,\n          paginationParams = _objectWithoutPropertiesLoose(_ref2, [\"collectionId\", \"orientation\"]);\n\n      return {\n        pathname: getPathname({\n          collectionId: collectionId\n        }),\n        query: compactDefined(_extends({}, getFeedParams(paginationParams), {\n          orientation: orientation\n        }))\n      };\n    }),\n    handleResponse: handleFeedResponse()\n  });\n}();\nvar get = /*#__PURE__*/function () {\n  var getPathname = function getPathname(_ref3) {\n    var collectionId = _ref3.collectionId;\n    return COLLECTIONS_PATH_PREFIX + \"/\" + collectionId;\n  };\n\n  return makeEndpoint({\n    getPathname: getPathname,\n    handleRequest: createRequestHandler(function (_ref4) {\n      var collectionId = _ref4.collectionId;\n      return {\n        pathname: getPathname({\n          collectionId: collectionId\n        }),\n        query: {}\n      };\n    }),\n    handleResponse: castResponse()\n  });\n}();\nvar list = /*#__PURE__*/function () {\n  var getPathname = function getPathname() {\n    return COLLECTIONS_PATH_PREFIX;\n  };\n\n  return makeEndpoint({\n    getPathname: getPathname,\n    handleRequest: createRequestHandler(function (paginationParams) {\n      if (paginationParams === void 0) {\n        paginationParams = {};\n      }\n\n      return {\n        pathname: getPathname(),\n        query: getFeedParams(paginationParams)\n      };\n    }),\n    handleResponse: handleFeedResponse()\n  });\n}();\nvar getRelated = /*#__PURE__*/function () {\n  var getPathname = function getPathname(_ref5) {\n    var collectionId = _ref5.collectionId;\n    return COLLECTIONS_PATH_PREFIX + \"/\" + collectionId + \"/related\";\n  };\n\n  return makeEndpoint({\n    getPathname: getPathname,\n    handleRequest: createRequestHandler(function (_ref6) {\n      var collectionId = _ref6.collectionId;\n      return {\n        pathname: getPathname({\n          collectionId: collectionId\n        }),\n        query: {}\n      };\n    }),\n    handleResponse: castResponse()\n  });\n}();\n\nvar index = {\n  __proto__: null,\n  getPhotos: getPhotos,\n  get: get,\n  list: list,\n  getRelated: getRelated\n};\n\nvar PHOTOS_PATH_PREFIX = '/photos';\nvar list$1 = /*#__PURE__*/function () {\n  var _getPathname = function getPathname() {\n    return PHOTOS_PATH_PREFIX;\n  };\n\n  return makeEndpoint({\n    // Wrapper uses type trick to allow 0 args\n    getPathname: function getPathname(_params) {\n      return _getPathname();\n    },\n    handleRequest: createRequestHandler(function (feedParams) {\n      if (feedParams === void 0) {\n        feedParams = {};\n      }\n\n      return {\n        pathname: PHOTOS_PATH_PREFIX,\n        query: compactDefined(getFeedParams(feedParams))\n      };\n    }),\n    handleResponse: handleFeedResponse()\n  });\n}();\nvar get$1 = /*#__PURE__*/function () {\n  var getPathname = function getPathname(_ref) {\n    var photoId = _ref.photoId;\n    return PHOTOS_PATH_PREFIX + \"/\" + photoId;\n  };\n\n  return makeEndpoint({\n    getPathname: getPathname,\n    handleRequest: createRequestHandler(function (_ref2) {\n      var photoId = _ref2.photoId;\n      return {\n        pathname: getPathname({\n          photoId: photoId\n        }),\n        query: {}\n      };\n    }),\n    handleResponse: castResponse()\n  });\n}();\nvar getStats = /*#__PURE__*/function () {\n  var getPathname = function getPathname(_ref3) {\n    var photoId = _ref3.photoId;\n    return PHOTOS_PATH_PREFIX + \"/\" + photoId + \"/statistics\";\n  };\n\n  return makeEndpoint({\n    getPathname: getPathname,\n    handleRequest: createRequestHandler(function (_ref4) {\n      var photoId = _ref4.photoId;\n      return {\n        pathname: getPathname({\n          photoId: photoId\n        }),\n        query: {}\n      };\n    }),\n    handleResponse: castResponse()\n  });\n}();\nvar getRandom = /*#__PURE__*/function () {\n  var getPathname = function getPathname() {\n    return PHOTOS_PATH_PREFIX + \"/random\";\n  };\n\n  return makeEndpoint({\n    getPathname: getPathname,\n    handleRequest: createRequestHandler(function (_temp) {\n      var _ref5 = _temp === void 0 ? {} : _temp,\n          collectionIds = _ref5.collectionIds,\n          contentFilter = _ref5.contentFilter,\n          topicIds = _ref5.topicIds,\n          queryParams = _objectWithoutPropertiesLoose(_ref5, [\"collectionIds\", \"contentFilter\", \"topicIds\"]);\n\n      return {\n        pathname: getPathname(),\n        query: compactDefined(_extends({}, queryParams, {\n          content_filter: contentFilter\n        }, getCollections(collectionIds), getTopics(topicIds))),\n        headers: {\n          /**\r\n           * Avoid response caching\r\n           */\n          'cache-control': 'no-cache'\n        }\n      };\n    }),\n    handleResponse: castResponse()\n  });\n}();\nvar trackDownload = {\n  handleRequest: /*#__PURE__*/createRequestHandler(function (_ref6) {\n    var downloadLocation = _ref6.downloadLocation;\n\n    var _parseQueryAndPathnam = parseQueryAndPathname(downloadLocation),\n        pathname = _parseQueryAndPathnam.pathname,\n        query = _parseQueryAndPathnam.query;\n\n    if (!isDefined(pathname)) {\n      throw new Error('Could not parse pathname from url.');\n    }\n\n    return {\n      pathname: pathname,\n      query: compactDefined(query)\n    };\n  }),\n  handleResponse: /*#__PURE__*/castResponse()\n};\n\nvar index$1 = {\n  __proto__: null,\n  list: list$1,\n  get: get$1,\n  getStats: getStats,\n  getRandom: getRandom,\n  trackDownload: trackDownload\n};\n\nvar SEARCH_PATH_PREFIX = \"/search\";\nvar getPhotos$1 = /*#__PURE__*/function () {\n  var _getPathname = function getPathname() {\n    return SEARCH_PATH_PREFIX + \"/photos\";\n  };\n\n  return makeEndpoint({\n    // Wrapper uses type trick to allow 0 args\n    getPathname: function getPathname(_params) {\n      return _getPathname();\n    },\n    handleRequest: createRequestHandler(function (_ref) {\n      var query = _ref.query,\n          page = _ref.page,\n          perPage = _ref.perPage,\n          orderBy = _ref.orderBy,\n          collectionIds = _ref.collectionIds,\n          lang = _ref.lang,\n          contentFilter = _ref.contentFilter,\n          filters = _objectWithoutPropertiesLoose(_ref, [\"query\", \"page\", \"perPage\", \"orderBy\", \"collectionIds\", \"lang\", \"contentFilter\"]);\n\n      return {\n        pathname: _getPathname(),\n        query: compactDefined(_extends({\n          query: query,\n          content_filter: contentFilter,\n          lang: lang,\n          order_by: orderBy\n        }, getFeedParams({\n          page: page,\n          perPage: perPage\n        }), getCollections(collectionIds), filters))\n      };\n    }),\n    handleResponse: castResponse()\n  });\n}();\nvar getCollections$1 = /*#__PURE__*/function () {\n  var _getPathname2 = function getPathname() {\n    return SEARCH_PATH_PREFIX + \"/collections\";\n  };\n\n  return makeEndpoint({\n    // Wrapper uses type trick to allow 0 args\n    getPathname: function getPathname(_params) {\n      return _getPathname2();\n    },\n    handleRequest: createRequestHandler(function (_ref2) {\n      var query = _ref2.query,\n          paginationParams = _objectWithoutPropertiesLoose(_ref2, [\"query\"]);\n\n      return {\n        pathname: _getPathname2(),\n        query: _extends({\n          query: query\n        }, getFeedParams(paginationParams))\n      };\n    }),\n    handleResponse: castResponse()\n  });\n}();\nvar getUsers = /*#__PURE__*/function () {\n  var _getPathname3 = function getPathname() {\n    return SEARCH_PATH_PREFIX + \"/users\";\n  };\n\n  return makeEndpoint({\n    // Wrapper uses type trick to allow 0 args\n    getPathname: function getPathname(_params) {\n      return _getPathname3();\n    },\n    handleRequest: createRequestHandler(function (_ref3) {\n      var query = _ref3.query,\n          paginationParams = _objectWithoutPropertiesLoose(_ref3, [\"query\"]);\n\n      return {\n        pathname: _getPathname3(),\n        query: _extends({\n          query: query\n        }, getFeedParams(paginationParams))\n      };\n    }),\n    handleResponse: castResponse()\n  });\n}();\n\nvar index$2 = {\n  __proto__: null,\n  getPhotos: getPhotos$1,\n  getCollections: getCollections$1,\n  getUsers: getUsers\n};\n\nvar USERS_PATH_PREFIX = '/users';\nvar get$2 = /*#__PURE__*/function () {\n  var getPathname = function getPathname(_ref) {\n    var username = _ref.username;\n    return USERS_PATH_PREFIX + \"/\" + username;\n  };\n\n  return makeEndpoint({\n    getPathname: getPathname,\n    handleRequest: createRequestHandler(function (_ref2) {\n      var username = _ref2.username;\n      return {\n        pathname: getPathname({\n          username: username\n        }),\n        query: {}\n      };\n    }),\n    handleResponse: castResponse()\n  });\n}();\nvar getPhotos$2 = /*#__PURE__*/function () {\n  var getPathname = function getPathname(_ref3) {\n    var username = _ref3.username;\n    return USERS_PATH_PREFIX + \"/\" + username + \"/photos\";\n  };\n\n  return makeEndpoint({\n    getPathname: getPathname,\n    handleRequest: createRequestHandler(function (_ref4) {\n      var username = _ref4.username,\n          stats = _ref4.stats,\n          orientation = _ref4.orientation,\n          paginationParams = _objectWithoutPropertiesLoose(_ref4, [\"username\", \"stats\", \"orientation\"]);\n\n      return {\n        pathname: getPathname({\n          username: username\n        }),\n        query: compactDefined(_extends({}, getFeedParams(paginationParams), {\n          orientation: orientation,\n          stats: stats\n        }))\n      };\n    }),\n    handleResponse: handleFeedResponse()\n  });\n}();\nvar getLikes = /*#__PURE__*/function () {\n  var getPathname = function getPathname(_ref5) {\n    var username = _ref5.username;\n    return USERS_PATH_PREFIX + \"/\" + username + \"/likes\";\n  };\n\n  return makeEndpoint({\n    getPathname: getPathname,\n    handleRequest: createRequestHandler(function (_ref6) {\n      var username = _ref6.username,\n          orientation = _ref6.orientation,\n          paginationParams = _objectWithoutPropertiesLoose(_ref6, [\"username\", \"orientation\"]);\n\n      return {\n        pathname: getPathname({\n          username: username\n        }),\n        query: compactDefined(_extends({}, getFeedParams(paginationParams), {\n          orientation: orientation\n        }))\n      };\n    }),\n    handleResponse: handleFeedResponse()\n  });\n}();\nvar getCollections$2 = /*#__PURE__*/function () {\n  var getPathname = function getPathname(_ref7) {\n    var username = _ref7.username;\n    return USERS_PATH_PREFIX + \"/\" + username + \"/collections\";\n  };\n\n  return makeEndpoint({\n    getPathname: getPathname,\n    handleRequest: createRequestHandler(function (_ref8) {\n      var username = _ref8.username,\n          paginationParams = _objectWithoutPropertiesLoose(_ref8, [\"username\"]);\n\n      return {\n        pathname: getPathname({\n          username: username\n        }),\n        query: getFeedParams(paginationParams)\n      };\n    }),\n    handleResponse: handleFeedResponse()\n  });\n}();\n\nvar index$3 = {\n  __proto__: null,\n  get: get$2,\n  getPhotos: getPhotos$2,\n  getLikes: getLikes,\n  getCollections: getCollections$2\n};\n\nvar BASE_TOPIC_PATH = '/topics';\n\nvar getTopicPath = function getTopicPath(_ref) {\n  var topicIdOrSlug = _ref.topicIdOrSlug;\n  return BASE_TOPIC_PATH + \"/\" + topicIdOrSlug;\n};\n\nvar list$2 = /*#__PURE__*/makeEndpoint({\n  getPathname: getTopicPath,\n  handleRequest: function handleRequest(_ref2) {\n    var page = _ref2.page,\n        perPage = _ref2.perPage,\n        orderBy = _ref2.orderBy,\n        topicIdsOrSlugs = _ref2.topicIdsOrSlugs;\n    return {\n      pathname: BASE_TOPIC_PATH,\n      query: compactDefined(_extends({}, getFeedParams({\n        page: page,\n        perPage: perPage\n      }), {\n        ids: topicIdsOrSlugs == null ? void 0 : topicIdsOrSlugs.join(','),\n        order_by: orderBy\n      }))\n    };\n  },\n  handleResponse: /*#__PURE__*/handleFeedResponse()\n});\nvar get$3 = /*#__PURE__*/makeEndpoint({\n  getPathname: getTopicPath,\n  handleRequest: function handleRequest(_ref3) {\n    var topicIdOrSlug = _ref3.topicIdOrSlug;\n    return {\n      pathname: getTopicPath({\n        topicIdOrSlug: topicIdOrSlug\n      }),\n      query: {}\n    };\n  },\n  handleResponse: /*#__PURE__*/castResponse()\n});\nvar getPhotos$3 = /*#__PURE__*/function () {\n  var getPathname = /*#__PURE__*/flow(getTopicPath, function (topicPath) {\n    return topicPath + \"/photos\";\n  });\n  return makeEndpoint({\n    getPathname: getPathname,\n    handleRequest: function handleRequest(_ref4) {\n      var topicIdOrSlug = _ref4.topicIdOrSlug,\n          orientation = _ref4.orientation,\n          feedParams = _objectWithoutPropertiesLoose(_ref4, [\"topicIdOrSlug\", \"orientation\"]);\n\n      return {\n        pathname: getPathname({\n          topicIdOrSlug: topicIdOrSlug\n        }),\n        query: compactDefined(_extends({}, getFeedParams(feedParams), {\n          orientation: orientation\n        }))\n      };\n    },\n    handleResponse: handleFeedResponse()\n  });\n}();\n\nvar index$4 = {\n  __proto__: null,\n  list: list$2,\n  get: get$3,\n  getPhotos: getPhotos$3\n};\n\nvar trackNonHotLinkedPhotoView = function trackNonHotLinkedPhotoView(_ref) {\n  var appId = _ref.appId;\n  return function (_ref2) {\n    var photoId = _ref2.photoId;\n    var ids = !Array.isArray(photoId) ? [photoId] : photoId;\n\n    if (ids.length > 20) {\n      throw new Error('You cannot track more than 20 photos at once. Please try again with fewer photos.');\n    }\n\n    return fetch(\"views.unsplash.com/v?photo_id=\" + ids.join() + \"&app_id=\" + appId);\n  };\n};\n\n\n\nvar internals = {\n  __proto__: null,\n  collections: index,\n  photos: index$1,\n  search: index$2,\n  users: index$3,\n  topics: index$4,\n  trackNonHotLinkedPhotoView: trackNonHotLinkedPhotoView\n};\n\nvar Language;\n\n(function (Language) {\n  Language[\"Afrikaans\"] = \"af\";\n  Language[\"Amharic\"] = \"am\";\n  Language[\"Arabic\"] = \"ar\";\n  Language[\"Azerbaijani\"] = \"az\";\n  Language[\"Belarusian\"] = \"be\";\n  Language[\"Bulgarian\"] = \"bg\";\n  Language[\"Bengali\"] = \"bn\";\n  Language[\"Bosnian\"] = \"bs\";\n  Language[\"Catalan\"] = \"ca\";\n  Language[\"Cebuano\"] = \"ceb\";\n  Language[\"Corsican\"] = \"co\";\n  Language[\"Czech\"] = \"cs\";\n  Language[\"Welsh\"] = \"cy\";\n  Language[\"Danish\"] = \"da\";\n  Language[\"German\"] = \"de\";\n  Language[\"Greek\"] = \"el\";\n  Language[\"English\"] = \"en\";\n  Language[\"Esperanto\"] = \"eo\";\n  Language[\"Spanish\"] = \"es\";\n  Language[\"Estonian\"] = \"et\";\n  Language[\"Basque\"] = \"eu\";\n  Language[\"Persian\"] = \"fa\";\n  Language[\"Finnish\"] = \"fi\";\n  Language[\"French\"] = \"fr\";\n  Language[\"Frisian\"] = \"fy\";\n  Language[\"Irish\"] = \"ga\";\n  Language[\"ScotsGaelic\"] = \"gd\";\n  Language[\"Galician\"] = \"gl\";\n  Language[\"Gujarati\"] = \"gu\";\n  Language[\"Hausa\"] = \"ha\";\n  Language[\"Hawaiian\"] = \"haw\";\n  Language[\"Hindi\"] = \"hi\";\n  Language[\"Hmong\"] = \"hmn\";\n  Language[\"Croatian\"] = \"hr\";\n  Language[\"HaitianCreole\"] = \"ht\";\n  Language[\"Hungarian\"] = \"hu\";\n  Language[\"Armenian\"] = \"hy\";\n  Language[\"Indonesian\"] = \"id\";\n  Language[\"Igbo\"] = \"ig\";\n  Language[\"Icelandic\"] = \"is\";\n  Language[\"Italian\"] = \"it\";\n  Language[\"Hebrew\"] = \"iw\";\n  Language[\"Japanese\"] = \"ja\";\n  Language[\"Javanese\"] = \"jw\";\n  Language[\"Georgian\"] = \"ka\";\n  Language[\"Kazakh\"] = \"kk\";\n  Language[\"Khmer\"] = \"km\";\n  Language[\"Kannada\"] = \"kn\";\n  Language[\"Korean\"] = \"ko\";\n  Language[\"Kurdish\"] = \"ku\";\n  Language[\"Kyrgyz\"] = \"ky\";\n  Language[\"Latin\"] = \"la\";\n  Language[\"Luxembourgish\"] = \"lb\";\n  Language[\"Lao\"] = \"lo\";\n  Language[\"Lithuanian\"] = \"lt\";\n  Language[\"Latvian\"] = \"lv\";\n  Language[\"Malagasy\"] = \"mg\";\n  Language[\"Maori\"] = \"mi\";\n  Language[\"Macedonian\"] = \"mk\";\n  Language[\"Malayalam\"] = \"ml\";\n  Language[\"Mongolian\"] = \"mn\";\n  Language[\"Marathi\"] = \"mr\";\n  Language[\"Malay\"] = \"ms\";\n  Language[\"Maltese\"] = \"mt\";\n  Language[\"Myanmar\"] = \"my\";\n  Language[\"Nepali\"] = \"ne\";\n  Language[\"Dutch\"] = \"nl\";\n  Language[\"Norwegian\"] = \"no\";\n  Language[\"Nyanja\"] = \"ny\";\n  Language[\"Oriya\"] = \"or\";\n  Language[\"Punjabi\"] = \"pa\";\n  Language[\"Polish\"] = \"pl\";\n  Language[\"Pashto\"] = \"ps\";\n  Language[\"Portuguese\"] = \"pt\";\n  Language[\"Romanian\"] = \"ro\";\n  Language[\"Russian\"] = \"ru\";\n  Language[\"Kinyarwanda\"] = \"rw\";\n  Language[\"Sindhi\"] = \"sd\";\n  Language[\"Sinhala\"] = \"si\";\n  Language[\"Slovak\"] = \"sk\";\n  Language[\"Slovenian\"] = \"sl\";\n  Language[\"Samoan\"] = \"sm\";\n  Language[\"Shona\"] = \"sn\";\n  Language[\"Somali\"] = \"so\";\n  Language[\"Albanian\"] = \"sq\";\n  Language[\"Serbian\"] = \"sr\";\n  Language[\"Sesotho\"] = \"st\";\n  Language[\"Sundanese\"] = \"su\";\n  Language[\"Swedish\"] = \"sv\";\n  Language[\"Swahili\"] = \"sw\";\n  Language[\"Tamil\"] = \"ta\";\n  Language[\"Telugu\"] = \"te\";\n  Language[\"Tajik\"] = \"tg\";\n  Language[\"Thai\"] = \"th\";\n  Language[\"Turkmen\"] = \"tk\";\n  Language[\"Filipino\"] = \"tl\";\n  Language[\"Turkish\"] = \"tr\";\n  Language[\"Tatar\"] = \"tt\";\n  Language[\"Uighur\"] = \"ug\";\n  Language[\"Ukrainian\"] = \"uk\";\n  Language[\"Urdu\"] = \"ur\";\n  Language[\"Uzbek\"] = \"uz\";\n  Language[\"Vietnamese\"] = \"vi\";\n  Language[\"Xhosa\"] = \"xh\";\n  Language[\"Yiddish\"] = \"yi\";\n  Language[\"Yoruba\"] = \"yo\";\n  Language[\"ChineseSimplified\"] = \"zh\";\n  Language[\"ChineseTraditional\"] = \"zh-TW\";\n  Language[\"Zulu\"] = \"zu\";\n})(Language || (Language = {}));\n\nvar OrderBy;\n\n(function (OrderBy) {\n  OrderBy[\"LATEST\"] = \"latest\";\n  OrderBy[\"POPULAR\"] = \"popular\";\n  OrderBy[\"VIEWS\"] = \"views\";\n  OrderBy[\"DOWNLOADS\"] = \"downloads\";\n  OrderBy[\"OLDEST\"] = \"oldest\";\n})(OrderBy || (OrderBy = {}));\n\nvar createApi = /*#__PURE__*/flow(initMakeRequest, function (makeRequest) {\n  return {\n    photos: {\n      get: makeRequest(get$1),\n      list: makeRequest(list$1),\n      getStats: makeRequest(getStats),\n      getRandom: makeRequest(getRandom),\n      trackDownload: makeRequest(trackDownload)\n    },\n    users: {\n      getPhotos: makeRequest(getPhotos$2),\n      getCollections: makeRequest(getCollections$2),\n      getLikes: makeRequest(getLikes),\n      get: makeRequest(get$2)\n    },\n    search: {\n      getCollections: makeRequest(getCollections$1),\n      getPhotos: makeRequest(getPhotos$1),\n      getUsers: makeRequest(getUsers)\n    },\n    collections: {\n      getPhotos: makeRequest(getPhotos),\n      get: makeRequest(get),\n      list: makeRequest(list),\n      getRelated: makeRequest(getRelated)\n    },\n    topics: {\n      list: makeRequest(list$2),\n      get: makeRequest(get$3),\n      getPhotos: makeRequest(getPhotos$3)\n    }\n  };\n});\n\n\n//# sourceMappingURL=unsplash-js.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvdW5zcGxhc2gtanMvZGlzdC91bnNwbGFzaC1qcy5lc20uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBO0FBQ0E7QUFDQSxvQkFBb0Isc0JBQXNCO0FBQzFDOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxjQUFjLHVCQUF1QjtBQUNyQztBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0Esc0JBQXNCLG9DQUFvQywrQkFBK0I7QUFDekYsR0FBRyxJQUFJO0FBQ1A7QUFDQTtBQUNBLHFFQUFxRSxhQUFhO0FBQ2xGO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLHdFQUF3RSxlQUFlO0FBQ3ZGO0FBQ0E7O0FBRUE7O0FBRUEsb0JBQW9CLFVBQVU7QUFDOUI7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSixDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLHVDQUF1QztBQUN2Qzs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7O0FBR0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsSUFBSTtBQUNKO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0EsT0FBTztBQUNQLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxzQkFBc0I7QUFDdEI7QUFDQSwwQkFBMEI7QUFDMUIsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87O0FBRVA7QUFDQTtBQUNBLDRCQUE0QjtBQUM1QjtBQUNBLFNBQVM7QUFDVDtBQUNBLFVBQVUsSUFBSTtBQUNkO0FBQ0E7QUFDQSxPQUFPOztBQUVQO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTs7QUFFQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCx5Q0FBeUM7QUFDekM7QUFDQSxTQUFTO0FBQ1Q7QUFDQSxLQUFLO0FBQ0w7QUFDQSxHQUFHO0FBQ0gsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBLEdBQUc7QUFDSCxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0EsR0FBRztBQUNILENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSxHQUFHO0FBQ0gsQ0FBQzs7QUFFRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0EsR0FBRztBQUNILENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSxHQUFHO0FBQ0gsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBLEdBQUc7QUFDSCxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0Esd0NBQXdDO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSx5Q0FBeUM7QUFDekM7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSxHQUFHO0FBQ0gsQ0FBQztBQUNEO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0EsS0FBSztBQUNMO0FBQ0EsR0FBRztBQUNILENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQSxLQUFLO0FBQ0w7QUFDQSxHQUFHO0FBQ0gsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBLEtBQUs7QUFDTDtBQUNBLEdBQUc7QUFDSCxDQUFDOztBQUVEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBLEdBQUc7QUFDSCxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1QseUNBQXlDO0FBQ3pDO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQSxLQUFLO0FBQ0w7QUFDQSxHQUFHO0FBQ0gsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCx5Q0FBeUM7QUFDekM7QUFDQSxTQUFTO0FBQ1Q7QUFDQSxLQUFLO0FBQ0w7QUFDQSxHQUFHO0FBQ0gsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBLEdBQUc7QUFDSCxDQUFDOztBQUVEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVDQUF1QztBQUN2QztBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQSxHQUFHO0FBQ0g7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCx5Q0FBeUM7QUFDekM7QUFDQSxTQUFTO0FBQ1Q7QUFDQSxLQUFLO0FBQ0w7QUFDQSxHQUFHO0FBQ0gsQ0FBQzs7QUFFRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOzs7O0FBSUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLDRCQUE0Qjs7QUFFN0I7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQywwQkFBMEI7O0FBRTNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7O0FBRWdFO0FBQ2pFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGhlLWNhbnZhcy8uL25vZGVfbW9kdWxlcy91bnNwbGFzaC1qcy9kaXN0L3Vuc3BsYXNoLWpzLmVzbS5qcz8xMDg4Il0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIF9leHRlbmRzKCkge1xuICBfZXh0ZW5kcyA9IE9iamVjdC5hc3NpZ24gfHwgZnVuY3Rpb24gKHRhcmdldCkge1xuICAgIGZvciAodmFyIGkgPSAxOyBpIDwgYXJndW1lbnRzLmxlbmd0aDsgaSsrKSB7XG4gICAgICB2YXIgc291cmNlID0gYXJndW1lbnRzW2ldO1xuXG4gICAgICBmb3IgKHZhciBrZXkgaW4gc291cmNlKSB7XG4gICAgICAgIGlmIChPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LmNhbGwoc291cmNlLCBrZXkpKSB7XG4gICAgICAgICAgdGFyZ2V0W2tleV0gPSBzb3VyY2Vba2V5XTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cblxuICAgIHJldHVybiB0YXJnZXQ7XG4gIH07XG5cbiAgcmV0dXJuIF9leHRlbmRzLmFwcGx5KHRoaXMsIGFyZ3VtZW50cyk7XG59XG5cbmZ1bmN0aW9uIF9vYmplY3RXaXRob3V0UHJvcGVydGllc0xvb3NlKHNvdXJjZSwgZXhjbHVkZWQpIHtcbiAgaWYgKHNvdXJjZSA9PSBudWxsKSByZXR1cm4ge307XG4gIHZhciB0YXJnZXQgPSB7fTtcbiAgdmFyIHNvdXJjZUtleXMgPSBPYmplY3Qua2V5cyhzb3VyY2UpO1xuICB2YXIga2V5LCBpO1xuXG4gIGZvciAoaSA9IDA7IGkgPCBzb3VyY2VLZXlzLmxlbmd0aDsgaSsrKSB7XG4gICAga2V5ID0gc291cmNlS2V5c1tpXTtcbiAgICBpZiAoZXhjbHVkZWQuaW5kZXhPZihrZXkpID49IDApIGNvbnRpbnVlO1xuICAgIHRhcmdldFtrZXldID0gc291cmNlW2tleV07XG4gIH1cblxuICByZXR1cm4gdGFyZ2V0O1xufVxuXG52YXIgY2hlY2tJc1N0cmluZyA9IC8qI19fUFVSRV9fKi9nZXRSZWZpbmVtZW50KGZ1bmN0aW9uICh2YWx1ZSkge1xuICByZXR1cm4gdHlwZW9mIHZhbHVlID09PSAnc3RyaW5nJyA/IHZhbHVlIDogbnVsbDtcbn0pO1xudmFyIGlzRGVmaW5lZCA9IGZ1bmN0aW9uIGlzRGVmaW5lZCh4KSB7XG4gIHJldHVybiB4ICE9PSBudWxsICYmIHggIT09IHVuZGVmaW5lZDtcbn07XG5mdW5jdGlvbiBnZXRSZWZpbmVtZW50KGdldEIpIHtcbiAgcmV0dXJuIGZ1bmN0aW9uIChhKSB7XG4gICAgcmV0dXJuIGlzRGVmaW5lZChnZXRCKGEpKTtcbiAgfTtcbn1cbnZhciBjaGVja0lzTm9uRW1wdHlBcnJheSA9IGZ1bmN0aW9uIGNoZWNrSXNOb25FbXB0eUFycmF5KGEpIHtcbiAgcmV0dXJuIGEubGVuZ3RoID4gMDtcbn07XG5cbi8qKiBUYWtlcyBhIGRpY3Rpb25hcnkgY29udGFpbmluZyBudWxsaXNoIHZhbHVlcyBhbmQgcmV0dXJucyBhIGRpY3Rpb25hcnkgb2YgYWxsIHRoZSBkZWZpbmVkXHJcbiAqIChub24tbnVsbGlzaCkgdmFsdWVzLlxyXG4gKi9cblxudmFyIGNvbXBhY3REZWZpbmVkID0gZnVuY3Rpb24gY29tcGFjdERlZmluZWQob2JqKSB7XG4gIHJldHVybiBPYmplY3Qua2V5cyhvYmopLnJlZHVjZShmdW5jdGlvbiAoYWNjLCBrZXkpIHtcbiAgICB2YXIgX3JlZjtcblxuICAgIHZhciB2YWx1ZSA9IG9ialtrZXldO1xuICAgIHJldHVybiBfZXh0ZW5kcyh7fSwgYWNjLCBpc0RlZmluZWQodmFsdWUpID8gKF9yZWYgPSB7fSwgX3JlZltrZXldID0gdmFsdWUsIF9yZWYpIDoge30pO1xuICB9LCB7fSk7XG59O1xuZnVuY3Rpb24gZmxvdygpIHtcbiAgZm9yICh2YXIgX2xlbiA9IGFyZ3VtZW50cy5sZW5ndGgsIGZucyA9IG5ldyBBcnJheShfbGVuKSwgX2tleSA9IDA7IF9rZXkgPCBfbGVuOyBfa2V5KyspIHtcbiAgICBmbnNbX2tleV0gPSBhcmd1bWVudHNbX2tleV07XG4gIH1cblxuICB2YXIgbGVuID0gZm5zLmxlbmd0aCAtIDE7XG4gIHJldHVybiBmdW5jdGlvbiAoKSB7XG4gICAgZm9yICh2YXIgX2xlbjIgPSBhcmd1bWVudHMubGVuZ3RoLCB4ID0gbmV3IEFycmF5KF9sZW4yKSwgX2tleTIgPSAwOyBfa2V5MiA8IF9sZW4yOyBfa2V5MisrKSB7XG4gICAgICB4W19rZXkyXSA9IGFyZ3VtZW50c1tfa2V5Ml07XG4gICAgfVxuXG4gICAgdmFyIHkgPSBmbnNbMF0uYXBwbHkodGhpcywgeCk7XG5cbiAgICBmb3IgKHZhciBpID0gMTsgaSA8PSBsZW47IGkrKykge1xuICAgICAgeSA9IGZuc1tpXS5jYWxsKHRoaXMsIHkpO1xuICAgIH1cblxuICAgIHJldHVybiB5O1xuICB9O1xufVxuXG52YXIgY2hlY2tJc09iamVjdCA9IC8qI19fUFVSRV9fKi9nZXRSZWZpbmVtZW50KGZ1bmN0aW9uIChyZXNwb25zZSkge1xuICByZXR1cm4gaXNEZWZpbmVkKHJlc3BvbnNlKSAmJiB0eXBlb2YgcmVzcG9uc2UgPT09ICdvYmplY3QnICYmICFBcnJheS5pc0FycmF5KHJlc3BvbnNlKSA/IHJlc3BvbnNlIDogbnVsbDtcbn0pO1xudmFyIGNoZWNrSXNFcnJvcnMgPSAvKiNfX1BVUkVfXyovZ2V0UmVmaW5lbWVudChmdW5jdGlvbiAoZXJyb3JzKSB7XG4gIHJldHVybiBBcnJheS5pc0FycmF5KGVycm9ycykgJiYgZXJyb3JzLmV2ZXJ5KGNoZWNrSXNTdHJpbmcpICYmIGNoZWNrSXNOb25FbXB0eUFycmF5KGVycm9ycykgPyBlcnJvcnMgOiBudWxsO1xufSk7XG52YXIgY2hlY2tJc0FwaUVycm9yID0gLyojX19QVVJFX18qL2dldFJlZmluZW1lbnQoZnVuY3Rpb24gKHJlc3BvbnNlKSB7XG4gIHJldHVybiBjaGVja0lzT2JqZWN0KHJlc3BvbnNlKSAmJiAnZXJyb3JzJyBpbiByZXNwb25zZSAmJiBjaGVja0lzRXJyb3JzKHJlc3BvbnNlLmVycm9ycykgPyB7XG4gICAgZXJyb3JzOiByZXNwb25zZS5lcnJvcnNcbiAgfSA6IG51bGw7XG59KTtcbnZhciBnZXRFcnJvckZvckJhZFN0YXR1c0NvZGUgPSBmdW5jdGlvbiBnZXRFcnJvckZvckJhZFN0YXR1c0NvZGUoanNvblJlc3BvbnNlKSB7XG4gIGlmIChjaGVja0lzQXBpRXJyb3IoanNvblJlc3BvbnNlKSkge1xuICAgIHJldHVybiB7XG4gICAgICBlcnJvcnM6IGpzb25SZXNwb25zZS5lcnJvcnMsXG4gICAgICBzb3VyY2U6ICdhcGknXG4gICAgfTtcbiAgfSBlbHNlIHtcbiAgICByZXR1cm4ge1xuICAgICAgZXJyb3JzOiBbJ1Jlc3BvbmRlZCB3aXRoIGEgc3RhdHVzIGNvZGUgb3V0c2lkZSB0aGUgMnh4IHJhbmdlLCBhbmQgdGhlIHJlc3BvbnNlIGJvZHkgaXMgbm90IHJlY29nbmlzYWJsZS4nXSxcbiAgICAgIHNvdXJjZTogJ2RlY29kaW5nJ1xuICAgIH07XG4gIH1cbn07XG52YXIgRGVjb2RpbmdFcnJvciA9IGZ1bmN0aW9uIERlY29kaW5nRXJyb3IobWVzc2FnZSkge1xuICB0aGlzLm1lc3NhZ2UgPSBtZXNzYWdlO1xufTtcblxudmFyIGlzSlNPTiA9IGZ1bmN0aW9uIGlzSlNPTihjb250ZW50VHlwZSkge1xuICByZXR1cm4gL2FwcGxpY2F0aW9uXFwvW14rXSpbK10/KGpzb24pOz8uKi8udGVzdChjb250ZW50VHlwZSk7XG59O1xuXG52YXIgY2hlY2tJc0pzb25SZXNwb25zZSA9IGZ1bmN0aW9uIGNoZWNrSXNKc29uUmVzcG9uc2UocmVzcG9uc2UpIHtcbiAgdmFyIGNvbnRlbnRUeXBlSGVhZGVyID0gcmVzcG9uc2UuaGVhZGVycy5nZXQoJ2NvbnRlbnQtdHlwZScpO1xuICByZXR1cm4gaXNEZWZpbmVkKGNvbnRlbnRUeXBlSGVhZGVyKSAmJiBpc0pTT04oY29udGVudFR5cGVIZWFkZXIpO1xufTtcbi8qKlxyXG4gKiBOb3RlOiByZXN0cmljdCB0aGUgdHlwZSBvZiBKU09OIHRvIGBBbnlKc29uYCBzbyB0aGF0IGBhbnlgIGRvZXNuJ3QgbGVhayBkb3duc3RyZWFtLlxyXG4gKi9cblxuXG52YXIgZ2V0SnNvblJlc3BvbnNlID0gZnVuY3Rpb24gZ2V0SnNvblJlc3BvbnNlKHJlc3BvbnNlKSB7XG4gIGlmIChjaGVja0lzSnNvblJlc3BvbnNlKHJlc3BvbnNlKSkge1xuICAgIHJldHVybiByZXNwb25zZS5qc29uKClbXCJjYXRjaFwiXShmdW5jdGlvbiAoX2Vycikge1xuICAgICAgdGhyb3cgbmV3IERlY29kaW5nRXJyb3IoJ3VuYWJsZSB0byBwYXJzZSBKU09OIHJlc3BvbnNlLicpO1xuICAgIH0pO1xuICB9IGVsc2Uge1xuICAgIHRocm93IG5ldyBEZWNvZGluZ0Vycm9yKCdleHBlY3RlZCBKU09OIHJlc3BvbnNlIGZyb20gc2VydmVyLicpO1xuICB9XG59O1xuXG52YXIgaGFuZGxlRmV0Y2hSZXNwb25zZSA9IGZ1bmN0aW9uIGhhbmRsZUZldGNoUmVzcG9uc2UoaGFuZGxlUmVzcG9uc2UpIHtcbiAgcmV0dXJuIGZ1bmN0aW9uIChyZXNwb25zZSkge1xuICAgIHJldHVybiAocmVzcG9uc2Uub2sgPyBoYW5kbGVSZXNwb25zZSh7XG4gICAgICByZXNwb25zZTogcmVzcG9uc2VcbiAgICB9KS50aGVuKGZ1bmN0aW9uIChoYW5kbGVkUmVzcG9uc2UpIHtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIHR5cGU6ICdzdWNjZXNzJyxcbiAgICAgICAgc3RhdHVzOiByZXNwb25zZS5zdGF0dXMsXG4gICAgICAgIHJlc3BvbnNlOiBoYW5kbGVkUmVzcG9uc2UsXG4gICAgICAgIG9yaWdpbmFsUmVzcG9uc2U6IHJlc3BvbnNlXG4gICAgICB9O1xuICAgIH0pIDogZ2V0SnNvblJlc3BvbnNlKHJlc3BvbnNlKS50aGVuKGZ1bmN0aW9uIChqc29uUmVzcG9uc2UpIHtcbiAgICAgIHJldHVybiBfZXh0ZW5kcyh7XG4gICAgICAgIHR5cGU6ICdlcnJvcicsXG4gICAgICAgIHN0YXR1czogcmVzcG9uc2Uuc3RhdHVzXG4gICAgICB9LCBnZXRFcnJvckZvckJhZFN0YXR1c0NvZGUoanNvblJlc3BvbnNlKSwge1xuICAgICAgICBvcmlnaW5hbFJlc3BvbnNlOiByZXNwb25zZVxuICAgICAgfSk7XG4gICAgfSkpW1wiY2F0Y2hcIl0oZnVuY3Rpb24gKGVycm9yKSB7XG4gICAgICAvKipcclxuICAgICAgICogV2Ugd2FudCB0byBzZXBhcmF0ZSBleHBlY3RlZCBkZWNvZGluZyBlcnJvcnMgZnJvbSB1bmtub3duIG9uZXMuIFdlIGRvIHNvIGJ5IHRocm93aW5nIGEgY3VzdG9tXHJcbiAgICAgICAqIGBEZWNvZGluZ0Vycm9yYCB3aGVuZXZlciB3ZSBlbmNvdW50ZXIgb25lIHdpdGhpbiBgaGFuZGxlRmV0Y2hSZXNwb25zZWAgYW5kIGNhdGNoIHRoZW0gYWxsXHJcbiAgICAgICAqIGhlcmUuIFRoaXMgYWxsb3dzIHVzIHRvIGVhc2lseSBoYW5kbGUgYWxsIG9mIHRoZXNlIGVycm9ycyBhdCBvbmNlLiBVbmV4cGVjdGVkIGVycm9ycyBhcmUgbm90XHJcbiAgICAgICAqIGNhdWdodCwgc28gdGhhdCB0aGV5IGJ1YmJsZSB1cCBhbmQgZmFpbCBsb3VkbHkuXHJcbiAgICAgICAqXHJcbiAgICAgICAqIE5vdGU6IElkZWFsbHkgd2UnZCB1c2UgYW4gRWl0aGVyIHR5cGUsIGJ1dCB0aGlzIGRvZXMgdGhlIGpvYiB3aXRob3V0IGludHJvZHVjaW5nIGRlcGVuZGVuY2llc1xyXG4gICAgICAgKiBsaWtlIGBmcC10c2AuXHJcbiAgICAgICAqL1xuICAgICAgaWYgKGVycm9yIGluc3RhbmNlb2YgRGVjb2RpbmdFcnJvcikge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIHR5cGU6ICdlcnJvcicsXG4gICAgICAgICAgc291cmNlOiAnZGVjb2RpbmcnLFxuICAgICAgICAgIHN0YXR1czogcmVzcG9uc2Uuc3RhdHVzLFxuICAgICAgICAgIG9yaWdpbmFsUmVzcG9uc2U6IHJlc3BvbnNlLFxuICAgICAgICAgIGVycm9yczogW2Vycm9yLm1lc3NhZ2VdXG4gICAgICAgIH07XG4gICAgICB9IGVsc2Uge1xuICAgICAgICB0aHJvdyBlcnJvcjtcbiAgICAgIH1cbiAgICB9KTtcbiAgfTtcbn07XG52YXIgY2FzdFJlc3BvbnNlID0gZnVuY3Rpb24gY2FzdFJlc3BvbnNlKCkge1xuICByZXR1cm4gZnVuY3Rpb24gKF9yZWYpIHtcbiAgICB2YXIgcmVzcG9uc2UgPSBfcmVmLnJlc3BvbnNlO1xuICAgIHJldHVybiBnZXRKc29uUmVzcG9uc2UocmVzcG9uc2UpO1xuICB9O1xufTtcblxudmFyIGFkZFF1ZXJ5VG9VcmwgPSBmdW5jdGlvbiBhZGRRdWVyeVRvVXJsKHF1ZXJ5KSB7XG4gIHJldHVybiBmdW5jdGlvbiAodXJsKSB7XG4gICAgT2JqZWN0LmtleXMocXVlcnkpLmZvckVhY2goZnVuY3Rpb24gKHF1ZXJ5S2V5KSB7XG4gICAgICByZXR1cm4gdXJsLnNlYXJjaFBhcmFtcy5zZXQocXVlcnlLZXksIHF1ZXJ5W3F1ZXJ5S2V5XS50b1N0cmluZygpKTtcbiAgICB9KTtcbiAgfTtcbn07XG5cbnZhciBhZGRQYXRobmFtZVRvVXJsID0gZnVuY3Rpb24gYWRkUGF0aG5hbWVUb1VybChwYXRobmFtZSkge1xuICByZXR1cm4gZnVuY3Rpb24gKHVybCkge1xuICAgIC8vIFdoZW4gdGhlcmUgaXMgbm8gZXhpc3RpbmcgcGF0aG5hbWUsIHRoZSB2YWx1ZSBpcyBgL2AuIEFwcGVuZGluZyB3b3VsZCBnaXZlIHVzIGEgVVJMIHdpdGggdHdvXG4gICAgLy8gZm9yd2FyZCBzbGFzaGVzLiBUaGlzIGlzIHdoeSB3ZSByZXBsYWNlIHRoZSB2YWx1ZSBpbiB0aGF0IHNjZW5hcmlvLlxuICAgIGlmICh1cmwucGF0aG5hbWUgPT09ICcvJykge1xuICAgICAgdXJsLnBhdGhuYW1lID0gcGF0aG5hbWU7XG4gICAgfSBlbHNlIHtcbiAgICAgIHVybC5wYXRobmFtZSArPSBwYXRobmFtZTtcbiAgICB9XG4gIH07XG59O1xuXG52YXIgYnVpbGRVcmwgPSBmdW5jdGlvbiBidWlsZFVybChfcmVmKSB7XG4gIHZhciBwYXRobmFtZSA9IF9yZWYucGF0aG5hbWUsXG4gICAgICBxdWVyeSA9IF9yZWYucXVlcnk7XG4gIHJldHVybiBmdW5jdGlvbiAoYXBpVXJsKSB7XG4gICAgdmFyIHVybCA9IG5ldyBVUkwoYXBpVXJsKTtcbiAgICBhZGRQYXRobmFtZVRvVXJsKHBhdGhuYW1lKSh1cmwpO1xuICAgIGFkZFF1ZXJ5VG9VcmwocXVlcnkpKHVybCk7XG4gICAgcmV0dXJuIHVybC50b1N0cmluZygpO1xuICB9O1xufTtcblxudmFyIGdldFF1ZXJ5RnJvbVNlYXJjaFBhcmFtcyA9IGZ1bmN0aW9uIGdldFF1ZXJ5RnJvbVNlYXJjaFBhcmFtcyhzZWFyY2hQYXJhbXMpIHtcbiAgdmFyIHF1ZXJ5ID0ge307XG4gIHNlYXJjaFBhcmFtcy5mb3JFYWNoKGZ1bmN0aW9uICh2YWx1ZSwga2V5KSB7XG4gICAgcXVlcnlba2V5XSA9IHZhbHVlO1xuICB9KTtcbiAgcmV0dXJuIHF1ZXJ5O1xufTtcblxudmFyIHBhcnNlUXVlcnlBbmRQYXRobmFtZSA9IGZ1bmN0aW9uIHBhcnNlUXVlcnlBbmRQYXRobmFtZSh1cmwpIHtcbiAgdmFyIF9VUkwgPSBuZXcgVVJMKHVybCksXG4gICAgICBwYXRobmFtZSA9IF9VUkwucGF0aG5hbWUsXG4gICAgICBzZWFyY2hQYXJhbXMgPSBfVVJMLnNlYXJjaFBhcmFtcztcblxuICB2YXIgcXVlcnkgPSBnZXRRdWVyeUZyb21TZWFyY2hQYXJhbXMoc2VhcmNoUGFyYW1zKTtcbiAgcmV0dXJuIHtcbiAgICBxdWVyeTogcXVlcnksXG4gICAgcGF0aG5hbWU6IHBhdGhuYW1lID09PSAnLycgPyB1bmRlZmluZWQgOiBwYXRobmFtZVxuICB9O1xufTtcblxuLyoqXHJcbiAqIGhlbHBlciB1c2VkIHRvIHR5cGUtY2hlY2sgdGhlIGFyZ3VtZW50cywgYW5kIGFkZCBkZWZhdWx0IHBhcmFtcyBmb3IgYWxsIHJlcXVlc3RzXHJcbiAqL1xuXG52YXIgY3JlYXRlUmVxdWVzdEhhbmRsZXIgPSBmdW5jdGlvbiBjcmVhdGVSZXF1ZXN0SGFuZGxlcihmbikge1xuICByZXR1cm4gZnVuY3Rpb24gKGEsIGFkZGl0aW9uYWxGZXRjaE9wdGlvbnMpIHtcbiAgICBpZiAoYWRkaXRpb25hbEZldGNoT3B0aW9ucyA9PT0gdm9pZCAwKSB7XG4gICAgICBhZGRpdGlvbmFsRmV0Y2hPcHRpb25zID0ge307XG4gICAgfVxuXG4gICAgdmFyIF9mbiA9IGZuKGEpLFxuICAgICAgICBoZWFkZXJzID0gX2ZuLmhlYWRlcnMsXG4gICAgICAgIHF1ZXJ5ID0gX2ZuLnF1ZXJ5LFxuICAgICAgICBiYXNlUmVxUGFyYW1zID0gX29iamVjdFdpdGhvdXRQcm9wZXJ0aWVzTG9vc2UoX2ZuLCBbXCJoZWFkZXJzXCIsIFwicXVlcnlcIl0pO1xuXG4gICAgcmV0dXJuIF9leHRlbmRzKHt9LCBiYXNlUmVxUGFyYW1zLCBhZGRpdGlvbmFsRmV0Y2hPcHRpb25zLCB7XG4gICAgICBxdWVyeTogcXVlcnksXG4gICAgICBoZWFkZXJzOiBfZXh0ZW5kcyh7fSwgaGVhZGVycywgYWRkaXRpb25hbEZldGNoT3B0aW9ucy5oZWFkZXJzKVxuICAgIH0pO1xuICB9O1xufTtcbnZhciBtYWtlRW5kcG9pbnQgPSBmdW5jdGlvbiBtYWtlRW5kcG9pbnQoZW5kcG9pbnQpIHtcbiAgcmV0dXJuIGVuZHBvaW50O1xufTtcbnZhciBpbml0TWFrZVJlcXVlc3QgPSBmdW5jdGlvbiBpbml0TWFrZVJlcXVlc3QoX3JlZikge1xuICB2YXIgYWNjZXNzS2V5ID0gX3JlZi5hY2Nlc3NLZXksXG4gICAgICBfcmVmJGFwaVZlcnNpb24gPSBfcmVmLmFwaVZlcnNpb24sXG4gICAgICBhcGlWZXJzaW9uID0gX3JlZiRhcGlWZXJzaW9uID09PSB2b2lkIDAgPyAndjEnIDogX3JlZiRhcGlWZXJzaW9uLFxuICAgICAgX3JlZiRhcGlVcmwgPSBfcmVmLmFwaVVybCxcbiAgICAgIGFwaVVybCA9IF9yZWYkYXBpVXJsID09PSB2b2lkIDAgPyAnaHR0cHM6Ly9hcGkudW5zcGxhc2guY29tJyA6IF9yZWYkYXBpVXJsLFxuICAgICAgZ2VuZXJhbEhlYWRlcnMgPSBfcmVmLmhlYWRlcnMsXG4gICAgICBwcm92aWRlZEZldGNoID0gX3JlZi5mZXRjaCxcbiAgICAgIGdlbmVyYWxGZXRjaE9wdGlvbnMgPSBfb2JqZWN0V2l0aG91dFByb3BlcnRpZXNMb29zZShfcmVmLCBbXCJhY2Nlc3NLZXlcIiwgXCJhcGlWZXJzaW9uXCIsIFwiYXBpVXJsXCIsIFwiaGVhZGVyc1wiLCBcImZldGNoXCJdKTtcblxuICByZXR1cm4gZnVuY3Rpb24gKF9yZWYyKSB7XG4gICAgdmFyIGhhbmRsZVJlc3BvbnNlID0gX3JlZjIuaGFuZGxlUmVzcG9uc2UsXG4gICAgICAgIGhhbmRsZVJlcXVlc3QgPSBfcmVmMi5oYW5kbGVSZXF1ZXN0O1xuICAgIHJldHVybiBmbG93KGhhbmRsZVJlcXVlc3QsIGZ1bmN0aW9uIChfcmVmMykge1xuICAgICAgdmFyIHBhdGhuYW1lID0gX3JlZjMucGF0aG5hbWUsXG4gICAgICAgICAgcXVlcnkgPSBfcmVmMy5xdWVyeSxcbiAgICAgICAgICBfcmVmMyRtZXRob2QgPSBfcmVmMy5tZXRob2QsXG4gICAgICAgICAgbWV0aG9kID0gX3JlZjMkbWV0aG9kID09PSB2b2lkIDAgPyAnR0VUJyA6IF9yZWYzJG1ldGhvZCxcbiAgICAgICAgICBlbmRwb2ludEhlYWRlcnMgPSBfcmVmMy5oZWFkZXJzLFxuICAgICAgICAgIGJvZHkgPSBfcmVmMy5ib2R5LFxuICAgICAgICAgIHNpZ25hbCA9IF9yZWYzLnNpZ25hbDtcbiAgICAgIHZhciB1cmwgPSBidWlsZFVybCh7XG4gICAgICAgIHBhdGhuYW1lOiBwYXRobmFtZSxcbiAgICAgICAgcXVlcnk6IHF1ZXJ5XG4gICAgICB9KShhcGlVcmwpO1xuXG4gICAgICB2YXIgZmV0Y2hPcHRpb25zID0gX2V4dGVuZHMoe1xuICAgICAgICBtZXRob2Q6IG1ldGhvZCxcbiAgICAgICAgaGVhZGVyczogX2V4dGVuZHMoe30sIGdlbmVyYWxIZWFkZXJzLCBlbmRwb2ludEhlYWRlcnMsIHtcbiAgICAgICAgICAnQWNjZXB0LVZlcnNpb24nOiBhcGlWZXJzaW9uXG4gICAgICAgIH0sIGlzRGVmaW5lZChhY2Nlc3NLZXkpID8ge1xuICAgICAgICAgIEF1dGhvcml6YXRpb246IFwiQ2xpZW50LUlEIFwiICsgYWNjZXNzS2V5XG4gICAgICAgIH0gOiB7fSksXG4gICAgICAgIGJvZHk6IGJvZHksXG4gICAgICAgIHNpZ25hbDogc2lnbmFsXG4gICAgICB9LCBnZW5lcmFsRmV0Y2hPcHRpb25zKTtcblxuICAgICAgdmFyIGZldGNoVG9Vc2UgPSBwcm92aWRlZEZldGNoICE9IG51bGwgPyBwcm92aWRlZEZldGNoIDogZmV0Y2g7XG4gICAgICByZXR1cm4gZmV0Y2hUb1VzZSh1cmwsIGZldGNoT3B0aW9ucykudGhlbihoYW5kbGVGZXRjaFJlc3BvbnNlKGhhbmRsZVJlc3BvbnNlKSk7XG4gICAgfSk7XG4gIH07XG59O1xuXG52YXIgVE9UQUxfUkVTUE9OU0VfSEVBREVSID0gJ3gtdG90YWwnO1xuXG52YXIgZ2V0VG90YWxGcm9tQXBpRmVlZFJlc3BvbnNlID0gZnVuY3Rpb24gZ2V0VG90YWxGcm9tQXBpRmVlZFJlc3BvbnNlKHJlc3BvbnNlKSB7XG4gIHZhciB0b3RhbHNTdHIgPSByZXNwb25zZS5oZWFkZXJzLmdldChUT1RBTF9SRVNQT05TRV9IRUFERVIpO1xuXG4gIGlmIChpc0RlZmluZWQodG90YWxzU3RyKSkge1xuICAgIHZhciB0b3RhbCA9IHBhcnNlSW50KHRvdGFsc1N0cik7XG5cbiAgICBpZiAoTnVtYmVyLmlzSW50ZWdlcih0b3RhbCkpIHtcbiAgICAgIHJldHVybiB0b3RhbDtcbiAgICB9IGVsc2Uge1xuICAgICAgdGhyb3cgbmV3IERlY29kaW5nRXJyb3IoXCJleHBlY3RlZCBcIiArIFRPVEFMX1JFU1BPTlNFX0hFQURFUiArIFwiIGhlYWRlciB0byBiZSB2YWxpZCBpbnRlZ2VyLlwiKTtcbiAgICB9XG4gIH0gZWxzZSB7XG4gICAgdGhyb3cgbmV3IERlY29kaW5nRXJyb3IoXCJleHBlY3RlZCBcIiArIFRPVEFMX1JFU1BPTlNFX0hFQURFUiArIFwiIGhlYWRlciB0byBleGlzdC5cIik7XG4gIH1cbn07XG5cbnZhciBoYW5kbGVGZWVkUmVzcG9uc2UgPSBmdW5jdGlvbiBoYW5kbGVGZWVkUmVzcG9uc2UoKSB7XG4gIHJldHVybiBmdW5jdGlvbiAoX3JlZikge1xuICAgIHZhciByZXNwb25zZSA9IF9yZWYucmVzcG9uc2U7XG4gICAgcmV0dXJuIGNhc3RSZXNwb25zZSgpKHtcbiAgICAgIHJlc3BvbnNlOiByZXNwb25zZVxuICAgIH0pLnRoZW4oZnVuY3Rpb24gKHJlc3VsdHMpIHtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIHJlc3VsdHM6IHJlc3VsdHMsXG4gICAgICAgIHRvdGFsOiBnZXRUb3RhbEZyb21BcGlGZWVkUmVzcG9uc2UocmVzcG9uc2UpXG4gICAgICB9O1xuICAgIH0pO1xuICB9O1xufTtcblxudmFyIGdldENvbGxlY3Rpb25zID0gZnVuY3Rpb24gZ2V0Q29sbGVjdGlvbnMoY29sbGVjdGlvbklkcykge1xuICByZXR1cm4gaXNEZWZpbmVkKGNvbGxlY3Rpb25JZHMpID8ge1xuICAgIGNvbGxlY3Rpb25zOiBjb2xsZWN0aW9uSWRzLmpvaW4oKVxuICB9IDoge307XG59O1xudmFyIGdldFRvcGljcyA9IGZ1bmN0aW9uIGdldFRvcGljcyh0b3BpY0lkcykge1xuICByZXR1cm4gaXNEZWZpbmVkKHRvcGljSWRzKSA/IHtcbiAgICB0b3BpY3M6IHRvcGljSWRzLmpvaW4oKVxuICB9IDoge307XG59O1xudmFyIGdldEZlZWRQYXJhbXMgPSBmdW5jdGlvbiBnZXRGZWVkUGFyYW1zKF9yZWYpIHtcbiAgdmFyIHBhZ2UgPSBfcmVmLnBhZ2UsXG4gICAgICBwZXJQYWdlID0gX3JlZi5wZXJQYWdlLFxuICAgICAgb3JkZXJCeSA9IF9yZWYub3JkZXJCeTtcbiAgcmV0dXJuIGNvbXBhY3REZWZpbmVkKHtcbiAgICBwZXJfcGFnZTogcGVyUGFnZSxcbiAgICBvcmRlcl9ieTogb3JkZXJCeSxcbiAgICBwYWdlOiBwYWdlXG4gIH0pO1xufTtcblxudmFyIENPTExFQ1RJT05TX1BBVEhfUFJFRklYID0gJy9jb2xsZWN0aW9ucyc7XG52YXIgZ2V0UGhvdG9zID0gLyojX19QVVJFX18qL2Z1bmN0aW9uICgpIHtcbiAgdmFyIGdldFBhdGhuYW1lID0gZnVuY3Rpb24gZ2V0UGF0aG5hbWUoX3JlZikge1xuICAgIHZhciBjb2xsZWN0aW9uSWQgPSBfcmVmLmNvbGxlY3Rpb25JZDtcbiAgICByZXR1cm4gQ09MTEVDVElPTlNfUEFUSF9QUkVGSVggKyBcIi9cIiArIGNvbGxlY3Rpb25JZCArIFwiL3Bob3Rvc1wiO1xuICB9O1xuXG4gIHJldHVybiBtYWtlRW5kcG9pbnQoe1xuICAgIGdldFBhdGhuYW1lOiBnZXRQYXRobmFtZSxcbiAgICBoYW5kbGVSZXF1ZXN0OiBjcmVhdGVSZXF1ZXN0SGFuZGxlcihmdW5jdGlvbiAoX3JlZjIpIHtcbiAgICAgIHZhciBjb2xsZWN0aW9uSWQgPSBfcmVmMi5jb2xsZWN0aW9uSWQsXG4gICAgICAgICAgb3JpZW50YXRpb24gPSBfcmVmMi5vcmllbnRhdGlvbixcbiAgICAgICAgICBwYWdpbmF0aW9uUGFyYW1zID0gX29iamVjdFdpdGhvdXRQcm9wZXJ0aWVzTG9vc2UoX3JlZjIsIFtcImNvbGxlY3Rpb25JZFwiLCBcIm9yaWVudGF0aW9uXCJdKTtcblxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgcGF0aG5hbWU6IGdldFBhdGhuYW1lKHtcbiAgICAgICAgICBjb2xsZWN0aW9uSWQ6IGNvbGxlY3Rpb25JZFxuICAgICAgICB9KSxcbiAgICAgICAgcXVlcnk6IGNvbXBhY3REZWZpbmVkKF9leHRlbmRzKHt9LCBnZXRGZWVkUGFyYW1zKHBhZ2luYXRpb25QYXJhbXMpLCB7XG4gICAgICAgICAgb3JpZW50YXRpb246IG9yaWVudGF0aW9uXG4gICAgICAgIH0pKVxuICAgICAgfTtcbiAgICB9KSxcbiAgICBoYW5kbGVSZXNwb25zZTogaGFuZGxlRmVlZFJlc3BvbnNlKClcbiAgfSk7XG59KCk7XG52YXIgZ2V0ID0gLyojX19QVVJFX18qL2Z1bmN0aW9uICgpIHtcbiAgdmFyIGdldFBhdGhuYW1lID0gZnVuY3Rpb24gZ2V0UGF0aG5hbWUoX3JlZjMpIHtcbiAgICB2YXIgY29sbGVjdGlvbklkID0gX3JlZjMuY29sbGVjdGlvbklkO1xuICAgIHJldHVybiBDT0xMRUNUSU9OU19QQVRIX1BSRUZJWCArIFwiL1wiICsgY29sbGVjdGlvbklkO1xuICB9O1xuXG4gIHJldHVybiBtYWtlRW5kcG9pbnQoe1xuICAgIGdldFBhdGhuYW1lOiBnZXRQYXRobmFtZSxcbiAgICBoYW5kbGVSZXF1ZXN0OiBjcmVhdGVSZXF1ZXN0SGFuZGxlcihmdW5jdGlvbiAoX3JlZjQpIHtcbiAgICAgIHZhciBjb2xsZWN0aW9uSWQgPSBfcmVmNC5jb2xsZWN0aW9uSWQ7XG4gICAgICByZXR1cm4ge1xuICAgICAgICBwYXRobmFtZTogZ2V0UGF0aG5hbWUoe1xuICAgICAgICAgIGNvbGxlY3Rpb25JZDogY29sbGVjdGlvbklkXG4gICAgICAgIH0pLFxuICAgICAgICBxdWVyeToge31cbiAgICAgIH07XG4gICAgfSksXG4gICAgaGFuZGxlUmVzcG9uc2U6IGNhc3RSZXNwb25zZSgpXG4gIH0pO1xufSgpO1xudmFyIGxpc3QgPSAvKiNfX1BVUkVfXyovZnVuY3Rpb24gKCkge1xuICB2YXIgZ2V0UGF0aG5hbWUgPSBmdW5jdGlvbiBnZXRQYXRobmFtZSgpIHtcbiAgICByZXR1cm4gQ09MTEVDVElPTlNfUEFUSF9QUkVGSVg7XG4gIH07XG5cbiAgcmV0dXJuIG1ha2VFbmRwb2ludCh7XG4gICAgZ2V0UGF0aG5hbWU6IGdldFBhdGhuYW1lLFxuICAgIGhhbmRsZVJlcXVlc3Q6IGNyZWF0ZVJlcXVlc3RIYW5kbGVyKGZ1bmN0aW9uIChwYWdpbmF0aW9uUGFyYW1zKSB7XG4gICAgICBpZiAocGFnaW5hdGlvblBhcmFtcyA9PT0gdm9pZCAwKSB7XG4gICAgICAgIHBhZ2luYXRpb25QYXJhbXMgPSB7fTtcbiAgICAgIH1cblxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgcGF0aG5hbWU6IGdldFBhdGhuYW1lKCksXG4gICAgICAgIHF1ZXJ5OiBnZXRGZWVkUGFyYW1zKHBhZ2luYXRpb25QYXJhbXMpXG4gICAgICB9O1xuICAgIH0pLFxuICAgIGhhbmRsZVJlc3BvbnNlOiBoYW5kbGVGZWVkUmVzcG9uc2UoKVxuICB9KTtcbn0oKTtcbnZhciBnZXRSZWxhdGVkID0gLyojX19QVVJFX18qL2Z1bmN0aW9uICgpIHtcbiAgdmFyIGdldFBhdGhuYW1lID0gZnVuY3Rpb24gZ2V0UGF0aG5hbWUoX3JlZjUpIHtcbiAgICB2YXIgY29sbGVjdGlvbklkID0gX3JlZjUuY29sbGVjdGlvbklkO1xuICAgIHJldHVybiBDT0xMRUNUSU9OU19QQVRIX1BSRUZJWCArIFwiL1wiICsgY29sbGVjdGlvbklkICsgXCIvcmVsYXRlZFwiO1xuICB9O1xuXG4gIHJldHVybiBtYWtlRW5kcG9pbnQoe1xuICAgIGdldFBhdGhuYW1lOiBnZXRQYXRobmFtZSxcbiAgICBoYW5kbGVSZXF1ZXN0OiBjcmVhdGVSZXF1ZXN0SGFuZGxlcihmdW5jdGlvbiAoX3JlZjYpIHtcbiAgICAgIHZhciBjb2xsZWN0aW9uSWQgPSBfcmVmNi5jb2xsZWN0aW9uSWQ7XG4gICAgICByZXR1cm4ge1xuICAgICAgICBwYXRobmFtZTogZ2V0UGF0aG5hbWUoe1xuICAgICAgICAgIGNvbGxlY3Rpb25JZDogY29sbGVjdGlvbklkXG4gICAgICAgIH0pLFxuICAgICAgICBxdWVyeToge31cbiAgICAgIH07XG4gICAgfSksXG4gICAgaGFuZGxlUmVzcG9uc2U6IGNhc3RSZXNwb25zZSgpXG4gIH0pO1xufSgpO1xuXG52YXIgaW5kZXggPSB7XG4gIF9fcHJvdG9fXzogbnVsbCxcbiAgZ2V0UGhvdG9zOiBnZXRQaG90b3MsXG4gIGdldDogZ2V0LFxuICBsaXN0OiBsaXN0LFxuICBnZXRSZWxhdGVkOiBnZXRSZWxhdGVkXG59O1xuXG52YXIgUEhPVE9TX1BBVEhfUFJFRklYID0gJy9waG90b3MnO1xudmFyIGxpc3QkMSA9IC8qI19fUFVSRV9fKi9mdW5jdGlvbiAoKSB7XG4gIHZhciBfZ2V0UGF0aG5hbWUgPSBmdW5jdGlvbiBnZXRQYXRobmFtZSgpIHtcbiAgICByZXR1cm4gUEhPVE9TX1BBVEhfUFJFRklYO1xuICB9O1xuXG4gIHJldHVybiBtYWtlRW5kcG9pbnQoe1xuICAgIC8vIFdyYXBwZXIgdXNlcyB0eXBlIHRyaWNrIHRvIGFsbG93IDAgYXJnc1xuICAgIGdldFBhdGhuYW1lOiBmdW5jdGlvbiBnZXRQYXRobmFtZShfcGFyYW1zKSB7XG4gICAgICByZXR1cm4gX2dldFBhdGhuYW1lKCk7XG4gICAgfSxcbiAgICBoYW5kbGVSZXF1ZXN0OiBjcmVhdGVSZXF1ZXN0SGFuZGxlcihmdW5jdGlvbiAoZmVlZFBhcmFtcykge1xuICAgICAgaWYgKGZlZWRQYXJhbXMgPT09IHZvaWQgMCkge1xuICAgICAgICBmZWVkUGFyYW1zID0ge307XG4gICAgICB9XG5cbiAgICAgIHJldHVybiB7XG4gICAgICAgIHBhdGhuYW1lOiBQSE9UT1NfUEFUSF9QUkVGSVgsXG4gICAgICAgIHF1ZXJ5OiBjb21wYWN0RGVmaW5lZChnZXRGZWVkUGFyYW1zKGZlZWRQYXJhbXMpKVxuICAgICAgfTtcbiAgICB9KSxcbiAgICBoYW5kbGVSZXNwb25zZTogaGFuZGxlRmVlZFJlc3BvbnNlKClcbiAgfSk7XG59KCk7XG52YXIgZ2V0JDEgPSAvKiNfX1BVUkVfXyovZnVuY3Rpb24gKCkge1xuICB2YXIgZ2V0UGF0aG5hbWUgPSBmdW5jdGlvbiBnZXRQYXRobmFtZShfcmVmKSB7XG4gICAgdmFyIHBob3RvSWQgPSBfcmVmLnBob3RvSWQ7XG4gICAgcmV0dXJuIFBIT1RPU19QQVRIX1BSRUZJWCArIFwiL1wiICsgcGhvdG9JZDtcbiAgfTtcblxuICByZXR1cm4gbWFrZUVuZHBvaW50KHtcbiAgICBnZXRQYXRobmFtZTogZ2V0UGF0aG5hbWUsXG4gICAgaGFuZGxlUmVxdWVzdDogY3JlYXRlUmVxdWVzdEhhbmRsZXIoZnVuY3Rpb24gKF9yZWYyKSB7XG4gICAgICB2YXIgcGhvdG9JZCA9IF9yZWYyLnBob3RvSWQ7XG4gICAgICByZXR1cm4ge1xuICAgICAgICBwYXRobmFtZTogZ2V0UGF0aG5hbWUoe1xuICAgICAgICAgIHBob3RvSWQ6IHBob3RvSWRcbiAgICAgICAgfSksXG4gICAgICAgIHF1ZXJ5OiB7fVxuICAgICAgfTtcbiAgICB9KSxcbiAgICBoYW5kbGVSZXNwb25zZTogY2FzdFJlc3BvbnNlKClcbiAgfSk7XG59KCk7XG52YXIgZ2V0U3RhdHMgPSAvKiNfX1BVUkVfXyovZnVuY3Rpb24gKCkge1xuICB2YXIgZ2V0UGF0aG5hbWUgPSBmdW5jdGlvbiBnZXRQYXRobmFtZShfcmVmMykge1xuICAgIHZhciBwaG90b0lkID0gX3JlZjMucGhvdG9JZDtcbiAgICByZXR1cm4gUEhPVE9TX1BBVEhfUFJFRklYICsgXCIvXCIgKyBwaG90b0lkICsgXCIvc3RhdGlzdGljc1wiO1xuICB9O1xuXG4gIHJldHVybiBtYWtlRW5kcG9pbnQoe1xuICAgIGdldFBhdGhuYW1lOiBnZXRQYXRobmFtZSxcbiAgICBoYW5kbGVSZXF1ZXN0OiBjcmVhdGVSZXF1ZXN0SGFuZGxlcihmdW5jdGlvbiAoX3JlZjQpIHtcbiAgICAgIHZhciBwaG90b0lkID0gX3JlZjQucGhvdG9JZDtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIHBhdGhuYW1lOiBnZXRQYXRobmFtZSh7XG4gICAgICAgICAgcGhvdG9JZDogcGhvdG9JZFxuICAgICAgICB9KSxcbiAgICAgICAgcXVlcnk6IHt9XG4gICAgICB9O1xuICAgIH0pLFxuICAgIGhhbmRsZVJlc3BvbnNlOiBjYXN0UmVzcG9uc2UoKVxuICB9KTtcbn0oKTtcbnZhciBnZXRSYW5kb20gPSAvKiNfX1BVUkVfXyovZnVuY3Rpb24gKCkge1xuICB2YXIgZ2V0UGF0aG5hbWUgPSBmdW5jdGlvbiBnZXRQYXRobmFtZSgpIHtcbiAgICByZXR1cm4gUEhPVE9TX1BBVEhfUFJFRklYICsgXCIvcmFuZG9tXCI7XG4gIH07XG5cbiAgcmV0dXJuIG1ha2VFbmRwb2ludCh7XG4gICAgZ2V0UGF0aG5hbWU6IGdldFBhdGhuYW1lLFxuICAgIGhhbmRsZVJlcXVlc3Q6IGNyZWF0ZVJlcXVlc3RIYW5kbGVyKGZ1bmN0aW9uIChfdGVtcCkge1xuICAgICAgdmFyIF9yZWY1ID0gX3RlbXAgPT09IHZvaWQgMCA/IHt9IDogX3RlbXAsXG4gICAgICAgICAgY29sbGVjdGlvbklkcyA9IF9yZWY1LmNvbGxlY3Rpb25JZHMsXG4gICAgICAgICAgY29udGVudEZpbHRlciA9IF9yZWY1LmNvbnRlbnRGaWx0ZXIsXG4gICAgICAgICAgdG9waWNJZHMgPSBfcmVmNS50b3BpY0lkcyxcbiAgICAgICAgICBxdWVyeVBhcmFtcyA9IF9vYmplY3RXaXRob3V0UHJvcGVydGllc0xvb3NlKF9yZWY1LCBbXCJjb2xsZWN0aW9uSWRzXCIsIFwiY29udGVudEZpbHRlclwiLCBcInRvcGljSWRzXCJdKTtcblxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgcGF0aG5hbWU6IGdldFBhdGhuYW1lKCksXG4gICAgICAgIHF1ZXJ5OiBjb21wYWN0RGVmaW5lZChfZXh0ZW5kcyh7fSwgcXVlcnlQYXJhbXMsIHtcbiAgICAgICAgICBjb250ZW50X2ZpbHRlcjogY29udGVudEZpbHRlclxuICAgICAgICB9LCBnZXRDb2xsZWN0aW9ucyhjb2xsZWN0aW9uSWRzKSwgZ2V0VG9waWNzKHRvcGljSWRzKSkpLFxuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgLyoqXHJcbiAgICAgICAgICAgKiBBdm9pZCByZXNwb25zZSBjYWNoaW5nXHJcbiAgICAgICAgICAgKi9cbiAgICAgICAgICAnY2FjaGUtY29udHJvbCc6ICduby1jYWNoZSdcbiAgICAgICAgfVxuICAgICAgfTtcbiAgICB9KSxcbiAgICBoYW5kbGVSZXNwb25zZTogY2FzdFJlc3BvbnNlKClcbiAgfSk7XG59KCk7XG52YXIgdHJhY2tEb3dubG9hZCA9IHtcbiAgaGFuZGxlUmVxdWVzdDogLyojX19QVVJFX18qL2NyZWF0ZVJlcXVlc3RIYW5kbGVyKGZ1bmN0aW9uIChfcmVmNikge1xuICAgIHZhciBkb3dubG9hZExvY2F0aW9uID0gX3JlZjYuZG93bmxvYWRMb2NhdGlvbjtcblxuICAgIHZhciBfcGFyc2VRdWVyeUFuZFBhdGhuYW0gPSBwYXJzZVF1ZXJ5QW5kUGF0aG5hbWUoZG93bmxvYWRMb2NhdGlvbiksXG4gICAgICAgIHBhdGhuYW1lID0gX3BhcnNlUXVlcnlBbmRQYXRobmFtLnBhdGhuYW1lLFxuICAgICAgICBxdWVyeSA9IF9wYXJzZVF1ZXJ5QW5kUGF0aG5hbS5xdWVyeTtcblxuICAgIGlmICghaXNEZWZpbmVkKHBhdGhuYW1lKSkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdDb3VsZCBub3QgcGFyc2UgcGF0aG5hbWUgZnJvbSB1cmwuJyk7XG4gICAgfVxuXG4gICAgcmV0dXJuIHtcbiAgICAgIHBhdGhuYW1lOiBwYXRobmFtZSxcbiAgICAgIHF1ZXJ5OiBjb21wYWN0RGVmaW5lZChxdWVyeSlcbiAgICB9O1xuICB9KSxcbiAgaGFuZGxlUmVzcG9uc2U6IC8qI19fUFVSRV9fKi9jYXN0UmVzcG9uc2UoKVxufTtcblxudmFyIGluZGV4JDEgPSB7XG4gIF9fcHJvdG9fXzogbnVsbCxcbiAgbGlzdDogbGlzdCQxLFxuICBnZXQ6IGdldCQxLFxuICBnZXRTdGF0czogZ2V0U3RhdHMsXG4gIGdldFJhbmRvbTogZ2V0UmFuZG9tLFxuICB0cmFja0Rvd25sb2FkOiB0cmFja0Rvd25sb2FkXG59O1xuXG52YXIgU0VBUkNIX1BBVEhfUFJFRklYID0gXCIvc2VhcmNoXCI7XG52YXIgZ2V0UGhvdG9zJDEgPSAvKiNfX1BVUkVfXyovZnVuY3Rpb24gKCkge1xuICB2YXIgX2dldFBhdGhuYW1lID0gZnVuY3Rpb24gZ2V0UGF0aG5hbWUoKSB7XG4gICAgcmV0dXJuIFNFQVJDSF9QQVRIX1BSRUZJWCArIFwiL3Bob3Rvc1wiO1xuICB9O1xuXG4gIHJldHVybiBtYWtlRW5kcG9pbnQoe1xuICAgIC8vIFdyYXBwZXIgdXNlcyB0eXBlIHRyaWNrIHRvIGFsbG93IDAgYXJnc1xuICAgIGdldFBhdGhuYW1lOiBmdW5jdGlvbiBnZXRQYXRobmFtZShfcGFyYW1zKSB7XG4gICAgICByZXR1cm4gX2dldFBhdGhuYW1lKCk7XG4gICAgfSxcbiAgICBoYW5kbGVSZXF1ZXN0OiBjcmVhdGVSZXF1ZXN0SGFuZGxlcihmdW5jdGlvbiAoX3JlZikge1xuICAgICAgdmFyIHF1ZXJ5ID0gX3JlZi5xdWVyeSxcbiAgICAgICAgICBwYWdlID0gX3JlZi5wYWdlLFxuICAgICAgICAgIHBlclBhZ2UgPSBfcmVmLnBlclBhZ2UsXG4gICAgICAgICAgb3JkZXJCeSA9IF9yZWYub3JkZXJCeSxcbiAgICAgICAgICBjb2xsZWN0aW9uSWRzID0gX3JlZi5jb2xsZWN0aW9uSWRzLFxuICAgICAgICAgIGxhbmcgPSBfcmVmLmxhbmcsXG4gICAgICAgICAgY29udGVudEZpbHRlciA9IF9yZWYuY29udGVudEZpbHRlcixcbiAgICAgICAgICBmaWx0ZXJzID0gX29iamVjdFdpdGhvdXRQcm9wZXJ0aWVzTG9vc2UoX3JlZiwgW1wicXVlcnlcIiwgXCJwYWdlXCIsIFwicGVyUGFnZVwiLCBcIm9yZGVyQnlcIiwgXCJjb2xsZWN0aW9uSWRzXCIsIFwibGFuZ1wiLCBcImNvbnRlbnRGaWx0ZXJcIl0pO1xuXG4gICAgICByZXR1cm4ge1xuICAgICAgICBwYXRobmFtZTogX2dldFBhdGhuYW1lKCksXG4gICAgICAgIHF1ZXJ5OiBjb21wYWN0RGVmaW5lZChfZXh0ZW5kcyh7XG4gICAgICAgICAgcXVlcnk6IHF1ZXJ5LFxuICAgICAgICAgIGNvbnRlbnRfZmlsdGVyOiBjb250ZW50RmlsdGVyLFxuICAgICAgICAgIGxhbmc6IGxhbmcsXG4gICAgICAgICAgb3JkZXJfYnk6IG9yZGVyQnlcbiAgICAgICAgfSwgZ2V0RmVlZFBhcmFtcyh7XG4gICAgICAgICAgcGFnZTogcGFnZSxcbiAgICAgICAgICBwZXJQYWdlOiBwZXJQYWdlXG4gICAgICAgIH0pLCBnZXRDb2xsZWN0aW9ucyhjb2xsZWN0aW9uSWRzKSwgZmlsdGVycykpXG4gICAgICB9O1xuICAgIH0pLFxuICAgIGhhbmRsZVJlc3BvbnNlOiBjYXN0UmVzcG9uc2UoKVxuICB9KTtcbn0oKTtcbnZhciBnZXRDb2xsZWN0aW9ucyQxID0gLyojX19QVVJFX18qL2Z1bmN0aW9uICgpIHtcbiAgdmFyIF9nZXRQYXRobmFtZTIgPSBmdW5jdGlvbiBnZXRQYXRobmFtZSgpIHtcbiAgICByZXR1cm4gU0VBUkNIX1BBVEhfUFJFRklYICsgXCIvY29sbGVjdGlvbnNcIjtcbiAgfTtcblxuICByZXR1cm4gbWFrZUVuZHBvaW50KHtcbiAgICAvLyBXcmFwcGVyIHVzZXMgdHlwZSB0cmljayB0byBhbGxvdyAwIGFyZ3NcbiAgICBnZXRQYXRobmFtZTogZnVuY3Rpb24gZ2V0UGF0aG5hbWUoX3BhcmFtcykge1xuICAgICAgcmV0dXJuIF9nZXRQYXRobmFtZTIoKTtcbiAgICB9LFxuICAgIGhhbmRsZVJlcXVlc3Q6IGNyZWF0ZVJlcXVlc3RIYW5kbGVyKGZ1bmN0aW9uIChfcmVmMikge1xuICAgICAgdmFyIHF1ZXJ5ID0gX3JlZjIucXVlcnksXG4gICAgICAgICAgcGFnaW5hdGlvblBhcmFtcyA9IF9vYmplY3RXaXRob3V0UHJvcGVydGllc0xvb3NlKF9yZWYyLCBbXCJxdWVyeVwiXSk7XG5cbiAgICAgIHJldHVybiB7XG4gICAgICAgIHBhdGhuYW1lOiBfZ2V0UGF0aG5hbWUyKCksXG4gICAgICAgIHF1ZXJ5OiBfZXh0ZW5kcyh7XG4gICAgICAgICAgcXVlcnk6IHF1ZXJ5XG4gICAgICAgIH0sIGdldEZlZWRQYXJhbXMocGFnaW5hdGlvblBhcmFtcykpXG4gICAgICB9O1xuICAgIH0pLFxuICAgIGhhbmRsZVJlc3BvbnNlOiBjYXN0UmVzcG9uc2UoKVxuICB9KTtcbn0oKTtcbnZhciBnZXRVc2VycyA9IC8qI19fUFVSRV9fKi9mdW5jdGlvbiAoKSB7XG4gIHZhciBfZ2V0UGF0aG5hbWUzID0gZnVuY3Rpb24gZ2V0UGF0aG5hbWUoKSB7XG4gICAgcmV0dXJuIFNFQVJDSF9QQVRIX1BSRUZJWCArIFwiL3VzZXJzXCI7XG4gIH07XG5cbiAgcmV0dXJuIG1ha2VFbmRwb2ludCh7XG4gICAgLy8gV3JhcHBlciB1c2VzIHR5cGUgdHJpY2sgdG8gYWxsb3cgMCBhcmdzXG4gICAgZ2V0UGF0aG5hbWU6IGZ1bmN0aW9uIGdldFBhdGhuYW1lKF9wYXJhbXMpIHtcbiAgICAgIHJldHVybiBfZ2V0UGF0aG5hbWUzKCk7XG4gICAgfSxcbiAgICBoYW5kbGVSZXF1ZXN0OiBjcmVhdGVSZXF1ZXN0SGFuZGxlcihmdW5jdGlvbiAoX3JlZjMpIHtcbiAgICAgIHZhciBxdWVyeSA9IF9yZWYzLnF1ZXJ5LFxuICAgICAgICAgIHBhZ2luYXRpb25QYXJhbXMgPSBfb2JqZWN0V2l0aG91dFByb3BlcnRpZXNMb29zZShfcmVmMywgW1wicXVlcnlcIl0pO1xuXG4gICAgICByZXR1cm4ge1xuICAgICAgICBwYXRobmFtZTogX2dldFBhdGhuYW1lMygpLFxuICAgICAgICBxdWVyeTogX2V4dGVuZHMoe1xuICAgICAgICAgIHF1ZXJ5OiBxdWVyeVxuICAgICAgICB9LCBnZXRGZWVkUGFyYW1zKHBhZ2luYXRpb25QYXJhbXMpKVxuICAgICAgfTtcbiAgICB9KSxcbiAgICBoYW5kbGVSZXNwb25zZTogY2FzdFJlc3BvbnNlKClcbiAgfSk7XG59KCk7XG5cbnZhciBpbmRleCQyID0ge1xuICBfX3Byb3RvX186IG51bGwsXG4gIGdldFBob3RvczogZ2V0UGhvdG9zJDEsXG4gIGdldENvbGxlY3Rpb25zOiBnZXRDb2xsZWN0aW9ucyQxLFxuICBnZXRVc2VyczogZ2V0VXNlcnNcbn07XG5cbnZhciBVU0VSU19QQVRIX1BSRUZJWCA9ICcvdXNlcnMnO1xudmFyIGdldCQyID0gLyojX19QVVJFX18qL2Z1bmN0aW9uICgpIHtcbiAgdmFyIGdldFBhdGhuYW1lID0gZnVuY3Rpb24gZ2V0UGF0aG5hbWUoX3JlZikge1xuICAgIHZhciB1c2VybmFtZSA9IF9yZWYudXNlcm5hbWU7XG4gICAgcmV0dXJuIFVTRVJTX1BBVEhfUFJFRklYICsgXCIvXCIgKyB1c2VybmFtZTtcbiAgfTtcblxuICByZXR1cm4gbWFrZUVuZHBvaW50KHtcbiAgICBnZXRQYXRobmFtZTogZ2V0UGF0aG5hbWUsXG4gICAgaGFuZGxlUmVxdWVzdDogY3JlYXRlUmVxdWVzdEhhbmRsZXIoZnVuY3Rpb24gKF9yZWYyKSB7XG4gICAgICB2YXIgdXNlcm5hbWUgPSBfcmVmMi51c2VybmFtZTtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIHBhdGhuYW1lOiBnZXRQYXRobmFtZSh7XG4gICAgICAgICAgdXNlcm5hbWU6IHVzZXJuYW1lXG4gICAgICAgIH0pLFxuICAgICAgICBxdWVyeToge31cbiAgICAgIH07XG4gICAgfSksXG4gICAgaGFuZGxlUmVzcG9uc2U6IGNhc3RSZXNwb25zZSgpXG4gIH0pO1xufSgpO1xudmFyIGdldFBob3RvcyQyID0gLyojX19QVVJFX18qL2Z1bmN0aW9uICgpIHtcbiAgdmFyIGdldFBhdGhuYW1lID0gZnVuY3Rpb24gZ2V0UGF0aG5hbWUoX3JlZjMpIHtcbiAgICB2YXIgdXNlcm5hbWUgPSBfcmVmMy51c2VybmFtZTtcbiAgICByZXR1cm4gVVNFUlNfUEFUSF9QUkVGSVggKyBcIi9cIiArIHVzZXJuYW1lICsgXCIvcGhvdG9zXCI7XG4gIH07XG5cbiAgcmV0dXJuIG1ha2VFbmRwb2ludCh7XG4gICAgZ2V0UGF0aG5hbWU6IGdldFBhdGhuYW1lLFxuICAgIGhhbmRsZVJlcXVlc3Q6IGNyZWF0ZVJlcXVlc3RIYW5kbGVyKGZ1bmN0aW9uIChfcmVmNCkge1xuICAgICAgdmFyIHVzZXJuYW1lID0gX3JlZjQudXNlcm5hbWUsXG4gICAgICAgICAgc3RhdHMgPSBfcmVmNC5zdGF0cyxcbiAgICAgICAgICBvcmllbnRhdGlvbiA9IF9yZWY0Lm9yaWVudGF0aW9uLFxuICAgICAgICAgIHBhZ2luYXRpb25QYXJhbXMgPSBfb2JqZWN0V2l0aG91dFByb3BlcnRpZXNMb29zZShfcmVmNCwgW1widXNlcm5hbWVcIiwgXCJzdGF0c1wiLCBcIm9yaWVudGF0aW9uXCJdKTtcblxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgcGF0aG5hbWU6IGdldFBhdGhuYW1lKHtcbiAgICAgICAgICB1c2VybmFtZTogdXNlcm5hbWVcbiAgICAgICAgfSksXG4gICAgICAgIHF1ZXJ5OiBjb21wYWN0RGVmaW5lZChfZXh0ZW5kcyh7fSwgZ2V0RmVlZFBhcmFtcyhwYWdpbmF0aW9uUGFyYW1zKSwge1xuICAgICAgICAgIG9yaWVudGF0aW9uOiBvcmllbnRhdGlvbixcbiAgICAgICAgICBzdGF0czogc3RhdHNcbiAgICAgICAgfSkpXG4gICAgICB9O1xuICAgIH0pLFxuICAgIGhhbmRsZVJlc3BvbnNlOiBoYW5kbGVGZWVkUmVzcG9uc2UoKVxuICB9KTtcbn0oKTtcbnZhciBnZXRMaWtlcyA9IC8qI19fUFVSRV9fKi9mdW5jdGlvbiAoKSB7XG4gIHZhciBnZXRQYXRobmFtZSA9IGZ1bmN0aW9uIGdldFBhdGhuYW1lKF9yZWY1KSB7XG4gICAgdmFyIHVzZXJuYW1lID0gX3JlZjUudXNlcm5hbWU7XG4gICAgcmV0dXJuIFVTRVJTX1BBVEhfUFJFRklYICsgXCIvXCIgKyB1c2VybmFtZSArIFwiL2xpa2VzXCI7XG4gIH07XG5cbiAgcmV0dXJuIG1ha2VFbmRwb2ludCh7XG4gICAgZ2V0UGF0aG5hbWU6IGdldFBhdGhuYW1lLFxuICAgIGhhbmRsZVJlcXVlc3Q6IGNyZWF0ZVJlcXVlc3RIYW5kbGVyKGZ1bmN0aW9uIChfcmVmNikge1xuICAgICAgdmFyIHVzZXJuYW1lID0gX3JlZjYudXNlcm5hbWUsXG4gICAgICAgICAgb3JpZW50YXRpb24gPSBfcmVmNi5vcmllbnRhdGlvbixcbiAgICAgICAgICBwYWdpbmF0aW9uUGFyYW1zID0gX29iamVjdFdpdGhvdXRQcm9wZXJ0aWVzTG9vc2UoX3JlZjYsIFtcInVzZXJuYW1lXCIsIFwib3JpZW50YXRpb25cIl0pO1xuXG4gICAgICByZXR1cm4ge1xuICAgICAgICBwYXRobmFtZTogZ2V0UGF0aG5hbWUoe1xuICAgICAgICAgIHVzZXJuYW1lOiB1c2VybmFtZVxuICAgICAgICB9KSxcbiAgICAgICAgcXVlcnk6IGNvbXBhY3REZWZpbmVkKF9leHRlbmRzKHt9LCBnZXRGZWVkUGFyYW1zKHBhZ2luYXRpb25QYXJhbXMpLCB7XG4gICAgICAgICAgb3JpZW50YXRpb246IG9yaWVudGF0aW9uXG4gICAgICAgIH0pKVxuICAgICAgfTtcbiAgICB9KSxcbiAgICBoYW5kbGVSZXNwb25zZTogaGFuZGxlRmVlZFJlc3BvbnNlKClcbiAgfSk7XG59KCk7XG52YXIgZ2V0Q29sbGVjdGlvbnMkMiA9IC8qI19fUFVSRV9fKi9mdW5jdGlvbiAoKSB7XG4gIHZhciBnZXRQYXRobmFtZSA9IGZ1bmN0aW9uIGdldFBhdGhuYW1lKF9yZWY3KSB7XG4gICAgdmFyIHVzZXJuYW1lID0gX3JlZjcudXNlcm5hbWU7XG4gICAgcmV0dXJuIFVTRVJTX1BBVEhfUFJFRklYICsgXCIvXCIgKyB1c2VybmFtZSArIFwiL2NvbGxlY3Rpb25zXCI7XG4gIH07XG5cbiAgcmV0dXJuIG1ha2VFbmRwb2ludCh7XG4gICAgZ2V0UGF0aG5hbWU6IGdldFBhdGhuYW1lLFxuICAgIGhhbmRsZVJlcXVlc3Q6IGNyZWF0ZVJlcXVlc3RIYW5kbGVyKGZ1bmN0aW9uIChfcmVmOCkge1xuICAgICAgdmFyIHVzZXJuYW1lID0gX3JlZjgudXNlcm5hbWUsXG4gICAgICAgICAgcGFnaW5hdGlvblBhcmFtcyA9IF9vYmplY3RXaXRob3V0UHJvcGVydGllc0xvb3NlKF9yZWY4LCBbXCJ1c2VybmFtZVwiXSk7XG5cbiAgICAgIHJldHVybiB7XG4gICAgICAgIHBhdGhuYW1lOiBnZXRQYXRobmFtZSh7XG4gICAgICAgICAgdXNlcm5hbWU6IHVzZXJuYW1lXG4gICAgICAgIH0pLFxuICAgICAgICBxdWVyeTogZ2V0RmVlZFBhcmFtcyhwYWdpbmF0aW9uUGFyYW1zKVxuICAgICAgfTtcbiAgICB9KSxcbiAgICBoYW5kbGVSZXNwb25zZTogaGFuZGxlRmVlZFJlc3BvbnNlKClcbiAgfSk7XG59KCk7XG5cbnZhciBpbmRleCQzID0ge1xuICBfX3Byb3RvX186IG51bGwsXG4gIGdldDogZ2V0JDIsXG4gIGdldFBob3RvczogZ2V0UGhvdG9zJDIsXG4gIGdldExpa2VzOiBnZXRMaWtlcyxcbiAgZ2V0Q29sbGVjdGlvbnM6IGdldENvbGxlY3Rpb25zJDJcbn07XG5cbnZhciBCQVNFX1RPUElDX1BBVEggPSAnL3RvcGljcyc7XG5cbnZhciBnZXRUb3BpY1BhdGggPSBmdW5jdGlvbiBnZXRUb3BpY1BhdGgoX3JlZikge1xuICB2YXIgdG9waWNJZE9yU2x1ZyA9IF9yZWYudG9waWNJZE9yU2x1ZztcbiAgcmV0dXJuIEJBU0VfVE9QSUNfUEFUSCArIFwiL1wiICsgdG9waWNJZE9yU2x1Zztcbn07XG5cbnZhciBsaXN0JDIgPSAvKiNfX1BVUkVfXyovbWFrZUVuZHBvaW50KHtcbiAgZ2V0UGF0aG5hbWU6IGdldFRvcGljUGF0aCxcbiAgaGFuZGxlUmVxdWVzdDogZnVuY3Rpb24gaGFuZGxlUmVxdWVzdChfcmVmMikge1xuICAgIHZhciBwYWdlID0gX3JlZjIucGFnZSxcbiAgICAgICAgcGVyUGFnZSA9IF9yZWYyLnBlclBhZ2UsXG4gICAgICAgIG9yZGVyQnkgPSBfcmVmMi5vcmRlckJ5LFxuICAgICAgICB0b3BpY0lkc09yU2x1Z3MgPSBfcmVmMi50b3BpY0lkc09yU2x1Z3M7XG4gICAgcmV0dXJuIHtcbiAgICAgIHBhdGhuYW1lOiBCQVNFX1RPUElDX1BBVEgsXG4gICAgICBxdWVyeTogY29tcGFjdERlZmluZWQoX2V4dGVuZHMoe30sIGdldEZlZWRQYXJhbXMoe1xuICAgICAgICBwYWdlOiBwYWdlLFxuICAgICAgICBwZXJQYWdlOiBwZXJQYWdlXG4gICAgICB9KSwge1xuICAgICAgICBpZHM6IHRvcGljSWRzT3JTbHVncyA9PSBudWxsID8gdm9pZCAwIDogdG9waWNJZHNPclNsdWdzLmpvaW4oJywnKSxcbiAgICAgICAgb3JkZXJfYnk6IG9yZGVyQnlcbiAgICAgIH0pKVxuICAgIH07XG4gIH0sXG4gIGhhbmRsZVJlc3BvbnNlOiAvKiNfX1BVUkVfXyovaGFuZGxlRmVlZFJlc3BvbnNlKClcbn0pO1xudmFyIGdldCQzID0gLyojX19QVVJFX18qL21ha2VFbmRwb2ludCh7XG4gIGdldFBhdGhuYW1lOiBnZXRUb3BpY1BhdGgsXG4gIGhhbmRsZVJlcXVlc3Q6IGZ1bmN0aW9uIGhhbmRsZVJlcXVlc3QoX3JlZjMpIHtcbiAgICB2YXIgdG9waWNJZE9yU2x1ZyA9IF9yZWYzLnRvcGljSWRPclNsdWc7XG4gICAgcmV0dXJuIHtcbiAgICAgIHBhdGhuYW1lOiBnZXRUb3BpY1BhdGgoe1xuICAgICAgICB0b3BpY0lkT3JTbHVnOiB0b3BpY0lkT3JTbHVnXG4gICAgICB9KSxcbiAgICAgIHF1ZXJ5OiB7fVxuICAgIH07XG4gIH0sXG4gIGhhbmRsZVJlc3BvbnNlOiAvKiNfX1BVUkVfXyovY2FzdFJlc3BvbnNlKClcbn0pO1xudmFyIGdldFBob3RvcyQzID0gLyojX19QVVJFX18qL2Z1bmN0aW9uICgpIHtcbiAgdmFyIGdldFBhdGhuYW1lID0gLyojX19QVVJFX18qL2Zsb3coZ2V0VG9waWNQYXRoLCBmdW5jdGlvbiAodG9waWNQYXRoKSB7XG4gICAgcmV0dXJuIHRvcGljUGF0aCArIFwiL3Bob3Rvc1wiO1xuICB9KTtcbiAgcmV0dXJuIG1ha2VFbmRwb2ludCh7XG4gICAgZ2V0UGF0aG5hbWU6IGdldFBhdGhuYW1lLFxuICAgIGhhbmRsZVJlcXVlc3Q6IGZ1bmN0aW9uIGhhbmRsZVJlcXVlc3QoX3JlZjQpIHtcbiAgICAgIHZhciB0b3BpY0lkT3JTbHVnID0gX3JlZjQudG9waWNJZE9yU2x1ZyxcbiAgICAgICAgICBvcmllbnRhdGlvbiA9IF9yZWY0Lm9yaWVudGF0aW9uLFxuICAgICAgICAgIGZlZWRQYXJhbXMgPSBfb2JqZWN0V2l0aG91dFByb3BlcnRpZXNMb29zZShfcmVmNCwgW1widG9waWNJZE9yU2x1Z1wiLCBcIm9yaWVudGF0aW9uXCJdKTtcblxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgcGF0aG5hbWU6IGdldFBhdGhuYW1lKHtcbiAgICAgICAgICB0b3BpY0lkT3JTbHVnOiB0b3BpY0lkT3JTbHVnXG4gICAgICAgIH0pLFxuICAgICAgICBxdWVyeTogY29tcGFjdERlZmluZWQoX2V4dGVuZHMoe30sIGdldEZlZWRQYXJhbXMoZmVlZFBhcmFtcyksIHtcbiAgICAgICAgICBvcmllbnRhdGlvbjogb3JpZW50YXRpb25cbiAgICAgICAgfSkpXG4gICAgICB9O1xuICAgIH0sXG4gICAgaGFuZGxlUmVzcG9uc2U6IGhhbmRsZUZlZWRSZXNwb25zZSgpXG4gIH0pO1xufSgpO1xuXG52YXIgaW5kZXgkNCA9IHtcbiAgX19wcm90b19fOiBudWxsLFxuICBsaXN0OiBsaXN0JDIsXG4gIGdldDogZ2V0JDMsXG4gIGdldFBob3RvczogZ2V0UGhvdG9zJDNcbn07XG5cbnZhciB0cmFja05vbkhvdExpbmtlZFBob3RvVmlldyA9IGZ1bmN0aW9uIHRyYWNrTm9uSG90TGlua2VkUGhvdG9WaWV3KF9yZWYpIHtcbiAgdmFyIGFwcElkID0gX3JlZi5hcHBJZDtcbiAgcmV0dXJuIGZ1bmN0aW9uIChfcmVmMikge1xuICAgIHZhciBwaG90b0lkID0gX3JlZjIucGhvdG9JZDtcbiAgICB2YXIgaWRzID0gIUFycmF5LmlzQXJyYXkocGhvdG9JZCkgPyBbcGhvdG9JZF0gOiBwaG90b0lkO1xuXG4gICAgaWYgKGlkcy5sZW5ndGggPiAyMCkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdZb3UgY2Fubm90IHRyYWNrIG1vcmUgdGhhbiAyMCBwaG90b3MgYXQgb25jZS4gUGxlYXNlIHRyeSBhZ2FpbiB3aXRoIGZld2VyIHBob3Rvcy4nKTtcbiAgICB9XG5cbiAgICByZXR1cm4gZmV0Y2goXCJ2aWV3cy51bnNwbGFzaC5jb20vdj9waG90b19pZD1cIiArIGlkcy5qb2luKCkgKyBcIiZhcHBfaWQ9XCIgKyBhcHBJZCk7XG4gIH07XG59O1xuXG5cblxudmFyIGludGVybmFscyA9IHtcbiAgX19wcm90b19fOiBudWxsLFxuICBjb2xsZWN0aW9uczogaW5kZXgsXG4gIHBob3RvczogaW5kZXgkMSxcbiAgc2VhcmNoOiBpbmRleCQyLFxuICB1c2VyczogaW5kZXgkMyxcbiAgdG9waWNzOiBpbmRleCQ0LFxuICB0cmFja05vbkhvdExpbmtlZFBob3RvVmlldzogdHJhY2tOb25Ib3RMaW5rZWRQaG90b1ZpZXdcbn07XG5cbnZhciBMYW5ndWFnZTtcblxuKGZ1bmN0aW9uIChMYW5ndWFnZSkge1xuICBMYW5ndWFnZVtcIkFmcmlrYWFuc1wiXSA9IFwiYWZcIjtcbiAgTGFuZ3VhZ2VbXCJBbWhhcmljXCJdID0gXCJhbVwiO1xuICBMYW5ndWFnZVtcIkFyYWJpY1wiXSA9IFwiYXJcIjtcbiAgTGFuZ3VhZ2VbXCJBemVyYmFpamFuaVwiXSA9IFwiYXpcIjtcbiAgTGFuZ3VhZ2VbXCJCZWxhcnVzaWFuXCJdID0gXCJiZVwiO1xuICBMYW5ndWFnZVtcIkJ1bGdhcmlhblwiXSA9IFwiYmdcIjtcbiAgTGFuZ3VhZ2VbXCJCZW5nYWxpXCJdID0gXCJiblwiO1xuICBMYW5ndWFnZVtcIkJvc25pYW5cIl0gPSBcImJzXCI7XG4gIExhbmd1YWdlW1wiQ2F0YWxhblwiXSA9IFwiY2FcIjtcbiAgTGFuZ3VhZ2VbXCJDZWJ1YW5vXCJdID0gXCJjZWJcIjtcbiAgTGFuZ3VhZ2VbXCJDb3JzaWNhblwiXSA9IFwiY29cIjtcbiAgTGFuZ3VhZ2VbXCJDemVjaFwiXSA9IFwiY3NcIjtcbiAgTGFuZ3VhZ2VbXCJXZWxzaFwiXSA9IFwiY3lcIjtcbiAgTGFuZ3VhZ2VbXCJEYW5pc2hcIl0gPSBcImRhXCI7XG4gIExhbmd1YWdlW1wiR2VybWFuXCJdID0gXCJkZVwiO1xuICBMYW5ndWFnZVtcIkdyZWVrXCJdID0gXCJlbFwiO1xuICBMYW5ndWFnZVtcIkVuZ2xpc2hcIl0gPSBcImVuXCI7XG4gIExhbmd1YWdlW1wiRXNwZXJhbnRvXCJdID0gXCJlb1wiO1xuICBMYW5ndWFnZVtcIlNwYW5pc2hcIl0gPSBcImVzXCI7XG4gIExhbmd1YWdlW1wiRXN0b25pYW5cIl0gPSBcImV0XCI7XG4gIExhbmd1YWdlW1wiQmFzcXVlXCJdID0gXCJldVwiO1xuICBMYW5ndWFnZVtcIlBlcnNpYW5cIl0gPSBcImZhXCI7XG4gIExhbmd1YWdlW1wiRmlubmlzaFwiXSA9IFwiZmlcIjtcbiAgTGFuZ3VhZ2VbXCJGcmVuY2hcIl0gPSBcImZyXCI7XG4gIExhbmd1YWdlW1wiRnJpc2lhblwiXSA9IFwiZnlcIjtcbiAgTGFuZ3VhZ2VbXCJJcmlzaFwiXSA9IFwiZ2FcIjtcbiAgTGFuZ3VhZ2VbXCJTY290c0dhZWxpY1wiXSA9IFwiZ2RcIjtcbiAgTGFuZ3VhZ2VbXCJHYWxpY2lhblwiXSA9IFwiZ2xcIjtcbiAgTGFuZ3VhZ2VbXCJHdWphcmF0aVwiXSA9IFwiZ3VcIjtcbiAgTGFuZ3VhZ2VbXCJIYXVzYVwiXSA9IFwiaGFcIjtcbiAgTGFuZ3VhZ2VbXCJIYXdhaWlhblwiXSA9IFwiaGF3XCI7XG4gIExhbmd1YWdlW1wiSGluZGlcIl0gPSBcImhpXCI7XG4gIExhbmd1YWdlW1wiSG1vbmdcIl0gPSBcImhtblwiO1xuICBMYW5ndWFnZVtcIkNyb2F0aWFuXCJdID0gXCJoclwiO1xuICBMYW5ndWFnZVtcIkhhaXRpYW5DcmVvbGVcIl0gPSBcImh0XCI7XG4gIExhbmd1YWdlW1wiSHVuZ2FyaWFuXCJdID0gXCJodVwiO1xuICBMYW5ndWFnZVtcIkFybWVuaWFuXCJdID0gXCJoeVwiO1xuICBMYW5ndWFnZVtcIkluZG9uZXNpYW5cIl0gPSBcImlkXCI7XG4gIExhbmd1YWdlW1wiSWdib1wiXSA9IFwiaWdcIjtcbiAgTGFuZ3VhZ2VbXCJJY2VsYW5kaWNcIl0gPSBcImlzXCI7XG4gIExhbmd1YWdlW1wiSXRhbGlhblwiXSA9IFwiaXRcIjtcbiAgTGFuZ3VhZ2VbXCJIZWJyZXdcIl0gPSBcIml3XCI7XG4gIExhbmd1YWdlW1wiSmFwYW5lc2VcIl0gPSBcImphXCI7XG4gIExhbmd1YWdlW1wiSmF2YW5lc2VcIl0gPSBcImp3XCI7XG4gIExhbmd1YWdlW1wiR2VvcmdpYW5cIl0gPSBcImthXCI7XG4gIExhbmd1YWdlW1wiS2F6YWtoXCJdID0gXCJra1wiO1xuICBMYW5ndWFnZVtcIktobWVyXCJdID0gXCJrbVwiO1xuICBMYW5ndWFnZVtcIkthbm5hZGFcIl0gPSBcImtuXCI7XG4gIExhbmd1YWdlW1wiS29yZWFuXCJdID0gXCJrb1wiO1xuICBMYW5ndWFnZVtcIkt1cmRpc2hcIl0gPSBcImt1XCI7XG4gIExhbmd1YWdlW1wiS3lyZ3l6XCJdID0gXCJreVwiO1xuICBMYW5ndWFnZVtcIkxhdGluXCJdID0gXCJsYVwiO1xuICBMYW5ndWFnZVtcIkx1eGVtYm91cmdpc2hcIl0gPSBcImxiXCI7XG4gIExhbmd1YWdlW1wiTGFvXCJdID0gXCJsb1wiO1xuICBMYW5ndWFnZVtcIkxpdGh1YW5pYW5cIl0gPSBcImx0XCI7XG4gIExhbmd1YWdlW1wiTGF0dmlhblwiXSA9IFwibHZcIjtcbiAgTGFuZ3VhZ2VbXCJNYWxhZ2FzeVwiXSA9IFwibWdcIjtcbiAgTGFuZ3VhZ2VbXCJNYW9yaVwiXSA9IFwibWlcIjtcbiAgTGFuZ3VhZ2VbXCJNYWNlZG9uaWFuXCJdID0gXCJta1wiO1xuICBMYW5ndWFnZVtcIk1hbGF5YWxhbVwiXSA9IFwibWxcIjtcbiAgTGFuZ3VhZ2VbXCJNb25nb2xpYW5cIl0gPSBcIm1uXCI7XG4gIExhbmd1YWdlW1wiTWFyYXRoaVwiXSA9IFwibXJcIjtcbiAgTGFuZ3VhZ2VbXCJNYWxheVwiXSA9IFwibXNcIjtcbiAgTGFuZ3VhZ2VbXCJNYWx0ZXNlXCJdID0gXCJtdFwiO1xuICBMYW5ndWFnZVtcIk15YW5tYXJcIl0gPSBcIm15XCI7XG4gIExhbmd1YWdlW1wiTmVwYWxpXCJdID0gXCJuZVwiO1xuICBMYW5ndWFnZVtcIkR1dGNoXCJdID0gXCJubFwiO1xuICBMYW5ndWFnZVtcIk5vcndlZ2lhblwiXSA9IFwibm9cIjtcbiAgTGFuZ3VhZ2VbXCJOeWFuamFcIl0gPSBcIm55XCI7XG4gIExhbmd1YWdlW1wiT3JpeWFcIl0gPSBcIm9yXCI7XG4gIExhbmd1YWdlW1wiUHVuamFiaVwiXSA9IFwicGFcIjtcbiAgTGFuZ3VhZ2VbXCJQb2xpc2hcIl0gPSBcInBsXCI7XG4gIExhbmd1YWdlW1wiUGFzaHRvXCJdID0gXCJwc1wiO1xuICBMYW5ndWFnZVtcIlBvcnR1Z3Vlc2VcIl0gPSBcInB0XCI7XG4gIExhbmd1YWdlW1wiUm9tYW5pYW5cIl0gPSBcInJvXCI7XG4gIExhbmd1YWdlW1wiUnVzc2lhblwiXSA9IFwicnVcIjtcbiAgTGFuZ3VhZ2VbXCJLaW55YXJ3YW5kYVwiXSA9IFwicndcIjtcbiAgTGFuZ3VhZ2VbXCJTaW5kaGlcIl0gPSBcInNkXCI7XG4gIExhbmd1YWdlW1wiU2luaGFsYVwiXSA9IFwic2lcIjtcbiAgTGFuZ3VhZ2VbXCJTbG92YWtcIl0gPSBcInNrXCI7XG4gIExhbmd1YWdlW1wiU2xvdmVuaWFuXCJdID0gXCJzbFwiO1xuICBMYW5ndWFnZVtcIlNhbW9hblwiXSA9IFwic21cIjtcbiAgTGFuZ3VhZ2VbXCJTaG9uYVwiXSA9IFwic25cIjtcbiAgTGFuZ3VhZ2VbXCJTb21hbGlcIl0gPSBcInNvXCI7XG4gIExhbmd1YWdlW1wiQWxiYW5pYW5cIl0gPSBcInNxXCI7XG4gIExhbmd1YWdlW1wiU2VyYmlhblwiXSA9IFwic3JcIjtcbiAgTGFuZ3VhZ2VbXCJTZXNvdGhvXCJdID0gXCJzdFwiO1xuICBMYW5ndWFnZVtcIlN1bmRhbmVzZVwiXSA9IFwic3VcIjtcbiAgTGFuZ3VhZ2VbXCJTd2VkaXNoXCJdID0gXCJzdlwiO1xuICBMYW5ndWFnZVtcIlN3YWhpbGlcIl0gPSBcInN3XCI7XG4gIExhbmd1YWdlW1wiVGFtaWxcIl0gPSBcInRhXCI7XG4gIExhbmd1YWdlW1wiVGVsdWd1XCJdID0gXCJ0ZVwiO1xuICBMYW5ndWFnZVtcIlRhamlrXCJdID0gXCJ0Z1wiO1xuICBMYW5ndWFnZVtcIlRoYWlcIl0gPSBcInRoXCI7XG4gIExhbmd1YWdlW1wiVHVya21lblwiXSA9IFwidGtcIjtcbiAgTGFuZ3VhZ2VbXCJGaWxpcGlub1wiXSA9IFwidGxcIjtcbiAgTGFuZ3VhZ2VbXCJUdXJraXNoXCJdID0gXCJ0clwiO1xuICBMYW5ndWFnZVtcIlRhdGFyXCJdID0gXCJ0dFwiO1xuICBMYW5ndWFnZVtcIlVpZ2h1clwiXSA9IFwidWdcIjtcbiAgTGFuZ3VhZ2VbXCJVa3JhaW5pYW5cIl0gPSBcInVrXCI7XG4gIExhbmd1YWdlW1wiVXJkdVwiXSA9IFwidXJcIjtcbiAgTGFuZ3VhZ2VbXCJVemJla1wiXSA9IFwidXpcIjtcbiAgTGFuZ3VhZ2VbXCJWaWV0bmFtZXNlXCJdID0gXCJ2aVwiO1xuICBMYW5ndWFnZVtcIlhob3NhXCJdID0gXCJ4aFwiO1xuICBMYW5ndWFnZVtcIllpZGRpc2hcIl0gPSBcInlpXCI7XG4gIExhbmd1YWdlW1wiWW9ydWJhXCJdID0gXCJ5b1wiO1xuICBMYW5ndWFnZVtcIkNoaW5lc2VTaW1wbGlmaWVkXCJdID0gXCJ6aFwiO1xuICBMYW5ndWFnZVtcIkNoaW5lc2VUcmFkaXRpb25hbFwiXSA9IFwiemgtVFdcIjtcbiAgTGFuZ3VhZ2VbXCJadWx1XCJdID0gXCJ6dVwiO1xufSkoTGFuZ3VhZ2UgfHwgKExhbmd1YWdlID0ge30pKTtcblxudmFyIE9yZGVyQnk7XG5cbihmdW5jdGlvbiAoT3JkZXJCeSkge1xuICBPcmRlckJ5W1wiTEFURVNUXCJdID0gXCJsYXRlc3RcIjtcbiAgT3JkZXJCeVtcIlBPUFVMQVJcIl0gPSBcInBvcHVsYXJcIjtcbiAgT3JkZXJCeVtcIlZJRVdTXCJdID0gXCJ2aWV3c1wiO1xuICBPcmRlckJ5W1wiRE9XTkxPQURTXCJdID0gXCJkb3dubG9hZHNcIjtcbiAgT3JkZXJCeVtcIk9MREVTVFwiXSA9IFwib2xkZXN0XCI7XG59KShPcmRlckJ5IHx8IChPcmRlckJ5ID0ge30pKTtcblxudmFyIGNyZWF0ZUFwaSA9IC8qI19fUFVSRV9fKi9mbG93KGluaXRNYWtlUmVxdWVzdCwgZnVuY3Rpb24gKG1ha2VSZXF1ZXN0KSB7XG4gIHJldHVybiB7XG4gICAgcGhvdG9zOiB7XG4gICAgICBnZXQ6IG1ha2VSZXF1ZXN0KGdldCQxKSxcbiAgICAgIGxpc3Q6IG1ha2VSZXF1ZXN0KGxpc3QkMSksXG4gICAgICBnZXRTdGF0czogbWFrZVJlcXVlc3QoZ2V0U3RhdHMpLFxuICAgICAgZ2V0UmFuZG9tOiBtYWtlUmVxdWVzdChnZXRSYW5kb20pLFxuICAgICAgdHJhY2tEb3dubG9hZDogbWFrZVJlcXVlc3QodHJhY2tEb3dubG9hZClcbiAgICB9LFxuICAgIHVzZXJzOiB7XG4gICAgICBnZXRQaG90b3M6IG1ha2VSZXF1ZXN0KGdldFBob3RvcyQyKSxcbiAgICAgIGdldENvbGxlY3Rpb25zOiBtYWtlUmVxdWVzdChnZXRDb2xsZWN0aW9ucyQyKSxcbiAgICAgIGdldExpa2VzOiBtYWtlUmVxdWVzdChnZXRMaWtlcyksXG4gICAgICBnZXQ6IG1ha2VSZXF1ZXN0KGdldCQyKVxuICAgIH0sXG4gICAgc2VhcmNoOiB7XG4gICAgICBnZXRDb2xsZWN0aW9uczogbWFrZVJlcXVlc3QoZ2V0Q29sbGVjdGlvbnMkMSksXG4gICAgICBnZXRQaG90b3M6IG1ha2VSZXF1ZXN0KGdldFBob3RvcyQxKSxcbiAgICAgIGdldFVzZXJzOiBtYWtlUmVxdWVzdChnZXRVc2VycylcbiAgICB9LFxuICAgIGNvbGxlY3Rpb25zOiB7XG4gICAgICBnZXRQaG90b3M6IG1ha2VSZXF1ZXN0KGdldFBob3RvcyksXG4gICAgICBnZXQ6IG1ha2VSZXF1ZXN0KGdldCksXG4gICAgICBsaXN0OiBtYWtlUmVxdWVzdChsaXN0KSxcbiAgICAgIGdldFJlbGF0ZWQ6IG1ha2VSZXF1ZXN0KGdldFJlbGF0ZWQpXG4gICAgfSxcbiAgICB0b3BpY3M6IHtcbiAgICAgIGxpc3Q6IG1ha2VSZXF1ZXN0KGxpc3QkMiksXG4gICAgICBnZXQ6IG1ha2VSZXF1ZXN0KGdldCQzKSxcbiAgICAgIGdldFBob3RvczogbWFrZVJlcXVlc3QoZ2V0UGhvdG9zJDMpXG4gICAgfVxuICB9O1xufSk7XG5cbmV4cG9ydCB7IExhbmd1YWdlLCBPcmRlckJ5LCBpbnRlcm5hbHMgYXMgX2ludGVybmFscywgY3JlYXRlQXBpIH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD11bnNwbGFzaC1qcy5lc20uanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/unsplash-js/dist/unsplash-js.esm.js\n");

/***/ })

};
;