"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/sonner";
exports.ids = ["vendor-chunks/sonner"];
exports.modules = {

/***/ "(ssr)/./node_modules/sonner/dist/index.mjs":
/*!********************************************!*\
  !*** ./node_modules/sonner/dist/index.mjs ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Te),\n/* harmony export */   toast: () => (/* binding */ Jt),\n/* harmony export */   useSonner: () => (/* binding */ we)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* __next_internal_client_entry_do_not_use__ Toaster,toast,useSonner auto */ \n\n\nvar Ct = (s)=>{\n    switch(s){\n        case \"success\":\n            return $t;\n        case \"info\":\n            return _t;\n        case \"warning\":\n            return Wt;\n        case \"error\":\n            return Ut;\n        default:\n            return null;\n    }\n}, Ft = Array(12).fill(0), It = ({ visible: s })=>/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        className: \"sonner-loading-wrapper\",\n        \"data-visible\": s\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        className: \"sonner-spinner\"\n    }, Ft.map((o, t)=>/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n            className: \"sonner-loading-bar\",\n            key: `spinner-bar-${t}`\n        })))), $t = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z\",\n    clipRule: \"evenodd\"\n})), Wt = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z\",\n    clipRule: \"evenodd\"\n})), _t = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z\",\n    clipRule: \"evenodd\"\n})), Ut = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z\",\n    clipRule: \"evenodd\"\n}));\n\nvar Dt = ()=>{\n    let [s, o] = react__WEBPACK_IMPORTED_MODULE_0__.useState(document.hidden);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        let t = ()=>{\n            o(document.hidden);\n        };\n        return document.addEventListener(\"visibilitychange\", t), ()=>window.removeEventListener(\"visibilitychange\", t);\n    }, []), s;\n};\nvar ct = 1, ut = class {\n    constructor(){\n        this.subscribe = (o)=>(this.subscribers.push(o), ()=>{\n                let t = this.subscribers.indexOf(o);\n                this.subscribers.splice(t, 1);\n            });\n        this.publish = (o)=>{\n            this.subscribers.forEach((t)=>t(o));\n        };\n        this.addToast = (o)=>{\n            this.publish(o), this.toasts = [\n                ...this.toasts,\n                o\n            ];\n        };\n        this.create = (o)=>{\n            var b;\n            let { message: t, ...n } = o, h = typeof (o == null ? void 0 : o.id) == \"number\" || ((b = o.id) == null ? void 0 : b.length) > 0 ? o.id : ct++, u = this.toasts.find((d)=>d.id === h), g = o.dismissible === void 0 ? !0 : o.dismissible;\n            return u ? this.toasts = this.toasts.map((d)=>d.id === h ? (this.publish({\n                    ...d,\n                    ...o,\n                    id: h,\n                    title: t\n                }), {\n                    ...d,\n                    ...o,\n                    id: h,\n                    dismissible: g,\n                    title: t\n                }) : d) : this.addToast({\n                title: t,\n                ...n,\n                dismissible: g,\n                id: h\n            }), h;\n        };\n        this.dismiss = (o)=>(o || this.toasts.forEach((t)=>{\n                this.subscribers.forEach((n)=>n({\n                        id: t.id,\n                        dismiss: !0\n                    }));\n            }), this.subscribers.forEach((t)=>t({\n                    id: o,\n                    dismiss: !0\n                })), o);\n        this.message = (o, t)=>this.create({\n                ...t,\n                message: o\n            });\n        this.error = (o, t)=>this.create({\n                ...t,\n                message: o,\n                type: \"error\"\n            });\n        this.success = (o, t)=>this.create({\n                ...t,\n                type: \"success\",\n                message: o\n            });\n        this.info = (o, t)=>this.create({\n                ...t,\n                type: \"info\",\n                message: o\n            });\n        this.warning = (o, t)=>this.create({\n                ...t,\n                type: \"warning\",\n                message: o\n            });\n        this.loading = (o, t)=>this.create({\n                ...t,\n                type: \"loading\",\n                message: o\n            });\n        this.promise = (o, t)=>{\n            if (!t) return;\n            let n;\n            t.loading !== void 0 && (n = this.create({\n                ...t,\n                promise: o,\n                type: \"loading\",\n                message: t.loading,\n                description: typeof t.description != \"function\" ? t.description : void 0\n            }));\n            let h = o instanceof Promise ? o : o(), u = n !== void 0;\n            return h.then(async (g)=>{\n                if (Ot(g) && !g.ok) {\n                    u = !1;\n                    let b = typeof t.error == \"function\" ? await t.error(`HTTP error! status: ${g.status}`) : t.error, d = typeof t.description == \"function\" ? await t.description(`HTTP error! status: ${g.status}`) : t.description;\n                    this.create({\n                        id: n,\n                        type: \"error\",\n                        message: b,\n                        description: d\n                    });\n                } else if (t.success !== void 0) {\n                    u = !1;\n                    let b = typeof t.success == \"function\" ? await t.success(g) : t.success, d = typeof t.description == \"function\" ? await t.description(g) : t.description;\n                    this.create({\n                        id: n,\n                        type: \"success\",\n                        message: b,\n                        description: d\n                    });\n                }\n            }).catch(async (g)=>{\n                if (t.error !== void 0) {\n                    u = !1;\n                    let b = typeof t.error == \"function\" ? await t.error(g) : t.error, d = typeof t.description == \"function\" ? await t.description(g) : t.description;\n                    this.create({\n                        id: n,\n                        type: \"error\",\n                        message: b,\n                        description: d\n                    });\n                }\n            }).finally(()=>{\n                var g;\n                u && (this.dismiss(n), n = void 0), (g = t.finally) == null || g.call(t);\n            }), n;\n        };\n        this.custom = (o, t)=>{\n            let n = (t == null ? void 0 : t.id) || ct++;\n            return this.create({\n                jsx: o(n),\n                id: n,\n                ...t\n            }), n;\n        };\n        this.subscribers = [], this.toasts = [];\n    }\n}, v = new ut, Vt = (s, o)=>{\n    let t = (o == null ? void 0 : o.id) || ct++;\n    return v.addToast({\n        title: s,\n        ...o,\n        id: t\n    }), t;\n}, Ot = (s)=>s && typeof s == \"object\" && \"ok\" in s && typeof s.ok == \"boolean\" && \"status\" in s && typeof s.status == \"number\", Kt = Vt, Xt = ()=>v.toasts, Jt = Object.assign(Kt, {\n    success: v.success,\n    info: v.info,\n    warning: v.warning,\n    error: v.error,\n    custom: v.custom,\n    message: v.message,\n    promise: v.promise,\n    dismiss: v.dismiss,\n    loading: v.loading\n}, {\n    getHistory: Xt\n});\nfunction ft(s, { insertAt: o } = {}) {\n    if (!s || typeof document == \"undefined\") return;\n    let t = document.head || document.getElementsByTagName(\"head\")[0], n = document.createElement(\"style\");\n    n.type = \"text/css\", o === \"top\" && t.firstChild ? t.insertBefore(n, t.firstChild) : t.appendChild(n), n.styleSheet ? n.styleSheet.cssText = s : n.appendChild(document.createTextNode(s));\n}\nft(`:where(html[dir=\"ltr\"]),:where([data-sonner-toaster][dir=\"ltr\"]){--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}:where(html[dir=\"rtl\"]),:where([data-sonner-toaster][dir=\"rtl\"]){--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}:where([data-sonner-toaster]){position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999}:where([data-sonner-toaster][data-x-position=\"right\"]){right:max(var(--offset),env(safe-area-inset-right))}:where([data-sonner-toaster][data-x-position=\"left\"]){left:max(var(--offset),env(safe-area-inset-left))}:where([data-sonner-toaster][data-x-position=\"center\"]){left:50%;transform:translate(-50%)}:where([data-sonner-toaster][data-y-position=\"top\"]){top:max(var(--offset),env(safe-area-inset-top))}:where([data-sonner-toaster][data-y-position=\"bottom\"]){bottom:max(var(--offset),env(safe-area-inset-bottom))}:where([data-sonner-toast]){--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);filter:blur(0);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}:where([data-sonner-toast][data-styled=\"true\"]){padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}:where([data-sonner-toast]:focus-visible){box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast][data-y-position=\"top\"]){top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}:where([data-sonner-toast][data-y-position=\"bottom\"]){bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}:where([data-sonner-toast]) :where([data-description]){font-weight:400;line-height:1.4;color:inherit}:where([data-sonner-toast]) :where([data-title]){font-weight:500;line-height:1.5;color:inherit}:where([data-sonner-toast]) :where([data-icon]){display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}:where([data-sonner-toast][data-promise=\"true\"]) :where([data-icon])>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}:where([data-sonner-toast]) :where([data-icon])>*{flex-shrink:0}:where([data-sonner-toast]) :where([data-icon]) svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}:where([data-sonner-toast]) :where([data-content]){display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}:where([data-sonner-toast]) :where([data-button]):focus-visible{box-shadow:0 0 0 2px #0006}:where([data-sonner-toast]) :where([data-button]):first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}:where([data-sonner-toast]) :where([data-cancel]){color:var(--normal-text);background:rgba(0,0,0,.08)}:where([data-sonner-toast][data-theme=\"dark\"]) :where([data-cancel]){background:rgba(255,255,255,.3)}:where([data-sonner-toast]) :where([data-close-button]){position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;background:var(--gray1);color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}:where([data-sonner-toast]) :where([data-close-button]):focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast]) :where([data-disabled=\"true\"]){cursor:not-allowed}:where([data-sonner-toast]):hover :where([data-close-button]):hover{background:var(--gray2);border-color:var(--gray5)}:where([data-sonner-toast][data-swiping=\"true\"]):before{content:\"\";position:absolute;left:0;right:0;height:100%;z-index:-1}:where([data-sonner-toast][data-y-position=\"top\"][data-swiping=\"true\"]):before{bottom:50%;transform:scaleY(3) translateY(50%)}:where([data-sonner-toast][data-y-position=\"bottom\"][data-swiping=\"true\"]):before{top:50%;transform:scaleY(3) translateY(-50%)}:where([data-sonner-toast][data-swiping=\"false\"][data-removed=\"true\"]):before{content:\"\";position:absolute;inset:0;transform:scaleY(2)}:where([data-sonner-toast]):after{content:\"\";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}:where([data-sonner-toast][data-mounted=\"true\"]){--y: translateY(0);opacity:1}:where([data-sonner-toast][data-expanded=\"false\"][data-front=\"false\"]){--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}:where([data-sonner-toast])>*{transition:opacity .4s}:where([data-sonner-toast][data-expanded=\"false\"][data-front=\"false\"][data-styled=\"true\"])>*{opacity:0}:where([data-sonner-toast][data-visible=\"false\"]){opacity:0;pointer-events:none}:where([data-sonner-toast][data-mounted=\"true\"][data-expanded=\"true\"]){--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}:where([data-sonner-toast][data-removed=\"true\"][data-front=\"true\"][data-swipe-out=\"false\"]){--y: translateY(calc(var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed=\"true\"][data-front=\"false\"][data-swipe-out=\"false\"][data-expanded=\"true\"]){--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed=\"true\"][data-front=\"false\"][data-swipe-out=\"false\"][data-expanded=\"false\"]){--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}:where([data-sonner-toast][data-removed=\"true\"][data-front=\"false\"]):before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount, 0px));transition:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation:swipe-out .2s ease-out forwards}@keyframes swipe-out{0%{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount)));opacity:1}to{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount) + var(--lift) * -100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;--mobile-offset: 16px;right:var(--mobile-offset);left:var(--mobile-offset);width:100%}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset)}[data-sonner-toaster][data-y-position=bottom]{bottom:20px}[data-sonner-toaster][data-y-position=top]{top:20px}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset);right:var(--mobile-offset);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-rich-colors=true][data-sonner-toast][data-type=success],[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info],[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning],[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error],[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}\n`);\nfunction U(s) {\n    return s.label !== void 0;\n}\nvar qt = 3, Qt = \"32px\", Zt = 4e3, te = 356, ee = 14, oe = 20, ae = 200;\nfunction ne(...s) {\n    return s.filter(Boolean).join(\" \");\n}\nvar se = (s)=>{\n    var yt, xt, vt, wt, Tt, St, Rt, Et, Nt, Pt;\n    let { invert: o, toast: t, unstyled: n, interacting: h, setHeights: u, visibleToasts: g, heights: b, index: d, toasts: q, expanded: $, removeToast: V, defaultRichColors: Q, closeButton: i, style: O, cancelButtonStyle: K, actionButtonStyle: Z, className: tt = \"\", descriptionClassName: et = \"\", duration: X, position: ot, gap: w, loadingIcon: j, expandByDefault: W, classNames: r, icons: I, closeButtonAriaLabel: at = \"Close toast\", pauseWhenPageIsHidden: k, cn: T } = s, [z, nt] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [D, H] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [st, N] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [M, rt] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [c, m] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0), [y, S] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0), A = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null), l = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null), _ = d === 0, J = d + 1 <= g, x = t.type, P = t.dismissible !== !1, Mt = t.className || \"\", At = t.descriptionClassName || \"\", G = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>b.findIndex((a)=>a.toastId === t.id) || 0, [\n        b,\n        t.id\n    ]), Lt = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n        var a;\n        return (a = t.closeButton) != null ? a : i;\n    }, [\n        t.closeButton,\n        i\n    ]), mt = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>t.duration || X || Zt, [\n        t.duration,\n        X\n    ]), it = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0), Y = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0), pt = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0), F = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null), [gt, zt] = ot.split(\"-\"), ht = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>b.reduce((a, f, p)=>p >= G ? a : a + f.height, 0), [\n        b,\n        G\n    ]), bt = Dt(), jt = t.invert || o, lt = x === \"loading\";\n    Y.current = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>G * w + ht, [\n        G,\n        ht\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        nt(!0);\n    }, []), react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect(()=>{\n        if (!z) return;\n        let a = l.current, f = a.style.height;\n        a.style.height = \"auto\";\n        let p = a.getBoundingClientRect().height;\n        a.style.height = f, S(p), u((B)=>B.find((R)=>R.toastId === t.id) ? B.map((R)=>R.toastId === t.id ? {\n                    ...R,\n                    height: p\n                } : R) : [\n                {\n                    toastId: t.id,\n                    height: p,\n                    position: t.position\n                },\n                ...B\n            ]);\n    }, [\n        z,\n        t.title,\n        t.description,\n        u,\n        t.id\n    ]);\n    let L = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        H(!0), m(Y.current), u((a)=>a.filter((f)=>f.toastId !== t.id)), setTimeout(()=>{\n            V(t);\n        }, ae);\n    }, [\n        t,\n        V,\n        u,\n        Y\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (t.promise && x === \"loading\" || t.duration === 1 / 0 || t.type === \"loading\") return;\n        let a, f = mt;\n        return $ || h || k && bt ? (()=>{\n            if (pt.current < it.current) {\n                let C = new Date().getTime() - it.current;\n                f = f - C;\n            }\n            pt.current = new Date().getTime();\n        })() : (()=>{\n            f !== 1 / 0 && (it.current = new Date().getTime(), a = setTimeout(()=>{\n                var C;\n                (C = t.onAutoClose) == null || C.call(t, t), L();\n            }, f));\n        })(), ()=>clearTimeout(a);\n    }, [\n        $,\n        h,\n        W,\n        t,\n        mt,\n        L,\n        t.promise,\n        x,\n        k,\n        bt\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        let a = l.current;\n        if (a) {\n            let f = a.getBoundingClientRect().height;\n            return S(f), u((p)=>[\n                    {\n                        toastId: t.id,\n                        height: f,\n                        position: t.position\n                    },\n                    ...p\n                ]), ()=>u((p)=>p.filter((B)=>B.toastId !== t.id));\n        }\n    }, [\n        u,\n        t.id\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        t.delete && L();\n    }, [\n        L,\n        t.delete\n    ]);\n    function Yt() {\n        return I != null && I.loading ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n            className: \"sonner-loader\",\n            \"data-visible\": x === \"loading\"\n        }, I.loading) : j ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n            className: \"sonner-loader\",\n            \"data-visible\": x === \"loading\"\n        }, j) : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(It, {\n            visible: x === \"loading\"\n        });\n    }\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"li\", {\n        \"aria-live\": t.important ? \"assertive\" : \"polite\",\n        \"aria-atomic\": \"true\",\n        role: \"status\",\n        tabIndex: 0,\n        ref: l,\n        className: T(tt, Mt, r == null ? void 0 : r.toast, (yt = t == null ? void 0 : t.classNames) == null ? void 0 : yt.toast, r == null ? void 0 : r.default, r == null ? void 0 : r[x], (xt = t == null ? void 0 : t.classNames) == null ? void 0 : xt[x]),\n        \"data-sonner-toast\": \"\",\n        \"data-rich-colors\": (vt = t.richColors) != null ? vt : Q,\n        \"data-styled\": !(t.jsx || t.unstyled || n),\n        \"data-mounted\": z,\n        \"data-promise\": !!t.promise,\n        \"data-removed\": D,\n        \"data-visible\": J,\n        \"data-y-position\": gt,\n        \"data-x-position\": zt,\n        \"data-index\": d,\n        \"data-front\": _,\n        \"data-swiping\": st,\n        \"data-dismissible\": P,\n        \"data-type\": x,\n        \"data-invert\": jt,\n        \"data-swipe-out\": M,\n        \"data-expanded\": !!($ || W && z),\n        style: {\n            \"--index\": d,\n            \"--toasts-before\": d,\n            \"--z-index\": q.length - d,\n            \"--offset\": `${D ? c : Y.current}px`,\n            \"--initial-height\": W ? \"auto\" : `${y}px`,\n            ...O,\n            ...t.style\n        },\n        onPointerDown: (a)=>{\n            lt || !P || (A.current = new Date, m(Y.current), a.target.setPointerCapture(a.pointerId), a.target.tagName !== \"BUTTON\" && (N(!0), F.current = {\n                x: a.clientX,\n                y: a.clientY\n            }));\n        },\n        onPointerUp: ()=>{\n            var B, C, R, dt;\n            if (M || !P) return;\n            F.current = null;\n            let a = Number(((B = l.current) == null ? void 0 : B.style.getPropertyValue(\"--swipe-amount\").replace(\"px\", \"\")) || 0), f = new Date().getTime() - ((C = A.current) == null ? void 0 : C.getTime()), p = Math.abs(a) / f;\n            if (Math.abs(a) >= oe || p > .11) {\n                m(Y.current), (R = t.onDismiss) == null || R.call(t, t), L(), rt(!0);\n                return;\n            }\n            (dt = l.current) == null || dt.style.setProperty(\"--swipe-amount\", \"0px\"), N(!1);\n        },\n        onPointerMove: (a)=>{\n            var Bt;\n            if (!F.current || !P) return;\n            let f = a.clientY - F.current.y, p = a.clientX - F.current.x, C = (gt === \"top\" ? Math.min : Math.max)(0, f), R = a.pointerType === \"touch\" ? 10 : 2;\n            Math.abs(C) > R ? (Bt = l.current) == null || Bt.style.setProperty(\"--swipe-amount\", `${f}px`) : Math.abs(p) > R && (F.current = null);\n        }\n    }, Lt && !t.jsx ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"aria-label\": at,\n        \"data-disabled\": lt,\n        \"data-close-button\": !0,\n        onClick: lt || !P ? ()=>{} : ()=>{\n            var a;\n            L(), (a = t.onDismiss) == null || a.call(t, t);\n        },\n        className: T(r == null ? void 0 : r.closeButton, (wt = t == null ? void 0 : t.classNames) == null ? void 0 : wt.closeButton)\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        width: \"12\",\n        height: \"12\",\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"1.5\",\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\"\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"line\", {\n        x1: \"18\",\n        y1: \"6\",\n        x2: \"6\",\n        y2: \"18\"\n    }), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"line\", {\n        x1: \"6\",\n        y1: \"6\",\n        x2: \"18\",\n        y2: \"18\"\n    }))) : null, t.jsx || /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(t.title) ? t.jsx || t.title : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, x || t.icon || t.promise ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-icon\": \"\",\n        className: T(r == null ? void 0 : r.icon, (Tt = t == null ? void 0 : t.classNames) == null ? void 0 : Tt.icon)\n    }, t.promise || t.type === \"loading\" && !t.icon ? t.icon || Yt() : null, t.type !== \"loading\" ? t.icon || (I == null ? void 0 : I[x]) || Ct(x) : null) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-content\": \"\",\n        className: T(r == null ? void 0 : r.content, (St = t == null ? void 0 : t.classNames) == null ? void 0 : St.content)\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-title\": \"\",\n        className: T(r == null ? void 0 : r.title, (Rt = t == null ? void 0 : t.classNames) == null ? void 0 : Rt.title)\n    }, t.title), t.description ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-description\": \"\",\n        className: T(et, At, r == null ? void 0 : r.description, (Et = t == null ? void 0 : t.classNames) == null ? void 0 : Et.description)\n    }, t.description) : null), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(t.cancel) ? t.cancel : t.cancel && U(t.cancel) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"data-button\": !0,\n        \"data-cancel\": !0,\n        style: t.cancelButtonStyle || K,\n        onClick: (a)=>{\n            var f, p;\n            U(t.cancel) && P && ((p = (f = t.cancel).onClick) == null || p.call(f, a), L());\n        },\n        className: T(r == null ? void 0 : r.cancelButton, (Nt = t == null ? void 0 : t.classNames) == null ? void 0 : Nt.cancelButton)\n    }, t.cancel.label) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(t.action) ? t.action : t.action && U(t.action) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"data-button\": !0,\n        \"data-action\": !0,\n        style: t.actionButtonStyle || Z,\n        onClick: (a)=>{\n            var f, p;\n            U(t.action) && (a.defaultPrevented || ((p = (f = t.action).onClick) == null || p.call(f, a), L()));\n        },\n        className: T(r == null ? void 0 : r.actionButton, (Pt = t == null ? void 0 : t.classNames) == null ? void 0 : Pt.actionButton)\n    }, t.action.label) : null));\n};\nfunction Ht() {\n    if (true) return \"ltr\";\n    let s = document.documentElement.getAttribute(\"dir\");\n    return s === \"auto\" || !s ? window.getComputedStyle(document.documentElement).direction : s;\n}\nfunction we() {\n    let [s, o] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>v.subscribe((t)=>{\n            o((n)=>{\n                if (\"dismiss\" in t && t.dismiss) return n.filter((u)=>u.id !== t.id);\n                let h = n.findIndex((u)=>u.id === t.id);\n                if (h !== -1) {\n                    let u = [\n                        ...n\n                    ];\n                    return u[h] = {\n                        ...u[h],\n                        ...t\n                    }, u;\n                } else return [\n                    t,\n                    ...n\n                ];\n            });\n        }), []), {\n        toasts: s\n    };\n}\nvar Te = (s)=>{\n    let { invert: o, position: t = \"bottom-right\", hotkey: n = [\n        \"altKey\",\n        \"KeyT\"\n    ], expand: h, closeButton: u, className: g, offset: b, theme: d = \"light\", richColors: q, duration: $, style: V, visibleToasts: Q = qt, toastOptions: i, dir: O = Ht(), gap: K = ee, loadingIcon: Z, icons: tt, containerAriaLabel: et = \"Notifications\", pauseWhenPageIsHidden: X, cn: ot = ne } = s, [w, j] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]), W = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>Array.from(new Set([\n            t\n        ].concat(w.filter((c)=>c.position).map((c)=>c.position)))), [\n        w,\n        t\n    ]), [r, I] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]), [at, k] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [T, z] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [nt, D] = react__WEBPACK_IMPORTED_MODULE_0__.useState(d !== \"system\" ? d :  false ? 0 : \"light\"), H = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null), st = n.join(\"+\").replace(/Key/g, \"\").replace(/Digit/g, \"\"), N = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null), M = react__WEBPACK_IMPORTED_MODULE_0__.useRef(!1), rt = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((c)=>{\n        var m;\n        (m = w.find((y)=>y.id === c.id)) != null && m.delete || v.dismiss(c.id), j((y)=>y.filter(({ id: S })=>S !== c.id));\n    }, [\n        w\n    ]);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>v.subscribe((c)=>{\n            if (c.dismiss) {\n                j((m)=>m.map((y)=>y.id === c.id ? {\n                            ...y,\n                            delete: !0\n                        } : y));\n                return;\n            }\n            setTimeout(()=>{\n                react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync(()=>{\n                    j((m)=>{\n                        let y = m.findIndex((S)=>S.id === c.id);\n                        return y !== -1 ? [\n                            ...m.slice(0, y),\n                            {\n                                ...m[y],\n                                ...c\n                            },\n                            ...m.slice(y + 1)\n                        ] : [\n                            c,\n                            ...m\n                        ];\n                    });\n                });\n            });\n        }), []), react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (d !== \"system\") {\n            D(d);\n            return;\n        }\n        d === \"system\" && (window.matchMedia && window.matchMedia(\"(prefers-color-scheme: dark)\").matches ? D(\"dark\") : D(\"light\")),  false && 0;\n    }, [\n        d\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        w.length <= 1 && k(!1);\n    }, [\n        w\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        let c = (m)=>{\n            var S, A;\n            n.every((l)=>m[l] || m.code === l) && (k(!0), (S = H.current) == null || S.focus()), m.code === \"Escape\" && (document.activeElement === H.current || (A = H.current) != null && A.contains(document.activeElement)) && k(!1);\n        };\n        return document.addEventListener(\"keydown\", c), ()=>document.removeEventListener(\"keydown\", c);\n    }, [\n        n\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (H.current) return ()=>{\n            N.current && (N.current.focus({\n                preventScroll: !0\n            }), N.current = null, M.current = !1);\n        };\n    }, [\n        H.current\n    ]), w.length ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"section\", {\n        \"aria-label\": `${et} ${st}`,\n        tabIndex: -1\n    }, W.map((c, m)=>{\n        var A;\n        let [y, S] = c.split(\"-\");\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"ol\", {\n            key: c,\n            dir: O === \"auto\" ? Ht() : O,\n            tabIndex: -1,\n            ref: H,\n            className: g,\n            \"data-sonner-toaster\": !0,\n            \"data-theme\": nt,\n            \"data-y-position\": y,\n            \"data-x-position\": S,\n            style: {\n                \"--front-toast-height\": `${((A = r[0]) == null ? void 0 : A.height) || 0}px`,\n                \"--offset\": typeof b == \"number\" ? `${b}px` : b || Qt,\n                \"--width\": `${te}px`,\n                \"--gap\": `${K}px`,\n                ...V\n            },\n            onBlur: (l)=>{\n                M.current && !l.currentTarget.contains(l.relatedTarget) && (M.current = !1, N.current && (N.current.focus({\n                    preventScroll: !0\n                }), N.current = null));\n            },\n            onFocus: (l)=>{\n                l.target instanceof HTMLElement && l.target.dataset.dismissible === \"false\" || M.current || (M.current = !0, N.current = l.relatedTarget);\n            },\n            onMouseEnter: ()=>k(!0),\n            onMouseMove: ()=>k(!0),\n            onMouseLeave: ()=>{\n                T || k(!1);\n            },\n            onPointerDown: (l)=>{\n                l.target instanceof HTMLElement && l.target.dataset.dismissible === \"false\" || z(!0);\n            },\n            onPointerUp: ()=>z(!1)\n        }, w.filter((l)=>!l.position && m === 0 || l.position === c).map((l, _)=>{\n            var J, x;\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(se, {\n                key: l.id,\n                icons: tt,\n                index: _,\n                toast: l,\n                defaultRichColors: q,\n                duration: (J = i == null ? void 0 : i.duration) != null ? J : $,\n                className: i == null ? void 0 : i.className,\n                descriptionClassName: i == null ? void 0 : i.descriptionClassName,\n                invert: o,\n                visibleToasts: Q,\n                closeButton: (x = i == null ? void 0 : i.closeButton) != null ? x : u,\n                interacting: T,\n                position: c,\n                style: i == null ? void 0 : i.style,\n                unstyled: i == null ? void 0 : i.unstyled,\n                classNames: i == null ? void 0 : i.classNames,\n                cancelButtonStyle: i == null ? void 0 : i.cancelButtonStyle,\n                actionButtonStyle: i == null ? void 0 : i.actionButtonStyle,\n                removeToast: rt,\n                toasts: w.filter((P)=>P.position == l.position),\n                heights: r.filter((P)=>P.position == l.position),\n                setHeights: I,\n                expandByDefault: h,\n                gap: K,\n                loadingIcon: Z,\n                expanded: at,\n                pauseWhenPageIsHidden: X,\n                cn: ot\n            });\n        }));\n    })) : null;\n};\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/sonner/dist/index.mjs\n");

/***/ })

};
;