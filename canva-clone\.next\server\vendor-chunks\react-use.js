"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-use";
exports.ids = ["vendor-chunks/react-use"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-use/esm/misc/util.js":
/*!*************************************************!*\
  !*** ./node_modules/react-use/esm/misc/util.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isBrowser: () => (/* binding */ isBrowser),\n/* harmony export */   isNavigator: () => (/* binding */ isNavigator),\n/* harmony export */   noop: () => (/* binding */ noop),\n/* harmony export */   off: () => (/* binding */ off),\n/* harmony export */   on: () => (/* binding */ on)\n/* harmony export */ });\nvar noop = function() {};\nfunction on(obj) {\n    var args = [];\n    for(var _i = 1; _i < arguments.length; _i++){\n        args[_i - 1] = arguments[_i];\n    }\n    if (obj && obj.addEventListener) {\n        obj.addEventListener.apply(obj, args);\n    }\n}\nfunction off(obj) {\n    var args = [];\n    for(var _i = 1; _i < arguments.length; _i++){\n        args[_i - 1] = arguments[_i];\n    }\n    if (obj && obj.removeEventListener) {\n        obj.removeEventListener.apply(obj, args);\n    }\n}\nvar isBrowser = \"undefined\" !== \"undefined\";\nvar isNavigator = typeof navigator !== \"undefined\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-use/esm/misc/util.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-use/esm/useEvent.js":
/*!************************************************!*\
  !*** ./node_modules/react-use/esm/useEvent.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _misc_util__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./misc/util */ \"(ssr)/./node_modules/react-use/esm/misc/util.js\");\n\n\nvar defaultTarget = _misc_util__WEBPACK_IMPORTED_MODULE_1__.isBrowser ? window : null;\nvar isListenerType1 = function(target) {\n    return !!target.addEventListener;\n};\nvar isListenerType2 = function(target) {\n    return !!target.on;\n};\nvar useEvent = function(name, handler, target, options) {\n    if (target === void 0) {\n        target = defaultTarget;\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function() {\n        if (!handler) {\n            return;\n        }\n        if (!target) {\n            return;\n        }\n        if (isListenerType1(target)) {\n            (0,_misc_util__WEBPACK_IMPORTED_MODULE_1__.on)(target, name, handler, options);\n        } else if (isListenerType2(target)) {\n            target.on(name, handler, options);\n        }\n        return function() {\n            if (isListenerType1(target)) {\n                (0,_misc_util__WEBPACK_IMPORTED_MODULE_1__.off)(target, name, handler, options);\n            } else if (isListenerType2(target)) {\n                target.off(name, handler, options);\n            }\n        };\n    }, [\n        name,\n        handler,\n        target,\n        JSON.stringify(options)\n    ]);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useEvent);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-use/esm/useEvent.js\n");

/***/ })

};
;